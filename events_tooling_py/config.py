from collections.abc import Callable
from dataclasses import dataclass
import typing

from pika.exchange_type import ExchangeType
from pydantic import BaseModel, ConfigDict, Field

from events_tooling_py.defaults import (
    DEFAULT_DEAD_LETTER_EXCHANGE,
    default_get_exchange_for_entity,
)
from events_tooling_py.types import <PERSON><PERSON><PERSON><PERSON>and<PERSON>, EventBase


@dataclass
class EventPublishingData:
    exchange: str
    routing_key: str


@dataclass
class ServiceConfig:
    def __post_init__(self) -> None:
        self._event_publishing_data_cache: dict[
            type[EventBase], EventPublishingData
        ] = {}

    service_name: str
    get_exchange_for_entity: Callable[[str], str] = default_get_exchange_for_entity
    dead_letter_exchange: str = DEFAULT_DEAD_LETTER_EXCHANGE
    exception_handler: Callable | None = None

    def get_event_publishing_data(
        self, event_schema: type[EventBase]
    ) -> EventPublishingData:
        if event_schema in self._event_publishing_data_cache:
            return self._event_publishing_data_cache[event_schema]
        mv, entity, event, _ = event_schema.Meta.schema_type.split('.')
        if mv != "multiverse":
            raise ValueError("invalid schema type: %s", event_schema.Meta.schema_type)
        result = EventPublishingData(
            exchange=self.get_exchange_for_entity(entity),
            routing_key=f'{entity}.{event}',
        )
        self._event_publishing_data_cache[event_schema] = result
        return result


class BindingConfig(BaseModel):
    """Config for a binding between a queue and an exchange."""

    exchange: str
    routing_key: str


class ArgumentsConfig(BaseModel):
    """Config for additional arguments for queue declaration."""

    x_queue_type: str = Field(..., alias='x-queue-type')
    x_single_active_consumer: bool = Field(..., alias='x-single-active-consumer')
    x_dead_letter_exchange: str | None = Field(
        default=None, alias='x-dead-letter-exchange'
    )
    x_dead_letter_routing_key: str | None = Field(
        default=None, alias='x-dead-letter-routing-key'
    )


class QueueConfig(BaseModel):
    """Config for a queue."""

    name: str
    durable: bool = False
    arguments: ArgumentsConfig | None = None
    bindings: list[BindingConfig] = []
    handler: str | CloudEventHandler | None = None
    auto_ack: bool = False

    # This sets a limit on the number of times a rejected message can be
    # requeued for delivery.  The default is set to 0 so that queues managed
    # with the legacy configuration styles have unchanged behaviour (never
    # requeue a message).
    delivery_limit: int = 0
    model_config = ConfigDict(arbitrary_types_allowed=True)


class ConsumerQueueConfig(BaseModel):
    """Config for a consumer queue and dead letter queue."""

    dlq: QueueConfig | None = None
    queue: QueueConfig

    def get_consumer_queue_config(
        self, service_config: ServiceConfig
    ) -> "ConsumerQueueConfig":
        return self


@typing.runtime_checkable
class ConsumerQueueConfigProvider(typing.Protocol):
    def get_consumer_queue_config(
        self, service_config: ServiceConfig
    ) -> ConsumerQueueConfig: ...


if typing.TYPE_CHECKING:
    _: type[ConsumerQueueConfigProvider] = ConsumerQueueConfig


class ConsumerConfig(BaseModel):
    """Base config for event consumers."""

    exception_handler: Callable | None = None
    consumers: list[ConsumerQueueConfig] = []


class PublisherExchangeConfig(BaseModel):
    """Config for a publisher exchange."""

    exchange: str
    exchange_type: ExchangeType
    durable: bool


class PublisherConfig(BaseModel):
    """Base config for event publishers."""

    exception_handler: Callable | None = None
    exchanges: list[PublisherExchangeConfig] | None = []


class RabbitMQConfig(BaseModel):
    """Config for rabbitmq connection class."""

    url: str
    reconnect_attempts: int | None = 5
