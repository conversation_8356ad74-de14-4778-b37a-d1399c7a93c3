from collections.abc import Callable
from inspect import signature
import logging
import typing

from events_tooling_py.config import (
    ArgumentsConfig,
    BindingConfig,
    ConsumerQueueConfig,
    ConsumerQueueConfigProvider,
    QueueConfig,
    ServiceConfig,
)
from events_tooling_py.types import (
    CloudEventHandler,
    CloudEventProtocol,
    EventBase,
    EventContext,
    EventHandler,
    EventMetadata,
    FullCloudEventHandler,
    InvalidEventPayloadError,
    UnhandledEventTypeError,
)

logger = logging.getLogger(__name__)


class DefaultHandlerAlreadyRegisteredError(Exception):
    pass


class EventHandlerAlreadyRegisteredError(Exception):
    pass


class EventDispatcherConfig(typing.TypedDict, total=False):
    dlq_name: str
    dlq_routing_key: str
    queue_name: str


DEFAULT_EVENT_DISPATCHER_CONFIG = EventDispatcherConfig(
    dlq_name='{service_config.service_name}.{entity_name}_events.dead_letter_queue',
    # The dlq used to end in `.dead_letter.queue` and the routing key was set to
    # the same.  It is not possible to change the DLQ routing key without
    # recreating the consumer queue, so for now we keep that routing key.
    dlq_routing_key='{service_config.service_name}.{entity_name}_events.dead_letter.queue',
    queue_name='{service_config.service_name}.{entity_name}_events.queue',
)


def convert_cloudevent_handler(handler: CloudEventHandler) -> FullCloudEventHandler:
    sig = signature(handler)
    if len(sig.parameters) == 1:

        def _handler(event: CloudEventProtocol, metadata: EventMetadata) -> None:
            handler(event)  # type: ignore[call-arg]
    else:
        _handler = handler  # type: ignore[assignment]
    return _handler


class EventDispatcher:
    '''
    An event handler which can dispatch events according to its type, e.g.

        dispatcher = EventDispatcher()

        @dispatcher.handle('multiverse.user.competencies_attained.v1')
        def handle_user_competencies_attained_v1(event: CloudEventSchema):
           ...

        @dispatcher.handle('multiverse.user.competencies_attained.v2')
        def handle_user_competencies_attained_v1(event: CloudEventSchema):
           ...

        # The dispatcher can be registered as an event handler with the EventConsumer

    '''

    _handlers: dict[str, FullCloudEventHandler]
    _default_handler: FullCloudEventHandler | None

    def __init__(self, **overrides: "typing.Unpack[EventDispatcherConfig]") -> None:
        self._handlers = {}
        self._default_handler = None
        self._config_overrides = overrides

    def __call__(self, event: CloudEventProtocol, metadata: EventMetadata) -> None:
        '''
        Handle an event by dispatching it to the relevant handler
        '''
        handler = self._handlers.get(event.type, self._default_handler)
        if handler is None:
            logger.warn("Unhandled event of type '%s'", event.type)
            raise UnhandledEventTypeError(event.type)
        else:
            handler(event, metadata)

    def handle(
        self, event_type: str
    ) -> Callable[[CloudEventHandler], CloudEventHandler]:
        '''
        Register a handler for event_type, use as a decorator
        '''

        def handler_decorator(handler: CloudEventHandler) -> CloudEventHandler:
            if event_type in self._handlers:
                raise EventHandlerAlreadyRegisteredError(event_type)

            self._handlers[event_type] = convert_cloudevent_handler(handler)
            return handler

        return handler_decorator

    def handle_default(self, handler: CloudEventHandler) -> CloudEventHandler:
        '''
        Register a default handler, use as a decorator
        '''

        if self._default_handler is not None:
            raise DefaultHandlerAlreadyRegisteredError()
        self._default_handler = convert_cloudevent_handler(handler)
        return handler

    def register_handler_for_type[T: EventBase](
        self, event_cls: type[T]
    ) -> Callable[[EventHandler[T]], EventHandler[T]]:
        event_type = event_cls.Meta.schema_type

        def handler_decorator(handler: EventHandler[T]) -> EventHandler[T]:
            sig = signature(handler)
            if len(sig.parameters) == 1:

                def handler_wrapper(
                    envelope: CloudEventProtocol, metadata: EventMetadata
                ) -> None:
                    try:
                        event = event_cls(**envelope.data)
                    except Exception as e:
                        raise InvalidEventPayloadError() from e
                    else:
                        handler(event)  # type: ignore[call-arg]
            else:

                def handler_wrapper(
                    envelope: CloudEventProtocol, metadata: EventMetadata
                ) -> None:
                    try:
                        event = event_cls(**envelope.data)
                    except Exception as e:
                        raise InvalidEventPayloadError() from e
                    else:
                        ctx = EventContext(envelope=envelope, metadata=metadata)
                        handler(event, ctx)  # type: ignore[call-arg]

            self.handle(event_type)(handler_wrapper)
            return handler

        return handler_decorator

    def register_handler[T: EventBase](
        self, handler: EventHandler[T]
    ) -> EventHandler[T]:
        sig = signature(handler)
        param = next(iter(sig.parameters.values()))
        return self.register_handler_for_type(param.annotation)(handler)

    def _get_config(
        self, key: typing.Literal['dlq_name', 'dlq_routing_key', 'queue_name']
    ) -> str:
        if key in self._config_overrides:
            return self._config_overrides[key]
        return DEFAULT_EVENT_DISPATCHER_CONFIG[key]

    def get_consumer_queue_config(
        self, service_config: ServiceConfig
    ) -> ConsumerQueueConfig:
        entity_name = None
        known_routing_keys: set[str] = set()
        bindings: list[BindingConfig] = []

        for event_type in self._handlers:
            mv, event_entity, event, _ = event_type.split('.')
            if mv != "multiverse":
                raise ValueError("Only 'multiverse.'-prefixed events can be handled")
            if entity_name is None:
                entity_name = event_entity
            elif event_entity != entity_name:
                raise ValueError(
                    "cannot generate config for dispatcher with several entities"
                )
            routing_key = f"{event_entity}.{event}"
            if routing_key not in known_routing_keys:
                known_routing_keys.add(routing_key)
                bindings.append(
                    BindingConfig(
                        exchange=service_config.get_exchange_for_entity(entity_name),
                        routing_key=routing_key,
                    )
                )

        dlq_name = self._get_config('dlq_name').format(
            service_config=service_config, entity_name=entity_name
        )
        dlq_routing_key = self._get_config('dlq_routing_key').format(
            service_config=service_config, entity_name=entity_name
        )
        queue_name = self._get_config('queue_name').format(
            service_config=service_config, entity_name=entity_name
        )

        dlq = QueueConfig(
            name=dlq_name,
            durable=True,
            bindings=[
                BindingConfig(
                    exchange=service_config.dead_letter_exchange,
                    routing_key=dlq_routing_key,
                )
            ],
        )
        queue = QueueConfig(
            name=queue_name,
            durable=True,
            arguments=ArgumentsConfig(**{  # type: ignore
                "x-queue-type": 'quorum',
                "x-single-active-consumer": True,
                "x-dead-letter-exchange": service_config.dead_letter_exchange,
                "x-dead-letter-routing-key": dlq_routing_key,
            }),
            bindings=bindings,
            handler=self,
            delivery_limit=5,
        )
        return ConsumerQueueConfig(dlq=dlq, queue=queue)


if typing.TYPE_CHECKING:
    _: type[ConsumerQueueConfigProvider] = EventDispatcher

    # This is to check types work out

    class SampleV1Event:
        class Meta:
            schema_type = "multiverse.sample.v1"
            schema_uri = "https://schema.multiverse.io/sample/v1"
            raw_json_schema = {
                "$id": "https://schema.multiverse.io/sample/v1",
                "type": "object",
                "properties": {},
                "required": [],
            }
            non_nullable_optionals: dict[str, list[str]] = {}

    class SampleV2Event:
        class Meta:
            schema_type = "multiverse.sample.v1"
            schema_uri = "https://schema.multiverse.io/sample/v2"
            raw_json_schema = {
                "$id": "https://schema.multiverse.io/sample/v2",
                "type": "object",
                "properties": {},
                "required": [],
            }
            non_nullable_optionals: dict[str, list[str]] = {}

    dispatcher = EventDispatcher()

    _1: type[EventBase] = SampleV1Event
    _2: type[EventBase] = SampleV2Event

    @dispatcher.register_handler_for_type(SampleV1Event)
    def h1(event: SampleV1Event) -> None:
        pass

    @dispatcher.register_handler
    def h2(event: SampleV2Event, ctx: EventContext) -> None:
        pass
