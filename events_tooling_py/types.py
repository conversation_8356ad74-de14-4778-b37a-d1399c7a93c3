from collections.abc import Callable
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Literal, Protocol, runtime_checkable
import uuid


@runtime_checkable
class CloudEventProtocol(Protocol):
    id: uuid.UUID
    source: str
    specversion: str
    type: str
    datacontenttype: str
    dataschema: str
    data: Any
    subject: str | None
    time: datetime | None

    def __init__(self, **kwargs: Any) -> None: ...


DictAny = dict[str, Any]
RabbitMQMessage = DictAny

type FullCloudEventHandler = Callable[[CloudEventProtocol, EventMetadata], None]
type CloudEventHandler = Callable[[CloudEventProtocol], None] | FullCloudEventHandler


class EventBase(Protocol):
    class Meta(Protocol):
        schema_type: str
        schema_uri: str
        raw_json_schema: dict[str, Any]
        non_nullable_optionals: dict[str, Any] = {}


@dataclass
class EventMetadata:
    content_type: str | None = None
    app_id: str | None = None
    timestamp: int | None = None
    schema: str | None = None
    entity_type: str | None = None
    entity_id: str | None = None
    timestamp_microseconds: int | None = None
    log_type: (
        Literal['CREATE', 'UPDATE', 'DELETE', 'BACKFILL', 'INVALIDATE_HISTORY'] | None
    ) = None
    partition_key: str | None = None


@dataclass
class EventContext:
    envelope: CloudEventProtocol
    metadata: EventMetadata


type EventHandler[T: EventBase] = (
    Callable[[T], None] | Callable[[T, EventContext], None]
)


class NonRecoverableEventError(Exception):
    '''
    An error due to an event being malformed or otherwise impossible to handle.
    Application code may raise such an error to signal to the library that it
    should not requeue the event and instead send it straight to the DLQ.
    '''

    pass


class UnhandledEventTypeError(NonRecoverableEventError):
    '''
    An error due to an event type being unhandled.
    '''

    pass


class InvalidEventPayloadError(NonRecoverableEventError):
    '''
    An error due to an event payload being invalid.
    '''

    pass


class MessagingError(Exception):
    '''
    An error caused by the messaging system that could not be recovered from.
    '''

    pass
