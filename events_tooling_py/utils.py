from collections.abc import Callable
from functools import reduce
from importlib import import_module
import json
import logging
import sys
from typing import Any

from events_tooling_py.cloudevent import CloudEventSchema
from events_tooling_py.types import CloudEventProtocol, RabbitMQMessage

KEYS_TO_SCRUB = {
    'name',
    'first_name',
    'last_name',
    'preferred_first_name',
    'pronouns',
    'email',
    'mobile_number',
    'phone_number',
    'image',
    'bio',
}


LEGACY_DATASCHEMA = "https://schema.multiverse.io/legacy"


def cached_import(module_path: str, class_name: str) -> Any:
    """This is cut directly from Django's `django.utils.module_loading` module.
    The reason for this is that we want to avoid having Django as a dependency"""
    # Check whether module is loaded and fully initialized.
    if not (
        (module := sys.modules.get(module_path))
        and (spec := getattr(module, "__spec__", None))
        and getattr(spec, "_initializing", False) is False
    ):
        module = import_module(module_path)
    return getattr(module, class_name)


def import_string(dotted_path: str) -> Any:
    """This is cut directly from Django's `django.utils.module_loading` module.
    The reason for this is that we want to avoid having Django as a dependency

    Import a dotted module path and return the attribute/class designated by the
    last name in the path. Raise ImportError if the import failed.
    """
    try:
        module_path, class_name = dotted_path.rsplit(".", 1)
    except ValueError as err:
        raise ImportError(f"{dotted_path} doesn't look like a module path") from err

    try:
        return cached_import(module_path, class_name)
    except AttributeError as err:
        raise ImportError(
            f'Module "{module_path}" does not define a "{class_name}" attribute/class'
        ) from err


def scrub_pii(data: RabbitMQMessage | list[Any]) -> RabbitMQMessage | list[Any]:
    """Redacts sensitive user information (PII) from a message."""
    if isinstance(data, dict):
        return {
            k: ("<REDACTED>" if k in KEYS_TO_SCRUB else scrub_pii(v))
            for k, v in data.items()
        }
    elif isinstance(data, list):
        return [scrub_pii(item) for item in data]
    else:
        return data


def format_message(message: RabbitMQMessage | CloudEventProtocol | str) -> str:
    if isinstance(message, str):
        scrubbed_message = scrub_pii(json.loads(message))
    elif isinstance(message, CloudEventProtocol):
        scrubbed_message = scrub_pii(json.loads(dump_cloudevent_json(message)))
    else:
        scrubbed_message = scrub_pii(message)
    return json.dumps(scrubbed_message)


def dump_cloudevent_json(event: CloudEventProtocol) -> str:
    if event.dataschema == LEGACY_DATASCHEMA:
        return event.data if isinstance(event.data, str) else json.dumps(event.data)
    # This is a temporary measure to make sure we don't serialize unset
    # fields in the payload, while serializing all the fields in the
    # CloudEventSchema envelope.
    event = CloudEventSchema(
        id=event.id,
        source=event.source,
        specversion=event.specversion,
        type=event.type,
        datacontenttype=event.datacontenttype,
        dataschema=event.dataschema,
        data=event.data,
        subject=event.subject,
        time=event.time,
    )
    # Now all fields are explicily set in the envelope.
    return event.model_dump_json(exclude_unset=True)


def legacy_cloud_event(message: Any, type: str | None = None) -> CloudEventProtocol:
    return CloudEventSchema(
        type=type or 'multiverse.legacy',
        dataschema=LEGACY_DATASCHEMA,
        source='',
        data=message,
    )


JSON_DATA_TYPES = set(["string", "number", "boolean", "object", "array", "null"])


def extract_types(schema: dict) -> set[str]:
    """
    Extracts type keywords from a JSON schema.
    """

    def parse(_type: str | list[str]) -> set[str]:
        match _type:
            case list():
                return set(_type)
            case str():
                return set([_type])
            case _:
                return set()

    def subschemas(types: set[str], subschema: dict) -> set[str]:
        return types | extract_types(subschema)

    types = (
        parse(schema.get("type", []))
        | reduce(subschemas, schema.get("allOf", []), set())  # type: ignore[arg-type]
        | reduce(subschemas, schema.get("oneOf", []), set())  # type: ignore[arg-type]
        | reduce(subschemas, schema.get("anyOf", []), set())  # type: ignore[arg-type]
    ) or JSON_DATA_TYPES

    return types - reduce(subschemas, schema.get("not", []), set())  # type: ignore[arg-type]


def is_nullable(prop: dict) -> bool:
    """
    Checks if a JSON schema property allows null values.

    Args:
        prop (dict): A dictionary representing a JSON schema property.

    Returns:
        bool: True if the property is nullable, False otherwise.
              A property is considered nullable if its 'type' is explicitly 'null',
              or if 'type' is a list containing 'null', or if the 'type' key is missing
              (which defaults to allowing any type, including null).
    """
    return "null" in extract_types(prop)


def find_non_nullable_optionals(schema: dict, required: set[str] | None = None) -> dict:
    """
    Recursively finds all non-nullable, optional fields within a JSON schema.

    Args:
        schema (dict): A dictionary representing a JSON schema.
        required (set, optional): A set of field names that are currently considered required
                                   in the current scope. Defaults to an empty set.

    Returns:
        dict: A dictionary where keys are the names of non-nullable optional fields,
              and values are either True (for simple properties) or a nested dictionary
              representing non-nullable optionals within complex properties (objects or arrays).
    """
    optional: dict = {}

    _required: set[str] = set()

    if required is not None:
        _required = required

    # Update the set of required fields with any 'required' fields defined in the current schema.
    _required = _required | set(schema.get("required", {}))

    # A TypeAlias for the output (required, optional) of processing subschemas.
    type S = tuple[set[str], dict]

    def process_subschema(subschema: dict) -> S:
        """
        Returns:
            tuple[set, dict]: A tuple containing a set of required fields and a dictionary
                             of non-nullable optional fields found in the subschema.
        """
        return (
            set(subschema.get("required", {})),
            find_non_nullable_optionals(subschema, _required),
        )

    # Reducer function for 'allOf' keyword. It merges the required sets and optional dictionaries from all subschemas.
    def union(x: S, y: S) -> S:
        return (x[0] | y[0], x[1] | y[1])

    # Reducer function for 'anyOf' and 'oneOf' keywords. It intersects the required sets and merges
    # the optional dictionaries from all subschemas.
    def intersect(x: S, y: S) -> S:
        return (x[0] & y[0], x[1] | y[1])

    # A TypeAlias for functions reducing the outputs of subschema processing
    type Reducer = Callable[[S, S], S]

    def process_subschemas(reducer: Reducer, subschemas: list[dict]) -> S:
        sub_required, sub_optional = reduce(reducer, map(process_subschema, subschemas))
        return (_required | sub_required, optional | sub_optional)

    # Handle 'allOf' keyword: all subschemas must be valid.
    match schema:
        case {"allOf": subschemas} if len(subschemas) > 0:
            _required, optional = process_subschemas(union, subschemas)

    # Handle 'anyOf' keyword: at least one subschema must be valid.
    match schema:
        case {"anyOf": subschemas} if len(subschemas) > 0:
            _required, optional = process_subschemas(intersect, subschemas)

    # Handle 'oneOf' keyword: exactly one subschema must be valid.
    match schema:
        case {"oneOf": subschemas} if len(subschemas) > 0:
            _required, optional = process_subschemas(intersect, subschemas)

    # Helper function to handle complex properties (objects and arrays).
    def handle_complex(key: str, value: dict) -> dict:
        """
        Handles complex properties (objects or arrays) by recursively finding
        non-nullable optionals within their definitions.

        Args:
            k (str): The key (name) of the property.
            item (dict): The schema definition of the property's items (for arrays) or the property itself (for objects).

        Returns:
            dict: A dictionary containing the non-nullable optionals found within the complex property,
                  or a dictionary indicating the property itself is a non-nullable optional if no
                  nested non-nullable optionals are found and the property is not required.
        """
        nested = find_non_nullable_optionals(value)
        # If nested non-nullable optionals are found, return them under the current key.
        if nested != {}:
            return {key: nested}
        # If no nested optionals and the current property is not required, mark it as a non-nullable optional.
        elif key not in _required:
            return {key: True}
        # Otherwise, the property is either required or has no nested non-nullable optionals.
        else:
            return {}

    # Iterate through the properties defined in the current schema.
    for k, prop in schema.get("properties", {}).items():
        match prop:
            # If the property is an array, process its items.
            case {"type": "array"}:
                optional.update(handle_complex(k, prop["items"]))
            # If the property is an object, process it recursively.
            case {"type": "object"}:
                optional.update(handle_complex(k, prop))
            # If the property is not required and is not nullable, add it to the optional dictionary.
            case _ if k not in _required and not is_nullable(prop):
                optional.update({k: True})

    return optional


def validate_against_schema(
    event: Any, schema: dict[str, Any]
) -> tuple[bool, list[str]]:
    """
    Validate an event against a JSON schema.

    Args:
        event: The event to validate (must be a Pydantic model)
        schema: The JSON schema to validate against

    Returns:
        A tuple of (is_valid, errors)

    Raises:
        SchemaError: If there is an error in the schema itself
    """
    import os
    logger = logging.getLogger(__name__)

    if os.environ.get('PYTEST_CURRENT_TEST') is not None:
        logger.info("DEBUG: validate_against_schema - Starting")

        try:
            logger.info("DEBUG: validate_against_schema - About to import jsonschema")
            from jsonschema import validate, ValidationError
            logger.info("DEBUG: validate_against_schema - jsonschema imported successfully")

            logger.info("DEBUG: validate_against_schema - About to call event.model_dump()")
            event_dict = event.model_dump()
            logger.info(f"DEBUG: validate_against_schema - model_dump() successful: {event_dict}")

            logger.info("DEBUG: validate_against_schema - About to call jsonschema.validate()")
            validate(instance=event_dict, schema=schema)
            logger.info("DEBUG: validate_against_schema - jsonschema.validate() completed successfully")

            return True, []
        except ValidationError as e:
            logger.info(f"DEBUG: validate_against_schema - ValidationError: {e}")
            return False, [str(e)]
        except Exception as e:
            logger.error(f"DEBUG: validate_against_schema - Unexpected error: {e}")
            import traceback
            logger.error(f"DEBUG: validate_against_schema - Traceback: {traceback.format_exc()}")
            raise
    else:
        # Normal production path
        from jsonschema import validate, ValidationError

        # Convert the event to a dict
        event_dict = event.model_dump()

        try:
            validate(instance=event_dict, schema=schema)
            return True, []
        except ValidationError as e:
            return False, [str(e)]


def validate_event_against_schema(
    event: Any, schema: dict[str, Any], service_name: str | None = None
) -> None:
    """
    Validate an event against its JSON schema and raise an error with detailed info if validation fails.

    Args:
        event: The event to validate
        schema: The JSON schema to validate against
        service_name: Optional name of the service for error reporting

    Raises:
        ValueError: If validation fails
    """
    # Skip validation if the event doesn't have a schema
    if not schema:
        return

    # Add detailed debugging for test environments
    import os
    logger = logging.getLogger(__name__)

    if os.environ.get('PYTEST_CURRENT_TEST') is not None:
        logger.info("DEBUG: Starting validation in test environment")

        # Test each step individually to find where it fails
        try:
            logger.info("DEBUG: Step 1 - About to call validate_against_schema")
            is_valid, errors = validate_against_schema(event, schema)
            logger.info(f"DEBUG: Step 2 - validate_against_schema returned: is_valid={is_valid}, errors={errors}")

            if is_valid:
                logger.info("DEBUG: Step 3 - Validation passed, returning")
                return
            else:
                logger.info("DEBUG: Step 4 - Validation failed, proceeding to error handling")
        except Exception as e:
            logger.error(f"DEBUG: Exception in validate_against_schema: {e}")
            import traceback
            logger.error(f"DEBUG: Traceback: {traceback.format_exc()}")
            raise
    else:
        # Normal production validation
        is_valid, errors = validate_against_schema(event, schema)
        if is_valid:
            return

    # Handle validation failure
    error_message = f"Event validation failed: {'; '.join(errors)}"

    # Create a structured log entry with all relevant information for alerting
    alert_data: dict[str, Any] = {
        "event_type": event.Meta.schema_type,
        "schema_uri": event.Meta.schema_uri,
        "validation_errors": errors,
        "service": service_name or "unknown",
        "alert_type": "schema_validation_failure",
        "severity": "error",
    }

    # Add a sanitized version of the event to the alert data
    try:
        event_dict = event.model_dump()
        alert_data["event_data"] = scrub_pii(event_dict)
    except Exception as e:
        alert_data["event_serialization_error"] = str(e)

    # Log the validation error with CRITICAL level to ensure it triggers alerts
    logger = logging.getLogger(__name__)
    try:
        logger.critical(
            "ALERT: Schema validation failed for event %s: %s",
            event.Meta.schema_type,
            error_message,
            extra=alert_data,
        )
    except Exception:
        # If logging fails (e.g., in test environments), continue without logging
        # This ensures validation errors don't prevent the exception from being raised
        pass

    # Raise an exception to prevent publishing
    raise ValueError(error_message)
