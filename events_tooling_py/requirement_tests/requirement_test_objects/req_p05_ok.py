#!/usr/bin/env python3

import logging
import time

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p05")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Create a single publisher
    publisher = get_publisher("/test_p05", [ChangelogSchema])
    publisher.setup()

    print("started")
    assert input() == "ready"

    message_count = 0
    start_time = time.time()

    # Run for at least 6 seconds
    while time.time() - start_time < 6:
        # Create and publish event
        event = ChangelogSchema(
            id="123",
            name="test",
        )

        publisher.publish(event)

        message_count += 1
        time.sleep(0.001)  # Small delay to prevent overwhelming the system


if __name__ == '__main__':
    main()
