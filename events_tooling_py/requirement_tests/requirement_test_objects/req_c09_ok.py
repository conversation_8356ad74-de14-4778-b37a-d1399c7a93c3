#!/usr/bin/env -S python -u

import logging
import time

from event_test_schemas.foo.changelog.v1 import ChangelogSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c09")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Create dispatcher
    dispatcher = EventDispatcher()

    # Register handler that does 1ms of work before acknowledging
    @dispatcher.register_handler
    def handle_changelog(event: ChangelogSchema) -> None:
        # Do some busy work for 1ms
        time.sleep(0.001)
        # Print the event data as required by the test
        print(event.model_dump())

    # Create consumer with the dispatcher
    consumer = get_consumer("test_c09", [dispatcher])

    # Setup consumer but don't start consuming yet
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    # Notify test framework we're ready
    print("started")

    # Wait for signal to start consuming
    assert input() == "consume"

    # Start consuming messages
    consumer.start_consuming()


if __name__ == '__main__':
    main()
