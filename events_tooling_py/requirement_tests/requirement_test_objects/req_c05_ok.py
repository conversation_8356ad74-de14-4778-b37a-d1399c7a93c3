#!/usr/bin/env -S python -u

import logging

from event_test_schemas.foo.changelog.v1 import ChangelogSchema as ChangelogSchemaV1
from event_test_schemas.foo.changelog.v2 import ChangelogSchema as ChangelogSchemaV2
from event_test_schemas.foo.deleted.v1 import DeletedSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c05")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Create dispatcher to handle multiple event types
    dispatcher = EventDispatcher()

    # Register handlers for all three event types
    @dispatcher.register_handler
    def handle_changelog_v1(event: ChangelogSchemaV1) -> None:
        pass

    @dispatcher.register_handler
    def handle_changelog_v2(event: ChangelogSchemaV2) -> None:
        pass

    @dispatcher.register_handler
    def handle_deleted(event: DeletedSchema) -> None:
        pass

    # Create consumer with the dispatcher that has multiple bindings
    consumer = get_consumer("test_c05", [dispatcher])

    # Setup consumer with dead-letter-only exchanges
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("started")


if __name__ == '__main__':
    main()
