#!/usr/bin/env -S python -u


import json
import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema
from pydantic import ValidationError

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p02")


def main() -> None:
    logging.basicConfig(level=logging.INFO)
    publisher = get_publisher("/test_p02", [ChangelogSchema])
    publisher.setup()

    print("ready")

    while True:
        line = input()
        if not line:
            return

        payload = json.loads(line)

        try:
            event = ChangelogSchema(**payload)
            publisher.publish(event)
            print("message_published")
        except ValidationError:
            print("validation_error_raised")


if __name__ == '__main__':
    main()
