#!/usr/bin/env -S python -u

import logging
import uuid

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p06")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Initialize publisher with the foo changelog schema
    publisher = get_publisher(
        "/test_p06",
        [ChangelogSchema],
    )

    publisher.setup()

    print("started")

    assert input() == "ready"

    # Create a sample event with minimal data to match framework
    entity_id = str(uuid.uuid4())

    #  Create event to match framework's empty JSON payload
    event = ChangelogSchema(
        id=entity_id,
        name="Test Entity",
    )

    # The test will fail because the headers and properties are missing.
    # We should fix this in the library (event_tooling_py) and then fix the test.
    # It is the library's job to fill in the correct metadata / headers / properties
    publisher.publish(event)

    print("event sent")

    input()


if __name__ == '__main__':
    main()
