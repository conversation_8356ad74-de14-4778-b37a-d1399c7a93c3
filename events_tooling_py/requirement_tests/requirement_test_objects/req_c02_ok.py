#!/usr/bin/env -S python -u


import logging
import time

from event_test_schemas.bar.changelog.v1 import ChangelogSchema as BarChangelog
from event_test_schemas.baz.changelog.v1 import ChangelogSchema as BazChangelog
from event_test_schemas.foo.changelog.v1 import ChangelogSchema as FooChangelog

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c_02")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    foo_dispatcher = EventDispatcher()

    @foo_dispatcher.register_handler
    def handle_foo_changelog(event: FooChangelog) -> None:
        time.sleep(0.02)
        # Ack happens automatically on success

    bar_dispatcher = EventDispatcher()

    @bar_dispatcher.register_handler
    def handle_bar_changelog(event: BarChangelog) -> None:
        time.sleep(0.02)
        # Ack happens automatically on success

    baz_dispatcher = EventDispatcher()

    @baz_dispatcher.register_handler
    def handle_baz_changelog(event: BazChangelog) -> None:
        time.sleep(0.02)
        # Ack happens automatically on success

    consumer = get_consumer(
        "test_c02", [foo_dispatcher, bar_dispatcher, baz_dispatcher]
    )
    logger.info(consumer.consumers)
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("ready")

    assert input() == "consume"

    consumer.start_consuming()


if __name__ == '__main__':
    main()
