#!/usr/bin/env -S python -u

import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p04")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    publishers = []  # Keep references to publishers

    # Create three different publishers with the same connection
    publishers.append(get_publisher("/test_p04_01", [ChangelogSchema]))
    publishers.append(get_publisher("/test_p04_02", [ChangelogSchema]))
    publishers.append(get_publisher("/test_p04_03", [ChangelogSchema]))

    # Create three channels, one for each publisher
    for publisher in publishers:
        publisher.setup()

    print("started")

    # Keep publishers alive until verification
    assert input() == "finished"


if __name__ == '__main__':
    main()
