#!/usr/bin/env -S python -u


import logging

from event_test_schemas.foo.changelog.v1 import ChangelogSchema as ChangelogV1
from event_test_schemas.foo.count.v1 import CountSchema as CountV1
from event_test_schemas.foo.deleted.v1 import DeletedSchema as DeletedV1

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c_11")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    dispatcher = EventDispatcher()

    @dispatcher.register_handler
    def handle_changelog_v1(event: ChangelogV1) -> None:
        print(event.Meta.schema_type)

    @dispatcher.register_handler
    def handle_deleted_v1(event: DeletedV1) -> None:
        print(event.Meta.schema_type)

    @dispatcher.register_handler
    def handle_count_v1(event: CountV1) -> None:
        print(event.Meta.schema_type)

    consumer = get_consumer("test_c11", [dispatcher])
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("started")

    consumer.start_consuming()


if __name__ == '__main__':
    main()
