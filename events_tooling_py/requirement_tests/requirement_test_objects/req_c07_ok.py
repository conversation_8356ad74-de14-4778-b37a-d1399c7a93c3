#!/usr/bin/env -S python -u

from datetime import datetime
import json
import logging
import time

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)
from events_tooling_py.types import CloudEventProtocol

logger = logging.getLogger("test_c07")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Create dispatcher to handle changelog events
    dispatcher = EventDispatcher()

    @dispatcher.handle('multiverse.foo.changelog.v1')
    def handle_message(event: CloudEventProtocol) -> None:
        # Log message with timestamp (truncate microseconds to 3 digits)
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"Received message ({timestamp}): {json.dumps(event.data)}")

        # Rate limit to 2 messages per second
        time.sleep(0.5)

    # Create consumer with the dispatcher
    consumer = get_consumer("test_c07", [dispatcher])
    logger.info(consumer.consumers)

    # Setup consumer with dead-letter-only exchanges
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("started")

    # Wait for ready signal and start consuming
    assert input() == "ready"

    consumer.start_consuming()


if __name__ == '__main__':
    main()
