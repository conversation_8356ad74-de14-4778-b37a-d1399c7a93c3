#!/usr/bin/env -S python -u

import logging

from event_test_schemas.bar.changelog.v1 import ChangelogSchema as BarChangelogSchema
from event_test_schemas.foo.changelog.v1 import ChangelogSchema as FooChangelogSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

# This is a failing test because currently all consumers share the same connection
# and channel. So when one consumer is stopped, the other consumers are also stopped.
# It is therefore not currently possible to stop a single consumer - so this needs fixing.

logger = logging.getLogger("test_c08")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    foo_dispatcher = EventDispatcher()
    bar_dispatcher = EventDispatcher()

    @foo_dispatcher.register_handler
    def handle_foo_changelog(_: FooChangelogSchema) -> None:
        pass

    @bar_dispatcher.register_handler
    def handle_bar_changelog(_: BarChangelogSchema) -> None:
        pass

    consumer = get_consumer("test_c08", [foo_dispatcher, bar_dispatcher])
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("ready")

    assert input() == "stop consumer 1"
    consumer.rabbitmq.close()  # there is currently no way to stop a single consumer
    print("consumer 1 stopped")

    assert input() == "restart consumer 1"
    consumer = get_consumer("test_c08", [foo_dispatcher, bar_dispatcher])
    consumer.setup(start_consuming=False)


if __name__ == '__main__':
    main()
