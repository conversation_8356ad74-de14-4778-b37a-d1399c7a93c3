#!/usr/bin/env -S python -u

import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p01")


def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    logger.info("Getting publisher...")
    publisher = get_publisher("/test_p01", [ChangelogSchema])
    logger.info("Setting up publisher...")
    publisher.setup()

    print("started")

    assert input() == "ready"

    logger.info("Publishing message...")
    publisher.publish(ChangelogSchema(id="123", name="hello"))

    print("event sent")


if __name__ == '__main__':
    main()
