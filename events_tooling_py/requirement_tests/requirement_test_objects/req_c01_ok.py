#!/usr/bin/env -S python -u


import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c_01")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    dispatcher = EventDispatcher()

    @dispatcher.register_handler
    def handle_changelog(event: ChangelogSchema) -> None:
        pass

    consumer = get_consumer("test_c01", [dispatcher])
    logger.info(consumer.consumers)
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("ready")


if __name__ == '__main__':
    main()
