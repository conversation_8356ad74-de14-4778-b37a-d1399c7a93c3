#!/usr/bin/env -S python -u


import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p03")


def main() -> None:
    logging.basicConfig(level=logging.INFO)
    publisher = get_publisher("/test_p03", [ChangelogSchema])
    publisher.setup()

    print("ready")


if __name__ == '__main__':
    main()
