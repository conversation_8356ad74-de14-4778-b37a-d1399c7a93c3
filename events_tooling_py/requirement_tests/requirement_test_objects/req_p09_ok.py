#!/usr/bin/env -S python -u


import logging

from event_test_schemas.foo.count.v1 import CountSchema, encode_cloudevent

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p09")


def main() -> None:
    logging.basicConfig(level=logging.DEBUG)

    publisher = get_publisher("/test_p09", [CountSchema])
    publisher.setup()

    print("started")

    assert input() == "ready"

    for i in range(10):
        event = CountSchema(id=str(i), count=i)
        publisher.publish_to_exchange(
            exchange="",
            routing_key="test_p09.backfill_events.queue",
            event=encode_cloudevent(event, source="/test_p09"),
        )

    print("events sent")


if __name__ == '__main__':
    main()
