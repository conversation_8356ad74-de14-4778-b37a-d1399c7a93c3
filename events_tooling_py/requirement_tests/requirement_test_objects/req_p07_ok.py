#!/usr/bin/env -S python -u

import logging
import uuid

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p07")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Get publisher with retry capabilities
    publisher = get_publisher("/test_p07", [ChangelogSchema])
    publisher.setup()

    print("started")

    # Wait for ready signal before publishing first message
    print("waiting for ready signal...")
    ready_input = input()
    print(f"received input: {ready_input}")
    assert ready_input == "ready"

    # First message
    event_obj = ChangelogSchema(id=str(uuid.uuid4()), name="test_event")

    # Use the proper publish method which includes schema validation
    # Override the exchange and routing key to match test expectations
    try:
        # Temporarily use the same pattern as P01 to test
        publisher.publish(event_obj)
        print("first message published")
    except Exception as e:
        print(f"Error publishing first message: {e}")
        raise

    # Wait for signal to publish again (connection will be closed externally)
    assert input() == "publish_again"

    # Second message - should auto-reconnect
    event_obj = ChangelogSchema(id=str(uuid.uuid4()), name="test_event")

    # Use the proper publish method which includes schema validation
    try:
        # Temporarily use the same pattern as P01 to test
        publisher.publish(event_obj)
        print("second message published")
    except Exception as e:
        print(f"Error publishing second message: {e}")
        raise


if __name__ == "__main__":
    main()
