#!/usr/bin/env -S python -u

import logging
import uuid

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p07")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Get publisher with retry capabilities
    publisher = get_publisher("/test_p07", [ChangelogSchema])
    publisher.setup()

    print("started")

    # Wait for ready signal before publishing first message
    assert input() == "ready"

    # First message
    event_obj = ChangelogSchema(id=str(uuid.uuid4()), name="test_event")

    # Use the proper publish method which includes schema validation
    # Override the exchange and routing key to match test expectations
    publisher.publish(
        event_obj,
        exchange_override="foos.topic_exchange",
        routing_key_override="foo.changelog"
    )

    # Wait for signal to publish again (connection will be closed externally)
    assert input() == "publish_again"

    # Second message - should auto-reconnect
    event_obj = ChangelogSchema(id=str(uuid.uuid4()), name="test_event")

    # Use the proper publish method which includes schema validation
    publisher.publish(
        event_obj,
        exchange_override="foos.topic_exchange",
        routing_key_override="foo.changelog"
    )


if __name__ == "__main__":
    main()
