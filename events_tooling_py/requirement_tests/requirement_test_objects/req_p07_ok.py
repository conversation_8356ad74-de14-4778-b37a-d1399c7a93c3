#!/usr/bin/env -S python -u

import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p07")


def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    logger.info("Getting publisher...")
    publisher = get_publisher("/test_p07", [ChangelogSchema])
    logger.info("Setting up publisher...")
    publisher.setup()

    print("started")

    assert input() == "ready"

    logger.info("Publishing message...")
    try:
        # Create the event
        event = ChangelogSchema(id="123", name="hello")
        logger.info(f"Created event: {event}")

        # Skip the problematic validation for now and publish directly
        logger.info("Skipping validation and publishing directly...")

        # Use the internal _publish_to_exchange method to bypass validation
        logger.info("About to call _publish_to_exchange...")
        publisher._publish_to_exchange(
            event,
            time=None,
            subject=None,
            id=None,
            routing_key_override="foo.changelog",
            exchange_override="foos.topic_exchange"
        )
        logger.info("Message published successfully")
    except Exception as e:
        logger.error(f"Error publishing message: {e}")
        import traceback
        traceback.print_exc()
        raise

    print("event sent")

    # Wait for signal to publish again (connection will be closed externally)
    assert input() == "publish_again"

    # Second message - should auto-reconnect
    logger.info("Publishing second message...")
    try:
        event2 = ChangelogSchema(id="456", name="world")
        publisher._publish_to_exchange(
            event2,
            time=None,
            subject=None,
            id=None,
            routing_key_override="foo.changelog",
            exchange_override="foos.topic_exchange"
        )
        logger.info("Second message published successfully")
    except Exception as e:
        logger.error(f"Error publishing second message: {e}")
        import traceback
        traceback.print_exc()
        raise

    print("second event sent")


if __name__ == "__main__":
    main()
