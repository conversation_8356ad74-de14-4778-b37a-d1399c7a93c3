#!/usr/bin/env -S python -u

import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p07")


def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    logger.info("Getting publisher...")
    publisher = get_publisher("/test_p07", [ChangelogSchema])
    logger.info("Setting up publisher...")
    publisher.setup()

    print("started")

    assert input() == "ready"

    logger.info("Publishing message...")
    try:
        publisher.publish(ChangelogSchema(id="123", name="hello"))
        logger.info("Message published successfully")
    except Exception as e:
        logger.error(f"Error publishing message: {e}")
        import traceback
        traceback.print_exc()
        raise

    print("event sent")

    # Wait for signal to publish again (connection will be closed externally)
    assert input() == "publish_again"

    # Second message - should auto-reconnect
    logger.info("Publishing second message...")
    publisher.publish(ChangelogSchema(id="456", name="world"))

    print("second event sent")


if __name__ == "__main__":
    main()
