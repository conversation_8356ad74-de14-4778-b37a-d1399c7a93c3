#!/usr/bin/env -S python -u

import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p07")


def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    logger.info("Getting publisher...")
    publisher = get_publisher("/test_p07", [ChangelogSchema])
    logger.info("Setting up publisher...")
    publisher.setup()

    print("started")

    assert input() == "ready"

    logger.info("Publishing message...")
    try:
        # Create the event
        event = ChangelogSchema(id="123", name="hello")
        logger.info(f"Created event: {event}")

        # Use the normal publish method which includes validation
        logger.info("Using normal publish method with validation...")
        logger.info("About to call publisher.publish(event)...")
        publisher.publish(event)
        logger.info("Message published successfully")
    except Exception as e:
        logger.error(f"Error publishing message: {e}")
        import traceback
        traceback.print_exc()
        raise

    print("event sent")

    # Wait for signal to publish again (connection will be closed externally)
    assert input() == "publish_again"

    # Second message - should auto-reconnect
    logger.info("Publishing second message...")
    try:
        event2 = ChangelogSchema(id="456", name="world")
        publisher.publish(event2)
        logger.info("Second message published successfully")
    except Exception as e:
        logger.error(f"Error publishing second message: {e}")
        import traceback
        traceback.print_exc()
        raise

    print("second event sent")

    # Wait for completion signal
    assert input() == "done"
    logger.info("Test completed successfully")


if __name__ == "__main__":
    main()
