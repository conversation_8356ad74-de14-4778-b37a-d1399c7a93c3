#!/usr/bin/env -S python -u


import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c_15")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    # Need to override the dlq routing key as it still has the
    # ".dead_letter.queue" suffix.
    dispatcher = EventDispatcher(
        dlq_routing_key='{service_config.service_name}.{entity_name}_events.dead_letter_queue'
    )

    @dispatcher.register_handler
    def handle_changelog(event: ChangelogSchema) -> None:
        print(event.name)

    consumer = get_consumer("test_c15", [dispatcher])

    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("started")

    consumer.start_consuming()


if __name__ == '__main__':
    main()
