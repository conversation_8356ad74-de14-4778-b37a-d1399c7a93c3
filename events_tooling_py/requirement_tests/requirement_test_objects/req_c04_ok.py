#!/usr/bin/env -S python -u


import logging
import time

from event_test_schemas.foo.changelog.v1 import ChangelogSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c_04")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    dispatcher = EventDispatcher()

    @dispatcher.register_handler
    def handle_changelog(event: ChangelogSchema) -> None:
        print(f"message id = {event.id}")
        time.sleep(0.3)

    consumer = get_consumer("test_c04", [dispatcher])
    logger.info(consumer.consumers)
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("ready")

    assert input() == "consume"

    consumer.start_consuming()


if __name__ == '__main__':
    main()
