#!/usr/bin/env -S python -u


import json
import logging

from event_test_schemas.foo.changelog.v1 import ChangelogSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)
from events_tooling_py.types import EventContext

logger = logging.getLogger("test_c_10")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    dispatcher = EventDispatcher()

    @dispatcher.register_handler
    def handle_changelog(_: ChangelogSchema, ctx: EventContext) -> None:
        print(
            json.dumps({
                "content_type": ctx.metadata.content_type,
                "content_encoding": "UTF-8",
                "app_id": ctx.metadata.app_id,
                "timestamp": ctx.metadata.timestamp,
            })
        )
        print(
            json.dumps({
                "schema": ctx.metadata.schema,
                "entity_type": ctx.metadata.entity_type,
                "entity_id": ctx.metadata.entity_id,
            })
        )

    consumer = get_consumer("test_c10", [dispatcher])
    logger.info(consumer.consumers)
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("started")

    consumer.start_consuming()


if __name__ == '__main__':
    main()
