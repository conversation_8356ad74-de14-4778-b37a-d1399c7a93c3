#!/usr/bin/env -S python -u

import logging

from event_test_schemas.foo.count.v1 import CountSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c06")


class TestException(Exception):
    pass


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    dispatcher = EventDispatcher()

    @dispatcher.register_handler
    def handle_count(event: CountSchema) -> None:
        count = event.count
        if count % 5 == 0:
            print(f"{count}!")
            raise TestException("NACK")
        else:
            print(count)

    # Create consumer with custom exception handler
    consumer = get_consumer("test_c06", [dispatcher])

    # Setup and start consuming
    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("started")
    while True:
        try:
            consumer.start_consuming()
        except TestException:
            pass


if __name__ == '__main__':
    main()
