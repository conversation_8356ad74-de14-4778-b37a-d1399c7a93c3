#!/usr/bin/env -S python -u


import logging
import time

from event_test_schemas.foo.changelog.v1 import ChangelogSchema

from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_consumer,
)

logger = logging.getLogger("test_c_03")


def main() -> None:
    logging.basicConfig(level=logging.INFO)

    dispatcher = EventDispatcher()

    @dispatcher.register_handler
    def handle_changelog(event: ChangelogSchema) -> None:
        print("consuming")
        time.sleep(60)

    consumer = get_consumer("test_c03", [dispatcher])

    # Note: this needs to be done before setup
    consumer.rabbitmq.channel.basic_qos(prefetch_count=5)

    consumer.setup(start_consuming=False, declare_exchanges='dead-letter-only')

    print("started")

    assert input() == "ready"

    consumer.start_consuming()


if __name__ == '__main__':
    main()
