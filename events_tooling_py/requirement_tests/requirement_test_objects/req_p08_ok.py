#!/usr/bin/env -S python -u


import logging

from event_test_schemas.foo.changelog.v2 import ChangelogSchema

from events_tooling_py.requirement_tests.requirement_test_objects.test_object_utils import (
    get_publisher,
)

logger = logging.getLogger("test_p08")


def main() -> None:
    # This test currently fails because the publisher is unable to recover after
    # the test driver closes its connection to rabbitmq.
    logging.basicConfig(level=logging.INFO)
    publisher = get_publisher("/test_p08", [ChangelogSchema])
    publisher.setup()

    print("started")

    assert input() == "ready"
    publisher.publish(ChangelogSchema(id="123", name="Foo"))
    print("event sent")

    assert input() == "done"


if __name__ == '__main__':
    main()
