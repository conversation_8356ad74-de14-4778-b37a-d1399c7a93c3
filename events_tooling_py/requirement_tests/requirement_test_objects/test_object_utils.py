from functools import lru_cache
import os

from events_tooling_py.config import ConsumerQueueConfigProvider, ServiceConfig
from events_tooling_py.connection_client import Pika<PERSON>lient
from events_tooling_py.event_client import EventConsumer, EventPublisher
from events_tooling_py.types import EventBase


def get_exchange_for_test_entity(entity_name: str) -> str:
    return entity_name + "s.topic_exchange"


@lru_cache(maxsize=1)
def get_rabbitmq_client() -> PikaClient:
    username = os.getenv("RABBITMQ_USERNAME", "guest")
    password = os.getenv("RABBITMQ_PASSWORD", "guest")
    host = os.getenv("RABBITMQ_HOST", 'localhost')
    port = os.getenv("RABBITMQ_PORT", 5672)
    vhost = os.getenv("RABBITMQ_VHOST", "/")

    return PikaClient(
        config={"url": f"http://{username}:{password}@{host}:{port}/{vhost}"}
    )


def get_publisher(
    service_name: str, event_schemas: list[type[EventBase]]
) -> EventPublisher:
    return EventPublisher(
        rabbitmq_client=get_rabbitmq_client(),
        service_config=ServiceConfig(
            service_name=service_name,
            get_exchange_for_entity=get_exchange_for_test_entity,
        ),
        event_schemas=event_schemas,
    )


def get_consumer(
    service_name: str, consumer_providers: list[ConsumerQueueConfigProvider]
) -> EventConsumer:
    return EventConsumer(
        rabbitmq_client=get_rabbitmq_client(),
        service_config=ServiceConfig(
            service_name=service_name,
            get_exchange_for_entity=get_exchange_for_test_entity,
        ),
        consumer_providers=consumer_providers,
    )
