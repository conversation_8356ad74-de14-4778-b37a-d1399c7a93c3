import argparse
import asyncio
import pathlib
import sys

from event_tooling_test_engine.requirement_tests import print_report
from event_tooling_tests import testsuite

test_object_dir = pathlib.Path(__file__).parent.resolve() / "requirement_test_objects"  # noqa: F821


async def run_tests(
    requirement_codes: list[str] | None = None, verbose: bool = False
) -> None:
    results = await testsuite.run_tests(
        test_object_dir, requirement_codes=requirement_codes
    )
    ok_results = [r for r in results if r.test_object_path.name.endswith("_ok.py")]
    fail_results = [r for r in results if r.test_object_path.name.endswith("_fail.py")]
    passed_fail_results = [r for r in fail_results if r.passed]

    print("=" * 20, "ok tests")
    print_report(ok_results, detailed=True)

    print("=" * 20, "fail tests")
    print_report(fail_results, detailed=verbose)

    if passed_fail_results:
        print("The following tests passed when they should have failed:")
        for r in passed_fail_results:
            print(f"{r.requirement_test.requirement_code}: {r.test_object_path.name}")

    if any(not result.passed for result in ok_results) or passed_fail_results:
        sys.exit(1)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--req", type=str, default=None)
    args = parser.parse_args()

    requirement_codes = [args.req] if args.req else None
    verbose = args.req is not None

    asyncio.run(run_tests(requirement_codes, verbose))
