from unittest import mock

import pytest

from events_tooling_py.cloudevent import CloudEventSchema
from events_tooling_py.config import (
    ArgumentsConfig,
    BindingConfig,
    ConsumerQueueConfig,
    QueueConfig,
    ServiceConfig,
)
from events_tooling_py.dispatcher import (
    DefaultHandlerAlreadyRegisteredError,
    EventDispatcher,
    EventHandlerAlreadyRegisteredError,
)
from events_tooling_py.tests.sample_events import (
    SampleV1Event,
    SampleV1Event2,
    SampleV2Event,
)
from events_tooling_py.types import CloudEventProtocol, EventContext, EventMetadata


@pytest.fixture
def sample_v1_event():
    return CloudEventSchema(
        type="multiverse.sample.event.v1",
        dataschema="https://schema.multiverse.io/sample/v1",
        source="test",
        data={"message": "v1"},
    )


@pytest.fixture
def sample_v2_event():
    return CloudEventSchema(
        type="multiverse.sample.event.v2",
        dataschema="https://schema.multiverse.io/sample/v2",
        source="test",
        data={"message": "v2"},
    )


@pytest.fixture
def metadata():
    return EventMetadata(
        app_id="test_app",
        timestamp_microseconds=123456789,
    )


def test_dispatcher_two_versions(sample_v1_event, sample_v2_event, metadata):
    dispatcher = EventDispatcher()
    handle_v1 = mock.Mock()
    handle_v2 = mock.Mock()
    dispatcher.handle('multiverse.sample.event.v1')(handle_v1)
    dispatcher.handle('multiverse.sample.event.v2')(handle_v2)
    dispatcher(sample_v1_event, metadata)
    handle_v1.assert_called_once_with(sample_v1_event, metadata)
    dispatcher(sample_v2_event, metadata)
    handle_v2.assert_called_once_with(sample_v2_event, metadata)


def test_dispatcher_default_handler(sample_v1_event, sample_v2_event, metadata):
    dispatcher = EventDispatcher()
    handle_v1 = mock.Mock()
    handle_default = mock.Mock()
    dispatcher.handle('multiverse.sample.event.v1')(handle_v1)
    dispatcher.handle_default(handle_default)
    dispatcher(sample_v1_event, metadata)
    handle_v1.assert_called_once_with(sample_v1_event, metadata)
    dispatcher(sample_v2_event, metadata)
    handle_default.assert_called_once_with(sample_v2_event, metadata)


def test_dispatcher_register_handler_twice():
    dispatcher = EventDispatcher()

    @dispatcher.handle('multiverse.sample.event.v1')
    def handler1(event: CloudEventProtocol):
        pass

    with pytest.raises(EventHandlerAlreadyRegisteredError):

        @dispatcher.handle('multiverse.sample.event.v1')
        def handler2(event: CloudEventProtocol):
            pass


def test_dispatcher_register_default_handler_twice():
    dispatcher = EventDispatcher()

    @dispatcher.handle_default
    def handler1(event: CloudEventProtocol):
        pass

    with pytest.raises(DefaultHandlerAlreadyRegisteredError):

        @dispatcher.handle_default
        def handler2(event: CloudEventProtocol):
            pass


def test_dispatcher_handle_type(sample_v1_event, sample_v2_event, metadata):
    dispatcher = EventDispatcher()

    handle_v1 = mock.Mock()
    handle_v2 = mock.Mock()

    @dispatcher.register_handler_for_type(SampleV1Event)
    def handler1(event: SampleV1Event):
        assert isinstance(event, SampleV1Event)
        handle_v1(event.message)

    @dispatcher.register_handler_for_type(SampleV2Event)
    def handler2(event: SampleV2Event, ctx: EventContext):
        assert isinstance(event, SampleV2Event)
        assert isinstance(ctx, EventContext)
        assert ctx.envelope.source == 'test'
        assert ctx.metadata.app_id == 'test_app'
        handle_v2(event.message)

    dispatcher(sample_v1_event, metadata)
    dispatcher(sample_v2_event, metadata)

    handle_v1.assert_called_once_with("v1")
    handle_v2.assert_called_once_with("v2")


def test_dispatcher_register_handler(sample_v1_event, sample_v2_event, metadata):
    dispatcher = EventDispatcher()

    handle_v1 = mock.Mock()
    handle_v2 = mock.Mock()

    @dispatcher.register_handler
    def handler1(event: SampleV1Event):
        assert isinstance(event, SampleV1Event)
        handle_v1(event.message)

    @dispatcher.register_handler
    def handler2(event: SampleV2Event, ctx: EventContext):
        assert isinstance(event, SampleV2Event)
        assert isinstance(ctx, EventContext)
        assert ctx.envelope.source == 'test'
        assert ctx.metadata.timestamp_microseconds == 123456789
        handle_v2(event.message)

    dispatcher(sample_v1_event, metadata)
    dispatcher(sample_v2_event, metadata)

    handle_v1.assert_called_once_with("v1")
    handle_v2.assert_called_once_with("v2")


def test_dispatcher_get_consumer_queue_config():
    dispatcher = EventDispatcher()

    @dispatcher.register_handler
    def handler1(event: SampleV1Event):
        pass

    @dispatcher.register_handler
    def handler2(event: SampleV2Event):
        pass

    @dispatcher.register_handler
    def handler3(event: SampleV1Event2):
        pass

    service_config = ServiceConfig(
        service_name="test_service",
        get_exchange_for_entity=lambda e: {'sample': 'samples'}[e] + ".topic_exchange",
    )

    consumer_queue_config = dispatcher.get_consumer_queue_config(service_config)

    assert consumer_queue_config == ConsumerQueueConfig(
        dlq=QueueConfig(
            name="test_service.sample_events.dead_letter_queue",
            durable=True,
            bindings=[
                BindingConfig(
                    exchange="default-dead-letter-exchange",
                    routing_key="test_service.sample_events.dead_letter.queue",
                )
            ],
        ),
        queue=QueueConfig(
            name="test_service.sample_events.queue",
            durable=True,
            arguments=ArgumentsConfig(**{
                "x-queue-type": 'quorum',
                "x-single-active-consumer": True,
                "x-dead-letter-exchange": "default-dead-letter-exchange",
                "x-dead-letter-routing-key": "test_service.sample_events.dead_letter.queue",
            }),
            bindings=[
                BindingConfig(
                    exchange="samples.topic_exchange",
                    routing_key="sample.event",
                ),
                BindingConfig(
                    exchange="samples.topic_exchange",
                    routing_key="sample.event2",
                ),
            ],
            handler=dispatcher,
            delivery_limit=5,
        ),
    )


def test_dispatcher_get_consumer_queue_config_overrides():
    dispatcher = EventDispatcher(
        queue_name="{service_config.service_name}_{entity_name}_queue",
        dlq_name="{service_config.service_name}_{entity_name}_dlq",
        dlq_routing_key="{service_config.service_name}_{entity_name}_dlq",
    )

    @dispatcher.register_handler
    def handler1(event: SampleV1Event):
        pass

    @dispatcher.register_handler
    def handler2(event: SampleV2Event):
        pass

    @dispatcher.register_handler
    def handler3(event: SampleV1Event2):
        pass

    service_config = ServiceConfig(
        service_name="test_service",
        get_exchange_for_entity=lambda e: {'sample': 'samples'}[e] + ".topic_exchange",
    )

    consumer_queue_config = dispatcher.get_consumer_queue_config(service_config)

    assert consumer_queue_config == ConsumerQueueConfig(
        dlq=QueueConfig(
            name="test_service_sample_dlq",
            durable=True,
            bindings=[
                BindingConfig(
                    exchange="default-dead-letter-exchange",
                    routing_key="test_service_sample_dlq",
                )
            ],
        ),
        queue=QueueConfig(
            name="test_service_sample_queue",
            durable=True,
            arguments=ArgumentsConfig(**{
                "x-queue-type": 'quorum',
                "x-single-active-consumer": True,
                "x-dead-letter-exchange": "default-dead-letter-exchange",
                "x-dead-letter-routing-key": "test_service_sample_dlq",
            }),
            bindings=[
                BindingConfig(
                    exchange="samples.topic_exchange",
                    routing_key="sample.event",
                ),
                BindingConfig(
                    exchange="samples.topic_exchange",
                    routing_key="sample.event2",
                ),
            ],
            handler=dispatcher,
            delivery_limit=5,
        ),
    )
