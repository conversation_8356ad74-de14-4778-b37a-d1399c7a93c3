from unittest.mock import Mock

from pika.exceptions import StreamLostError
import pytest

from events_tooling_py.cloudevent import CloudEventSchema
from events_tooling_py.connection_client import PikaClient
from events_tooling_py.event_client import EventPublisher
from events_tooling_py.types import MessagingError


@pytest.fixture
def sample_v1_event():
    return CloudEventSchema(
        type="multiverse.sample.v1",
        dataschema="https://schema.multiverse.io/sample/v1",
        source="test",
        data={"message": "v1"},
    )


@pytest.fixture
def mock_rabbitmq_client():
    mock_rabbitmq_client = Mock(spec=PikaClient)
    return mock_rabbitmq_client


def test_publish_to_exchange_retries_x_times(mock_rabbitmq_client, sample_v1_event):
    publisher = EventPublisher(
        config={},
        rabbitmq_client=mock_rabbitmq_client,
        max_retry_count=3,
    )
    publisher.channel_manager.channel.basic_publish.side_effect = StreamLostError(
        'Stream lost'
    )
    with pytest.raises(MessagingError):
        publisher.publish_to_exchange(
            'exchange',
            'routing_key',
            sample_v1_event,
        )

    assert (
        publisher.channel_manager.channel.basic_publish.call_count == 4
    ), 'publisher.basic_publish_cloudevent should have been called once and retried 3 times'
    assert (
        publisher.channel_manager.client.close.call_count == 3
    ), 'self.rabbitmq.close should have been called 3 times (one for each retry)'


def test_publish_to_exchange_retries_then_succeeds(
    mock_rabbitmq_client, sample_v1_event
):
    publisher = EventPublisher(config={}, rabbitmq_client=mock_rabbitmq_client)
    publisher.channel_manager.channel.basic_publish.side_effect = (
        StreamLostError('Stream lost'),
        None,
    )

    assert (
        publisher.publish_to_exchange(
            'exchange',
            'routing_key',
            sample_v1_event,
        )
        is None
    ), 'publish_to_exchange should of succeeded after 2 retries - returns None'

    assert (
        publisher.channel_manager.channel.basic_publish.call_count == 2
    ), 'publisher.basic_publish_cloudevent should have failed first time then succeeded on the second attempt'
    assert (
        publisher.channel_manager.client.close.call_count == 1
    ), 'self.rabbitmq.close should have been called once (one retry)'
