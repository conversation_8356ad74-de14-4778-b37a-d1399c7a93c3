# serializer version: 1
# name: test_scrub_pii_event_dict
  dict({
    'cohort': dict({
      'id': '66666666-7777-8888-9999-000000000000',
      'title': 'Cohort #1',
    }),
    'user': dict({
      'bio': '<REDACTED>',
      'first_name': '<REDACTED>',
      'id': '00000000-1111-2222-3333-444444444444',
      'image': '<REDACTED>',
      'last_name': '<REDACTED>',
      'mobile_number': '<REDACTED>',
      'phone_number': '<REDACTED>',
      'region': 'US',
    }),
  })
# ---
# name: test_scrub_pii_user_dict
  dict({
    'bio': '<REDACTED>',
    'first_name': '<REDACTED>',
    'id': '00000000-1111-2222-3333-444444444444',
    'image': '<REDACTED>',
    'last_name': '<REDACTED>',
    'mobile_number': '<REDACTED>',
    'phone_number': '<REDACTED>',
    'region': 'US',
  })
# ---
# name: test_scrub_pii_user_list
  dict({
    'cohort': dict({
      'id': '66666666-7777-8888-9999-000000000000',
      'title': 'Cohort #1',
    }),
    'users': list([
      dict({
        'bio': '<REDACTED>',
        'first_name': '<REDACTED>',
        'id': '00000000-1111-2222-3333-444444444444',
        'image': '<REDACTED>',
        'last_name': '<REDACTED>',
        'mobile_number': '<REDACTED>',
        'phone_number': '<REDACTED>',
        'region': 'US',
      }),
      dict({
        'bio': '<REDACTED>',
        'first_name': '<REDACTED>',
        'id': '11111111-2222-3333-4444-555555555555',
        'image': '<REDACTED>',
        'last_name': '<REDACTED>',
        'mobile_number': '<REDACTED>',
        'phone_number': '<REDACTED>',
        'region': 'UK',
      }),
    ]),
  })
# ---
