import unittest.mock as mock

from pydantic import BaseModel
import pytest

from events_tooling_py.config import ServiceConfig
from events_tooling_py.event_client import EventPublisher
from events_tooling_py.tests.sample_events import SampleV1Event
from events_tooling_py.utils import (
    validate_against_schema,
    validate_event_against_schema,
)


class TestSchemaValidation:
    """Tests for schema validation functionality."""

    class SimpleEvent(BaseModel):
        """Simple event model for testing."""

        message: str = ""

        class Meta:
            schema_type = "test.simple"
            schema_uri = "test://simple"
            raw_json_schema = {
                "type": "object",
                "properties": {"message": {"type": "string"}},
                "required": ["message"],
            }

        def model_dump(self):
            """Convert to dict."""
            return {"message": self.message}

    def test_validate_against_schema_valid(self):
        """Test that validate_against_schema returns True for valid events."""
        # Create a valid event
        event = self.SimpleEvent(message="hello")
        schema = {
            "type": "object",
            "properties": {"message": {"type": "string"}},
            "required": ["message"],
        }

        # Validate the event
        is_valid, errors = validate_against_schema(event, schema)

        # Check that the event is valid
        assert is_valid
        assert len(errors) == 0

    def test_validate_against_schema_invalid(self):
        """Test that validate_against_schema returns False for invalid events."""
        # Create a test event for invalid schema check
        event = self.SimpleEvent()  # Missing required message

        # Set the message to None which is invalid for string type
        event.message = None  # type: ignore

        schema = {
            "type": "object",
            "properties": {"message": {"type": "string"}},
            "required": ["message"],
        }

        # Validate the event
        is_valid, errors = validate_against_schema(event, schema)

        # Check that the event is invalid
        assert not is_valid
        assert len(errors) > 0
        assert (
            "message" in errors[0] or "None" in errors[0]
        )  # Error message should indicate issue

    def test_validate_against_schema_invalid_type(self):
        """Test that validate_against_schema returns False for events with invalid types."""
        # Create a test event with mock model_dump to return invalid type
        event = mock.Mock()
        event.model_dump = mock.Mock(return_value={"message": 123})
        schema = {
            "type": "object",
            "properties": {"message": {"type": "string"}},
            "required": ["message"],
        }

        # Validate the event
        is_valid, errors = validate_against_schema(event, schema)

        # Check that the event is invalid
        assert not is_valid
        assert len(errors) > 0
        assert "123" in errors[0]  # Error message should mention the invalid value

    def test_publisher_validates_event(self, monkeypatch):
        """Test that the publisher validates events before publishing."""
        # Mock the validate_event_against_schema function
        mock_validate = mock.Mock()
        monkeypatch.setattr(
            "events_tooling_py.event_client.validate_event_against_schema",
            mock_validate,
        )

        # Create a publisher
        mock_pika_client = mock.Mock()
        service_config = ServiceConfig(
            service_name="test_service",
            get_exchange_for_entity=lambda e: {'sample': 'samples'}[e]
            + ".topic_exchange",
        )
        publisher = EventPublisher(
            config={"exchanges": []},
            rabbitmq_client=mock_pika_client,
            service_config=service_config,
            event_schemas=[SampleV1Event],
        )
        publisher._publish_to_exchange = mock.Mock()

        # Publish an event
        event = SampleV1Event(message="hello")
        publisher.publish(event)

        # Check that validate_event_against_schema was called with the event and its schema
        mock_validate.assert_called_once_with(
            event, event.Meta.raw_json_schema, service_config.service_name
        )

    def test_publisher_validation_error_propagates(self, monkeypatch):
        """Test that the publisher propagates validation errors."""
        # Mock the validate_event_against_schema function to raise a ValueError
        mock_validate = mock.Mock(side_effect=ValueError("Validation error"))
        monkeypatch.setattr(
            "events_tooling_py.event_client.validate_event_against_schema",
            mock_validate,
        )

        # Create a publisher
        mock_pika_client = mock.Mock()
        service_config = ServiceConfig(
            service_name="test_service",
            get_exchange_for_entity=lambda e: {'sample': 'samples'}[e]
            + ".topic_exchange",
        )
        publisher = EventPublisher(
            config={"exchanges": []},
            rabbitmq_client=mock_pika_client,
            service_config=service_config,
            event_schemas=[SampleV1Event],
        )
        publisher._publish_to_exchange = mock.Mock()

        # Try to publish an event that will fail validation
        event = SampleV1Event(message="hello")
        with pytest.raises(ValueError) as excinfo:
            publisher.publish(event)

        # Check that the error message was propagated
        assert "Validation error" in str(excinfo.value)

    def test_validate_event_against_schema_success(self, monkeypatch):
        """Test that validate_event_against_schema succeeds for valid events."""
        # Mock validate_against_schema to return success
        mock_validate = mock.Mock(return_value=(True, []))
        monkeypatch.setattr(
            "events_tooling_py.utils.validate_against_schema", mock_validate
        )

        # Create an event and schema
        event = mock.Mock()
        event.Meta.schema_type = "test.type"
        event.Meta.schema_uri = "test://uri"
        schema = {"type": "object"}

        # This should not raise an exception
        validate_event_against_schema(event, schema, "test_service")

        # Verify validate_against_schema was called
        mock_validate.assert_called_once_with(event, schema)

    def test_validate_event_against_schema_failure(self, monkeypatch, caplog):
        """Test that validate_event_against_schema raises an error for invalid events."""
        import logging

        caplog.set_level(logging.CRITICAL)

        # Mock validate_against_schema to return failure
        mock_validate = mock.Mock(return_value=(False, ["Error message"]))
        monkeypatch.setattr(
            "events_tooling_py.utils.validate_against_schema", mock_validate
        )

        # Create an event and schema
        event = mock.Mock()
        event.Meta.schema_type = "test.type"
        event.Meta.schema_uri = "test://uri"
        event.model_dump = mock.Mock(return_value={"field": "value"})
        schema = {"type": "object"}

        # This should raise an exception
        with pytest.raises(ValueError) as excinfo:
            validate_event_against_schema(event, schema, "test_service")

        # Check that the error message includes the validation error
        assert "Event validation failed" in str(excinfo.value)
        assert "Error message" in str(excinfo.value)

        # Check that the error was logged at CRITICAL level
        assert len(caplog.records) == 1
        assert "ALERT: Schema validation failed" in caplog.records[0].message

        # Check that the log record is at CRITICAL level
        assert caplog.records[0].levelname == "CRITICAL"
