import json
import unittest.mock as mock

from pika import BasicProperties
from pika.adapters.blocking_connection import BlockingChannel
from pika.spec import Basic
import pytest

from events_tooling_py.cloudevent import CloudEventSchema
from events_tooling_py.connection_client import PikaClient
from events_tooling_py.event_client import EventConsumer, wrap_event_handler
from events_tooling_py.types import EventMetadata


@pytest.fixture
def mock_consumer():
    return EventConsumer(
        config={'consumers': []},
        rabbitmq_client=PikaClient(config={'url': 'amqp://localhost'}),
    )


@pytest.fixture
def mock_channel():
    return mock.Mock(spec=BlockingChannel)


@pytest.fixture
def mock_method():
    mock_method = mock.Mock(spec=Basic.Deliver)
    mock_method.delivery_tag = 1
    mock_method.routing_key = 'foo.bar'
    mock_method.exchange = "foo.bar"
    return mock_method


@pytest.fixture
def mock_properties():
    mock_properties = mock.Mock(spec=BasicProperties)
    mock_properties.content_type = "application/json"
    mock_properties.app_id = 'mock_app'
    mock_properties.timestamp = 123456
    mock_properties.headers = {
        "schema": "mock_schema",
        "entity_type": "foo",
        "timestamp_microseconds": *********,
    }

    return mock_properties


@pytest.fixture
def mock_event_handler():
    return mock.Mock()


@pytest.fixture
def mock_exception_handler():
    return mock.Mock()


@pytest.fixture
def callack_with_exception_handler(mock_event_handler, mock_exception_handler):
    return wrap_event_handler(mock_event_handler, mock_exception_handler)


@pytest.fixture
def legacy_message():
    return '{"a": 1}'


def test_callback_with_legacy_message(
    mock_event_handler,
    mock_exception_handler,
    mock_channel,
    mock_method,
    mock_properties,
    legacy_message,
):
    callback = wrap_event_handler(mock_event_handler, mock_exception_handler)
    callback(mock_channel, mock_method, mock_properties, legacy_message)
    mock_channel.basic_ack.assert_called_once()
    mock_channel.basic_nack.assert_not_called()
    mock_event_handler.assert_called_once()
    event = mock_event_handler.call_args.args[0]
    assert isinstance(event, CloudEventSchema)
    assert event.data == json.loads(legacy_message)
    # The routing key is used to provide the message with a type
    assert event.type == "foo.bar"
    mock_exception_handler.assert_not_called()


@pytest.fixture
def cloudevent_message():
    return '{"source": "here", "type": "sample.v1", "dataschema": "https://schema.multiverse.io/sample/v1", "data": [1, 2]}'


def test_callback_with_cloudevent_message(
    mock_event_handler,
    mock_exception_handler,
    mock_channel,
    mock_method,
    mock_properties,
    cloudevent_message,
):
    callback = wrap_event_handler(mock_event_handler, mock_exception_handler)
    callback(mock_channel, mock_method, mock_properties, cloudevent_message)
    mock_channel.basic_ack.assert_called_once()
    mock_channel.basic_nack.assert_not_called()
    mock_event_handler.assert_called_once()
    event = mock_event_handler.call_args.args[0]
    assert isinstance(event, CloudEventSchema)
    assert event.data == [1, 2]
    assert event.type == "sample.v1"
    metadata = mock_event_handler.call_args.args[1]
    assert isinstance(metadata, EventMetadata)
    assert metadata.content_type == 'application/json'
    assert metadata.app_id == 'mock_app'
    assert metadata.timestamp == 123456
    assert metadata.entity_type == 'foo'
    assert metadata.entity_id is None
    assert metadata.timestamp_microseconds == *********
    mock_exception_handler.assert_not_called()


def test_callback_with_exception_in_event_handler(
    mock_event_handler,
    mock_exception_handler,
    mock_channel,
    mock_method,
    mock_properties,
    cloudevent_message,
):
    exception = Exception('Test exception')
    mock_event_handler.side_effect = exception
    callback = wrap_event_handler(mock_event_handler, mock_exception_handler)
    with pytest.raises(Exception) as e:
        callback(mock_channel, mock_method, mock_properties, cloudevent_message)
        assert e.value == exception
    mock_channel.basic_ack.assert_not_called()
    mock_channel.basic_nack.assert_called_once()
    mock_event_handler.assert_called_once()
    mock_exception_handler.assert_called_once_with(exception)
