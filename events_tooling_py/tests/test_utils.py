import json
from unittest.mock import patch

import pytest

from events_tooling_py.utils import format_message, scrub_pii


def test_scrub_pii_user_dict(snapshot):
    snapshot.assert_match(
        scrub_pii({
            'id': '00000000-1111-2222-3333-444444444444',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'image': 'https://example.com/image.jpg',
            'bio': 'I like to code',
            'phone_number': '1234567890',
            'mobile_number': '0987654321',
            'region': 'US',
        })
    )


def test_scrub_pii_event_dict(snapshot):
    snapshot.assert_match(
        scrub_pii({
            'cohort': {
                'id': '66666666-7777-8888-9999-000000000000',
                'title': 'Cohort #1',
            },
            'user': {
                'id': '00000000-1111-2222-3333-444444444444',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>e',
                'image': 'https://example.com/image.jpg',
                'bio': 'I like to code',
                'phone_number': '1234567890',
                'mobile_number': '0987654321',
                'region': 'US',
            },
        })
    )


def test_scrub_pii_user_list(snapshot):
    snapshot.assert_match(
        scrub_pii({
            'cohort': {
                'id': '66666666-7777-8888-9999-000000000000',
                'title': 'Cohort #1',
            },
            'users': [
                {
                    'id': '00000000-1111-2222-3333-444444444444',
                    'first_name': 'John',
                    'last_name': 'Doe',
                    'image': 'https://example.com/image.jpg',
                    'bio': 'I like to code',
                    'phone_number': '1234567890',
                    'mobile_number': '0987654321',
                    'region': 'US',
                },
                {
                    'id': '11111111-2222-3333-4444-555555555555',
                    'first_name': 'Jane',
                    'last_name': 'Doe',
                    'image': 'https://example.com/image.jpg',
                    'bio': 'I like to design',
                    'phone_number': '1234567890',
                    'mobile_number': '0987654321',
                    'region': 'UK',
                },
            ],
        })
    )


def test_format_message_with_string():
    message = '{"key": "value"}'
    with patch(
        'events_tooling_py.utils.scrub_pii',
        return_value={"key": "value"},
    ) as mock_scrub_pii:
        result = format_message(message)
    mock_scrub_pii.assert_called_once_with(json.loads(message))
    assert result == message


def test_format_message_with_dict():
    message = {"key": "value"}
    with patch(
        'events_tooling_py.utils.scrub_pii',
        return_value=message,
    ) as mock_scrub_pii:
        result = format_message(message)
    mock_scrub_pii.assert_called_once_with(message)
    assert result == json.dumps(message)


def test_format_message_with_invalid_string():
    message = 'not a valid json string'
    with pytest.raises(json.JSONDecodeError):
        format_message(message)
