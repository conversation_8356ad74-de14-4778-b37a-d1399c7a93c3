from datetime import datetime
import unittest.mock as mock
import uuid

from pika.exchange_type import ExchangeType
import pytest

from events_tooling_py.cloudevent import CloudEventSchema
from events_tooling_py.config import (
    BindingConfig,
    ConsumerQueueConfig,
    QueueConfig,
    ServiceConfig,
)
from events_tooling_py.event_client import (
    EventConsumer,
    EventPublisher,
)
from events_tooling_py.tests.sample_events import (
    SampleV1Event,
    SampleV1Event2,
    SampleV2Event,
)


@pytest.fixture
def mock_pika_client():
    return mock.Mock()


@pytest.fixture
def consumer(mock_pika_client):
    consumer = EventConsumer(
        config={"consumers": []},
        rabbitmq_client=mock_pika_client,
    )
    consumer.queue_declare = mock.Mock()
    consumer.bind_queue_to_exchange = mock.Mock()
    consumer._declare_exchanges = mock.Mock()
    consumer.start_consuming = mock.Mock()
    return consumer


@pytest.fixture
def publisher(mock_pika_client):
    publisher = EventPublisher(
        config={"exchanges": []},
        rabbitmq_client=mock_pika_client,
    )
    publisher.declare_exchanges = mock.Mock()
    return publisher


def test_consumer_setup_with_declaring_exchanges(
    consumer,
    mock_pika_client,
):
    consumer.declare_and_bind_queues = mock.Mock()
    consumer.declare_exchanges = mock.Mock()
    consumer.setup(declare_exchanges=True)
    mock_pika_client.connect.assert_called_once()
    consumer.declare_exchanges.assert_called_once()
    consumer.declare_and_bind_queues.assert_called_once()
    consumer.start_consuming.assert_called_once()


def test_consumer_setup_without_declaring_exchanges(
    consumer,
    mock_pika_client,
):
    consumer.declare_and_bind_queues = mock.Mock()
    consumer.declare_exchanges = mock.Mock()
    consumer.setup()
    mock_pika_client.connect.assert_called_once()
    consumer.declare_exchanges.assert_not_called()
    consumer.declare_and_bind_queues.assert_called_once()
    consumer.start_consuming.assert_called_once()


def test_consumer_declare_and_bind_queues(consumer):
    consumer.consumers = [
        ConsumerQueueConfig(
            dlq=QueueConfig(
                name="dlq",
                durable=True,
            ),
            queue=QueueConfig(
                name="queue",
                durable=True,
                bindings=[
                    BindingConfig(
                        exchange="exchange1",
                        routing_key="key1",
                    ),
                ],
            ),
        )
    ]

    consumer.declare_and_bind_queues()
    assert consumer.queue_declare.call_count == 2
    assert consumer.bind_queue_to_exchange.call_count == 1


def test_consumer_declare_exchanges(consumer):
    consumer.consumers = [
        ConsumerQueueConfig(
            dlq=QueueConfig(
                name="dlq",
                bindings=[
                    BindingConfig(exchange="dlq_exchange1", routing_key="key1"),
                    BindingConfig(exchange="dlq_exchange2", routing_key="key2"),
                ],
            ),
            queue=QueueConfig(
                name="queue",
                bindings=[
                    BindingConfig(exchange="exchange1", routing_key="key1"),
                    BindingConfig(exchange="exchange2", routing_key="key2"),
                ],
            ),
        )
    ]

    consumer.declare_exchanges()

    assert consumer._declare_exchanges.call_count == 2
    consumer._declare_exchanges.assert_any_call(
        {"exchange1", "exchange2"},
        ExchangeType.topic,
    )
    consumer._declare_exchanges.assert_any_call(
        {"dlq_exchange1", "dlq_exchange2"},
        ExchangeType.direct,
    )


def test_publisher_setup(
    publisher,
    mock_pika_client,
):
    publisher.setup()
    mock_pika_client.connect.assert_called_once()
    publisher.declare_exchanges.assert_called_once()


@pytest.mark.parametrize('dlq_only', (True, False))
def test_get_bindings(dlq_only):
    q_bindings = [
        {
            'exchange': 'foo',
            'routing_key': 'bar',
        },
        {
            'exchange': 'buck',
            'routing_key': 'fizz',
        },
    ]
    dlq_bindings = [
        {
            'exchange': 'foo-dql',
            'routing_key': 'bar-dql',
        },
        {
            'exchange': 'buck',
            'routing_key': 'fizz',
        },
    ]
    config = {
        'consumers': [
            {
                'queue': {'name': 'q_queue', 'bindings': q_bindings},
                'dlq': {'name': 'q_dlq', 'bindings': dlq_bindings},
            }
        ]
    }
    event_consumer = EventConsumer(config=config, rabbitmq_client=None)
    bindings = event_consumer.get_bindings(dlq_only=dlq_only)
    expected_bindings = dlq_bindings if dlq_only else q_bindings
    assert len(bindings) == len(expected_bindings)
    for i, binding in enumerate(bindings):
        assert binding.exchange == expected_bindings[i]['exchange']
        assert binding.routing_key == expected_bindings[i]['routing_key']


@pytest.mark.parametrize('dlq_only', (True, False))
def test_get_bindings_no_bindings(dlq_only):
    config = {
        'consumers': [
            {
                'queue': {'name': 'q_queue'},
                'dlq': {'name': 'q_dlq'},
            }
        ]
    }
    event_consumer = EventConsumer(config=config, rabbitmq_client=None)
    bindings = event_consumer.get_bindings(dlq_only=dlq_only)
    assert len(bindings) == 0


def test_publisher_configured_with_events(mock_pika_client):
    service_config = ServiceConfig(
        service_name="test_service",
        get_exchange_for_entity=lambda e: {'sample': 'samples'}[e] + ".topic_exchange",
    )

    publisher = EventPublisher(
        config={"exchanges": []},
        rabbitmq_client=mock_pika_client,
        service_config=service_config,
        event_schemas=[SampleV2Event, SampleV1Event2],
    )
    publisher.publish_to_exchange = mock.Mock()

    # Check setup creates exchanges
    publisher.setup()
    mock_pika_client.connect.assert_called_once()
    publisher.channel_manager.channel.exchange_declare.assert_has_calls([
        mock.call(
            exchange='samples.topic_exchange',
            exchange_type=ExchangeType.topic,
            durable=True,
        ),
        mock.call(
            exchange='samples.topic_exchange',
            exchange_type=ExchangeType.topic,
            durable=True,
        ),
    ])
    assert SampleV2Event in publisher.registered_event_schemas
    assert SampleV1Event2 in publisher.registered_event_schemas

    # Check event gets publish with correct envelope
    event = SampleV2Event(message="hello")
    now = datetime.now()
    event_uuid = uuid.uuid4()
    publisher.publish(event, time=now, id=event_uuid)
    publisher.publish_to_exchange.assert_called_once_with(
        exchange='samples.topic_exchange',
        routing_key='sample.event',
        event=CloudEventSchema(
            id=event_uuid,
            type='multiverse.sample.event.v2',
            dataschema="https://schema.multiverse.io/sample/event/v2",
            source="test_service",
            data=event,
            time=now,
        ),
    )

    # Check publishing unregistered event raise error
    eventv2 = SampleV1Event(message="bye")
    with pytest.raises(ValueError):
        publisher.publish(eventv2)


@pytest.mark.parametrize('default_exchange', (True, False))
def test_publisher_publish_with_overrides(mock_pika_client, default_exchange):
    service_config = ServiceConfig(
        service_name="test_service",
        get_exchange_for_entity=lambda e: {'sample': 'samples'}[e] + ".topic_exchange",
    )
    publisher = EventPublisher(
        config={"exchanges": []},
        rabbitmq_client=mock_pika_client,
        service_config=service_config,
        event_schemas=[SampleV1Event],
    )
    publisher.publish_to_exchange = mock.Mock()

    event = SampleV1Event(message="hello")
    now = datetime.now()
    event_uuid = uuid.uuid4()

    expected_exchange = '' if default_exchange else 'foo.topic_exchange'
    expected_routing_key = 'foo.bar'
    publisher.publish(
        event,
        time=now,
        id=event_uuid,
        exchange_override=expected_exchange,
        routing_key_override=expected_routing_key,
    )
    publisher.publish_to_exchange.assert_called_once_with(
        exchange=expected_exchange,
        routing_key=expected_routing_key,
        event=CloudEventSchema(
            id=event_uuid,
            type='multiverse.sample.event.v1',
            dataschema="https://schema.multiverse.io/sample/event/v1",
            source="test_service",
            data=event,
            time=now,
        ),
    )


def test_consumer_configured_with_dispatchers(mock_pika_client):
    service_config = ServiceConfig(
        service_name="test_service",
        get_exchange_for_entity=lambda e: {'sample': 'samples'}[e] + ".topic_exchange",
    )

    class MockDispatcher:
        def __init__(self, name):
            self.name = name

        def get_consumer_queue_config(self, arg):
            assert arg is service_config
            return self.name

    dispatcher1 = MockDispatcher("A")
    dispatcher2 = MockDispatcher("B")

    consumer = EventConsumer(
        config={"consumers": []},
        rabbitmq_client=mock_pika_client,
        service_config=service_config,
        consumer_providers=[dispatcher1, dispatcher2],
    )
    assert consumer.consumers == ["A", "B"]
