import pydantic
import pytest

from events_tooling_py.config import (
    ConsumerConfig,
    ExchangeType,
    PublisherConfig,
    RabbitMQConfig,
)


def test_rabbitmq_config():
    config = RabbitMQConfig(**{
        "url": "amqp://guest:guest@localhost:5672/",
        "reconnect_attempts": 5,
    })
    assert config.url == "amqp://guest:guest@localhost:5672/"
    assert config.reconnect_attempts == 5


def test_rabbitmq_config_with_default_reconnect_attempts():
    config = RabbitMQConfig(**{
        "url": "amqp://guest:guest@localhost:5672/",
    })
    assert config.url == "amqp://guest:guest@localhost:5672/"
    assert config.reconnect_attempts == 5


def test_rabbitmq_config_with_missing_url():
    with pytest.raises(pydantic.ValidationError) as exc_info:
        RabbitMQConfig(**{"reconnect_attempts": 5})
    errors = exc_info.value.errors()
    assert len(errors) == 1
    assert errors[0]['loc'] == ('url',)
    assert errors[0]['type'] == 'missing'


@pytest.mark.parametrize('exception_handler', (None, lambda: None))
@pytest.mark.parametrize(
    'exchanges',
    (
        [
            {
                "exchange": "foo",
                "exchange_type": ExchangeType.direct,
                "durable": True,
            },
        ],
        [],
        None,
    ),
)
def test_publisher_config(exception_handler, exchanges):
    config = {}
    if exception_handler:
        config["exception_handler"] = exception_handler
    if exchanges:
        config["exchanges"] = exchanges

    config = PublisherConfig(**config)
    assert config.exception_handler == exception_handler

    if not exchanges or not len(exchanges):
        assert config.exchanges == []
    else:
        assert len(config.exchanges) == 1
        assert config.exchanges[0].exchange == "foo"
        assert config.exchanges[0].exchange_type == ExchangeType.direct
        assert config.exchanges[0].durable is True


def test_publisher_config_with_invalid_exchange_type():
    with pytest.raises(pydantic.ValidationError) as exc_info:
        PublisherConfig(**{
            "exchanges": [
                {
                    "exchange": "foo",
                    "exchange_type": "invalid",
                    "durable": True,
                },
            ],
        })

    errors = exc_info.value.errors()
    assert len(errors) == 1
    assert errors[0]['loc'] == ('exchanges', 0, 'exchange_type')
    assert errors[0]['type'] == 'enum'


def test_publisher_config_with_invalid_exchange_attributes():
    with pytest.raises(pydantic.ValidationError) as exc_info:
        PublisherConfig(**{
            "exchanges": [
                {
                    "Exchange": "foo",
                    "ExchangeType": "invalid",
                    "DURABLE": True,
                },
            ],
        })

    errors = exc_info.value.errors()
    assert len(errors) == 3
    assert errors[0]['type'] == 'missing'
    assert errors[0]['loc'] == ('exchanges', 0, 'exchange')
    assert errors[1]['type'] == 'missing'
    assert errors[1]['loc'] == ('exchanges', 0, 'exchange_type')
    assert errors[2]['type'] == 'missing'
    assert errors[2]['loc'] == ('exchanges', 0, 'durable')


def test_consumer_config():
    config = ConsumerConfig(**{
        "exception_handler": None,
        "consumers": [
            {
                "dlq": {
                    "name": "test_dlq",
                    "durable": True,
                    "bindings": [
                        {
                            "exchange": "foo",
                            "routing_key": "bar",
                        },
                    ],
                },
                "queue": {
                    "name": "test_queue",
                    "arguments": {
                        "x-queue-type": "classic",
                        "x-single-active-consumer": True,
                        "x-dead-letter-exchange": "dlq-foo",
                        "x-dead-letter-routing-key": "dlq-bar",
                    },
                    "bindings": [
                        {
                            "exchange": "foo",
                            "routing_key": "bar",
                        },
                    ],
                },
            },
            {
                "queue": {"name": "test_queue_2"},
            },
        ],
    })
    assert config.exception_handler is None
    assert len(config.consumers) == 2

    assert config.consumers[0].dlq.name == "test_dlq"
    assert config.consumers[0].dlq.durable is True
    assert config.consumers[0].dlq.arguments is None
    assert len(config.consumers[0].dlq.bindings) == 1
    assert config.consumers[0].dlq.bindings[0].exchange == "foo"
    assert config.consumers[0].dlq.bindings[0].routing_key == "bar"

    assert config.consumers[0].queue.name == "test_queue"
    assert config.consumers[0].queue.durable is False
    assert config.consumers[0].queue.arguments.x_queue_type == "classic"
    assert config.consumers[0].queue.arguments.x_single_active_consumer is True
    assert config.consumers[0].queue.arguments.x_dead_letter_exchange == "dlq-foo"
    assert config.consumers[0].queue.arguments.x_dead_letter_routing_key == "dlq-bar"
    assert len(config.consumers[0].queue.bindings) == 1
    assert config.consumers[0].queue.bindings[0].exchange == "foo"
    assert config.consumers[0].queue.bindings[0].routing_key == "bar"

    assert config.consumers[1].dlq is None
    assert config.consumers[1].queue.name == "test_queue_2"
    assert config.consumers[1].queue.durable is False
    assert config.consumers[1].queue.arguments is None
    assert config.consumers[1].queue.bindings == []


def test_consumer_config_with_missing_queue():
    with pytest.raises(pydantic.ValidationError) as exc_info:
        ConsumerConfig(**{
            "consumers": [
                {},
            ],
        })

    errors = exc_info.value.errors()
    assert len(errors) == 1
    assert errors[0]['loc'] == ('consumers', 0, 'queue')
    assert errors[0]['type'] == 'missing'
