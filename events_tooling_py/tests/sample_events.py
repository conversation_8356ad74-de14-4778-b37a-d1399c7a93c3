class SampleV1Event:
    def __init__(self, message: str):
        self.message = message

    message: str

    def model_dump(self):
        return {"message": self.message}

    class Meta:
        schema_type = "multiverse.sample.event.v1"
        schema_uri = "https://schema.multiverse.io/sample/event/v1"
        raw_json_schema = {
            "$id": "https://schema.multiverse.io/sample/event/v1",
            "type": "object",
            "properties": {"message": {"type": "string"}},
            "required": ["message"],
        }


class SampleV2Event:
    def __init__(self, message: str):
        self.message = message

    message: str

    def model_dump(self):
        return {"message": self.message}

    class Meta:
        schema_type = "multiverse.sample.event.v2"
        schema_uri = "https://schema.multiverse.io/sample/event/v2"
        raw_json_schema = {
            "$id": "https://schema.multiverse.io/sample/event/v2",
            "type": "object",
            "properties": {"message": {"type": "string"}},
            "required": ["message"],
        }


class SampleV1Event2:
    def __init__(self, message: str):
        self.message = message

    message: str

    def model_dump(self):
        return {"message": self.message}

    class Meta:
        schema_type = "multiverse.sample.event2.v1"
        schema_uri = "https://schema.multiverse.io/sample/event2/v1"
        raw_json_schema = {
            "$id": "https://schema.multiverse.io/sample/event2/v1",
            "type": "object",
            "properties": {"message": {"type": "string"}},
            "required": ["message"],
        }
