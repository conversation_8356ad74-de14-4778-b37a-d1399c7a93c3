import pytest

from events_tooling_py.entity_plurals import (
    entity_plural,
    NonConformingEntityError,
    UnknownEntityError,
)


def test_known_entity_plural():
    assert entity_plural("user") == "users"


def test_unknown_entity_plural():
    with pytest.raises(UnknownEntityError):
        entity_plural("non_existent_entity")


def test_non_conforming_entity_plural():
    with pytest.raises(NonConformingEntityError):
        entity_plural("atlas")
