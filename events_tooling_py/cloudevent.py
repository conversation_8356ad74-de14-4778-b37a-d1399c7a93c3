from datetime import datetime, UTC
import typing
from typing import Any
import uuid

from pydantic import BaseModel, Field, field_validator

from events_tooling_py.types import CloudEventProtocol, EventBase


class CloudEventSchema(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    source: str
    specversion: str = '1.0'
    type: str
    datacontenttype: str = 'application/json'
    dataschema: str
    data: Any
    subject: str | None = None
    time: datetime | None = Field(default_factory=lambda: datetime.now(UTC))

    @field_validator('time')
    def set_time_now(cls, v: datetime | None) -> datetime:
        return v or datetime.now(UTC)


def encode_cloudevent(
    event: EventBase,
    source: str,
    time: datetime | None = None,
    subject: str | None = None,
    id: uuid.UUID | None = None,
) -> CloudEventSchema:
    return CloudEventSchema(
        id=id or uuid.uuid4(),
        type=event.Meta.schema_type,
        dataschema=event.Meta.schema_uri,
        source=source,
        data=event,
        subject=subject,
        time=time or datetime.now(UTC),
    )


if typing.TYPE_CHECKING:
    _: type[CloudEventProtocol] = CloudEventSchema
