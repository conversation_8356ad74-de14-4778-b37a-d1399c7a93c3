from abc import ABC, abstractmethod
from collections.abc import Callable
from datetime import datetime, UTC
from inspect import signature
import json
import logging
import traceback
import typing
from typing import Any, cast, Literal, Protocol
import uuid

from pika.adapters.blocking_connection import BlockingChannel
from pika.exceptions import ChannelClosed, ConnectionClosed, StreamLostError
from pika.exchange_type import ExchangeType
from pika.spec import Basic, BasicProperties, PERSISTENT_DELIVERY_MODE
import pydantic

from events_tooling_py.cloudevent import CloudEventSchema, encode_cloudevent
from events_tooling_py.config import (
    ArgumentsConfig,
    BindingConfig,
    ConsumerConfig,
    ConsumerQueueConfigProvider,
    PublisherConfig,
    QueueConfig,
    ServiceConfig,
)
from events_tooling_py.connection_client import ChannelManager, PikaClient
from events_tooling_py.types import (
    CloudEventHandler,
    CloudEventProtocol,
    DictAny,
    EventBase,
    EventMetadata,
    MessagingError,
    NonRecoverableEventError,
)
from events_tooling_py.utils import (
    dump_cloudevent_json,
    format_message,
    import_string,
    legacy_cloud_event,
    validate_event_against_schema,
)

logger = logging.getLogger(__name__)


def get_delivery_tag(method: Basic.Deliver) -> int:
    """`Basic.Deliver.method` defaults to `None` but `ch.basic_ack` expects an `int` which
    defaults to `0`"""
    return method.delivery_tag or 0


def get_message_identifier(method: Basic.Deliver) -> str:
    return f'[{method.exchange}][{method.routing_key}][{method.delivery_tag}]'


def get_event_handler(raw_event_handler: Any) -> CloudEventHandler:
    if isinstance(raw_event_handler, str):
        callback = import_string(raw_event_handler)
    elif callable(raw_event_handler):
        callback = raw_event_handler
    else:
        raise NotImplementedError(
            f'{raw_event_handler} of {type(raw_event_handler)} is not supported'
        )
    return cast(CloudEventHandler, callback)


def wrap_event_handler(
    handler: CloudEventHandler,
    exception_handler: Callable | None = None,
    delivery_limit: int = 0,
) -> Callable:
    sig = signature(handler)
    needs_metadata = len(sig.parameters) == 2

    def callback(
        channel: BlockingChannel,
        method: Basic.Deliver,
        properties: BasicProperties,
        body: bytes,
    ) -> None:
        delivery_tag = get_delivery_tag(method)
        message_identifier = get_message_identifier(method)

        try:
            message = json.loads(body)
            logger.info(
                f'{message_identifier} Received message: {format_message(message)}'
            )
            cloudevent: CloudEventProtocol
            try:
                # Attempt to extract the message contents from the CloudEvent envelope
                cloudevent = CloudEventSchema(**message)
            except pydantic.ValidationError:
                # Event isn't a CloudEvent, so just use the message and set the
                # type to the routing key because that is what determines the
                # message type.
                cloudevent = legacy_cloud_event(message, method.routing_key)
            if needs_metadata:
                headers = properties.headers or {}
                metadata = EventMetadata(
                    content_type=properties.content_type,
                    app_id=properties.app_id,
                    timestamp=properties.timestamp,
                    schema=headers.get('schema', None),
                    entity_type=headers.get('entity_type', None),
                    entity_id=headers.get('entity_id', None),
                    timestamp_microseconds=headers.get('timestamp_microseconds', None),
                    log_type=headers.get('log_type', None),
                    partition_key=headers.get('partition_key', None),
                )
                handler(cloudevent, metadata)  # type: ignore[call-arg]
            else:
                handler(cloudevent)  # type: ignore[call-arg]
            logger.info(f'{message_identifier} Successfully processed message')
            channel.basic_ack(delivery_tag=delivery_tag)
        except Exception as e:
            if exception_handler:
                exception_handler(e)
            logger.warning(
                f"{message_identifier} Failed to process message: {traceback.format_exc()}"
            )
            bad_event = isinstance(e, NonRecoverableEventError)

            # A message can be requeued if it hasn't exceeded the delivery
            # limit, there is a handler defined for it and its payload is valid.
            requeue = (
                delivery_limit > 0
                and not bad_event
                and properties.headers is not None
                and properties.headers.get('x-delivery-count', 0) < delivery_limit
            )
            channel.basic_nack(
                delivery_tag=delivery_tag,
                requeue=requeue,
            )

            # If the error is not due to a bad event, we don't know how
            # to recover from it so just re-raise it.
            if not bad_event:
                raise

    return callback


class EventClient(ABC):
    """Abstract base class for an event client."""

    @abstractmethod
    def setup(self) -> None:
        """Setup the event client."""
        raise NotImplementedError


class EventConsumer(EventClient):
    """Event consumer implementation."""

    def __init__(
        self,
        *,
        config: DictAny = {},
        rabbitmq_client: PikaClient,
        service_config: ServiceConfig | None = None,
        consumer_providers: list[ConsumerQueueConfigProvider] | None = None,
    ) -> None:
        super().__init__()
        consumer_config = ConsumerConfig(**config)
        self.rabbitmq = rabbitmq_client
        self.consumers = consumer_config.consumers
        if consumer_providers:
            if service_config is None:
                raise ValueError(
                    "service_config is required when config contains consumer_providers"
                )
            self.consumers.extend(
                c.get_consumer_queue_config(service_config) for c in consumer_providers
            )
        self.exception_handler = (
            service_config and service_config.exception_handler
        ) or consumer_config.exception_handler

    def _declare_exchanges(
        self,
        exchanges: set[str],
        exchange_type: ExchangeType,
    ) -> None:
        """Declare the exchanges used by the consumer."""
        for exchange in exchanges:
            self.rabbitmq.channel.exchange_declare(
                exchange=exchange,
                exchange_type=exchange_type,
                durable=True,
            )
            logger.info(f'{exchange} exchange declared...')

    def get_bindings(self, dlq_only: bool = False) -> list[BindingConfig]:
        """Extract queue bindings from the config."""
        bindings = []
        for conf in self.consumers:
            queue = conf.dlq if dlq_only else conf.queue
            if queue and queue.bindings:
                bindings.extend(queue.bindings)
        return bindings

    def declare_exchanges(self, dead_letter_only: bool = False) -> None:
        """Declare the exchanges used by the consumer."""
        dlq_exchanges = {
            binding.exchange for binding in self.get_bindings(dlq_only=True)
        }
        self._declare_exchanges(dlq_exchanges, ExchangeType.direct)
        if dead_letter_only:
            return
        exchanges = {binding.exchange for binding in self.get_bindings()}
        self._declare_exchanges(exchanges, ExchangeType.topic)

    def bind_queue_to_exchange(
        self,
        queue: str,
        binding: BindingConfig,
    ) -> None:
        """Bind a queue to an exchange."""
        self.rabbitmq.channel.queue_bind(
            exchange=binding.exchange,
            routing_key=binding.routing_key,
            queue=queue,
        )

    def queue_declare(
        self,
        queue: str,
        durable: bool = False,
        arguments: ArgumentsConfig | None = None,
    ) -> None:
        """Declare a queue."""
        dict_arguments = None
        if arguments:
            dict_arguments = arguments.model_dump(  # type: ignore[no-redef]
                by_alias=True,
                exclude_none=True,
            )
        self.rabbitmq.channel.queue_declare(
            queue=queue,
            durable=durable,
            arguments=dict_arguments,
        )

    def declare_queue_config(self, queue: QueueConfig) -> None:
        self.queue_declare(
            queue=queue.name,
            durable=queue.durable,
            arguments=queue.arguments,
        )
        if queue.bindings:
            for binding in queue.bindings:
                self.bind_queue_to_exchange(queue.name, binding)
        if queue.handler:
            handler = get_event_handler(queue.handler)
            self.rabbitmq.channel.basic_consume(
                queue=queue.name,
                on_message_callback=wrap_event_handler(
                    handler=handler,
                    exception_handler=self.exception_handler,
                    delivery_limit=queue.delivery_limit,
                ),
                auto_ack=queue.auto_ack,
            )

    def declare_and_bind_queues(self) -> None:
        """Declare and bind the queues used by the consumers."""
        for consumer_conf in self.consumers:
            dlq = consumer_conf.dlq
            if dlq:
                self.declare_queue_config(dlq)
            self.declare_queue_config(consumer_conf.queue)

    def start_consuming(self, max_retries: int = 5) -> None:
        """Start consuming messages from the queues."""
        while True:
            try:
                self.rabbitmq.channel.start_consuming()
            except (ChannelClosed, ConnectionClosed) as e:
                if max_retries == 0:
                    logger.error(
                        f"Channel/Connection was closed({e}) - Max retry attempts reached - stop consuming"
                    )
                    raise MessagingError('Unable to consume events') from e
                logger.info(
                    f"Channel/Connection was closed({e}), trying to reestablish connection"
                )
                # We need to register all the handlers again, doing that by
                # redeclaring all queues.  Probably would be better to
                # decouple declaring queues and registering consumers.
                self.declare_and_bind_queues()
                max_retries -= 1

    def setup(
        self,
        declare_exchanges: bool | Literal["dead-letter-only"] = False,
        start_consuming: bool = True,
    ) -> None:
        """Setup the event consumer by connecting to RabbitMQ and then declaring and binding the queues.
        If `declare_exchanges` is set to True, the exchanges that the event consumers bind to will also be
        declared. WARNING only use `declare_exchanges=True` in local development.
        """
        self.rabbitmq.connect()
        if declare_exchanges:
            logger.warning(
                'Declaring exchanges: Note: this option is only meant for local development!!!'
            )
            self.declare_exchanges(
                dead_letter_only=declare_exchanges == 'dead-letter-only'
            )

        self.declare_and_bind_queues()
        if start_consuming:
            self.start_consuming()


class BasicPublisher(Protocol):
    """
    A protocol for a class that can publish messages to an exchange.
    """

    def basic_publish(
        self, exchange: str, routing_key: str, body: str, properties: dict
    ) -> None:
        '''
        Publish a message to an exchange using the given routing_key and
        properties as defined in the RabbitMQ spec.

        Implementations may actually publish the message or for example just
        queue the publication.
        '''
        ...


class EventPublisherOnly:
    """
    Event publisher implementation.

    Instances of this class can turn multiverse events into RabbitMQ messages,
    then use a BasicPublisher to publish the message.
    """

    def __init__(
        self,
        *,
        basic_publisher: BasicPublisher,
        service_config: ServiceConfig | None = None,
        event_schemas: list[type[EventBase]] | None = None,
    ):
        self.basic_publisher = basic_publisher
        self.service_config = service_config
        self.registered_event_schemas = set(event_schemas) if event_schemas else set()

    def basic_publish_cloudevent(
        self, exchange: str, routing_key: str, event: CloudEventProtocol
    ) -> None:
        body = dump_cloudevent_json(event)
        event_time = event.time or datetime.now(UTC)
        event_timestamp = event_time.timestamp()
        event_epoch = int(event_timestamp)
        event_epoch_microseconds = int(event_epoch * 1_000_000)

        headers = {
            "schema": event.dataschema,
            "timestamp_microseconds": event_epoch_microseconds,
        }
        if event.type.startswith('multiverse.'):
            event_entity = event.type.split('.', maxsplit=3)[1]
            headers["entity_type"] = event_entity
        if hasattr(event.data, 'id'):
            headers["entity_id"] = str(event.data.id)

        self.basic_publisher.basic_publish(
            exchange=exchange,
            routing_key=routing_key,
            body=body,
            properties=dict(
                content_type='application/json',
                timestamp=event_epoch,
                app_id=event.source,
                delivery_mode=PERSISTENT_DELIVERY_MODE,
                headers=headers,
            ),
        )

    def publish_to_exchange(
        self,
        exchange: str,
        routing_key: str,
        event: CloudEventProtocol,
    ) -> None:
        self.basic_publish_cloudevent(exchange, routing_key, event)

    def publish(
        self,
        event: EventBase,
        time: datetime | None = None,
        subject: str | None = None,
        id: uuid.UUID | None = None,
        routing_key_override: str | None = None,
        exchange_override: str | None = None,
    ) -> None:
        '''
        Publish an event to RabbitMQ, using the Multiverse RabbitMQ conventions.
        Uses published_to_exchanges.

        Args:
            event: The event to publish
            time: Optional time to use for the CloudEvent
            subject: Optional subject to use for the CloudEvent
            id: Optional ID to use for the CloudEvent
            routing_key_override: Optional routing key to use instead of the default
            exchange_override: Optional exchange to use instead of the default
        '''
        if self.service_config is None:
            raise ValueError('service_config is required to publish events')
        if type(event) not in self.registered_event_schemas:
            raise ValueError(f'type of event {event} has not been registered')

        # Validate against schema.
        # If event.Meta.raw_json_schema is not present, an AttributeError will be raised.
        # If event.Meta.raw_json_schema is present but None or an empty dict,
        # validate_event_against_schema will handle it by skipping validation.
        service_name = (
            self.service_config.service_name if self.service_config else None
        )
        validate_event_against_schema(
            event, event.Meta.raw_json_schema, service_name
        )

        # Publish the event
        self._publish_to_exchange(
            event, time, subject, id, routing_key_override, exchange_override
        )

    def _publish_to_exchange(
        self,
        event: EventBase,
        time: datetime | None = None,
        subject: str | None = None,
        id: uuid.UUID | None = None,
        routing_key_override: str | None = None,
        exchange_override: str | None = None,
    ) -> None:
        """Internal method to publish an event to an exchange."""
        if self.service_config is None:
            raise ValueError('service_config is required to publish events')
        pub_data = self.service_config.get_event_publishing_data(type(event))

        exchange = pub_data.exchange
        if exchange_override is not None:
            exchange = exchange_override

        self.publish_to_exchange(
            exchange=exchange,
            routing_key=routing_key_override or pub_data.routing_key,
            event=encode_cloudevent(
                event,
                source=self.service_config.service_name if self.service_config else "",
                time=time,
                subject=subject,
                id=id,
            ),
        )


class RabbitMQPublisherConfigurator(EventClient):
    """
    A class to configure the RabbitMQ exchanges used by a publisher given the
    service configuration.
    """

    def __init__(
        self,
        *,
        channel_manager: ChannelManager,
        config: DictAny = {},
        service_config: ServiceConfig | None = None,
        event_schemas: list[type[EventBase]] | None = None,
    ):
        self.channel_manager = channel_manager
        self.config = PublisherConfig(**config)
        self.service_config = service_config
        self.event_schemas = event_schemas

    def declare_exchanges(self) -> None:
        """Declare the exchanges used by the publishers."""
        if not self.config.exchanges:
            return
        for exchange_conf in self.config.exchanges:
            self.channel_manager.channel.exchange_declare(
                exchange=exchange_conf.exchange,
                exchange_type=exchange_conf.exchange_type,
                durable=exchange_conf.durable,
            )

    def declare_exchanges_for_event_schemas(self) -> None:
        if not self.event_schemas:
            return
        if self.service_config is None:
            raise ValueError(
                'service_config is required to declare exchanges for event schemas'
            )
        for event_schema in self.event_schemas:
            pub_data = self.service_config.get_event_publishing_data(event_schema)
            logger.info(f"Declaring exchange {pub_data.exchange}")
            self.channel_manager.channel.exchange_declare(
                exchange=pub_data.exchange,
                exchange_type=ExchangeType.topic,
                durable=True,
            )

    def setup(self) -> None:
        """Setup the event publisher by connecting to RabbitMQ and then declaring the exchanges."""
        self.declare_exchanges()
        self.declare_exchanges_for_event_schemas()


class EventPublisher(RabbitMQPublisherConfigurator, EventPublisherOnly):
    """
    Event publisher implementation.

    Publishers have their own channel so that they can multiplex over one single
    connection.
    """

    def __init__(
        self,
        *,
        config: DictAny = {},
        rabbitmq_client: PikaClient,
        service_config: ServiceConfig | None = None,
        event_schemas: list[type[EventBase]] | None = None,
        max_retry_count: int = 5,
    ) -> None:
        self.rabbitmq_client = rabbitmq_client
        publisher_channel = rabbitmq_client.new_channel_manager()
        RabbitMQPublisherConfigurator.__init__(
            self,
            channel_manager=publisher_channel,
            config=config,
            service_config=service_config,
            event_schemas=event_schemas,
        )
        EventPublisherOnly.__init__(
            self,
            basic_publisher=RabbitMQBasicPublisher(publisher_channel, max_retry_count),
            service_config=service_config,
            event_schemas=event_schemas,
        )

    def setup(self) -> None:
        self.rabbitmq_client.connect()
        super().setup()


class RabbitMQBasicPublisher:
    '''
    Implementation of BasicPublisher that actually publishes messages to RabbitMQ.
    '''

    def __init__(self, channel_manager: ChannelManager, max_retry_count: int = 5):
        self.channel_manager = channel_manager
        self.max_retry_count = max_retry_count

    def basic_publish(
        self, exchange: str, routing_key: str, body: str, properties: dict
    ) -> None:
        retries_left = self.max_retry_count
        while True:
            try:
                self.channel_manager.channel.basic_publish(
                    exchange, routing_key, body, BasicProperties(**properties)
                )
                logger.info(
                    f'Sent message. Exchange: {exchange}, Routing Key: {routing_key}, Body: {format_message(body)}'
                )
                break
            except (StreamLostError, ConnectionClosed, ChannelClosed) as e:
                if retries_left == 0:
                    logger.error(
                        f'Max retry attempts reached - message not published: {format_message(body)}'
                    )
                    raise MessagingError('Unable to publish message') from e
                retries_left -= 1
                logger.warning(
                    f'StreamLostError: perhaps a heartbeat was missed? Retries left: {retries_left}: {e}'
                )
                self.channel_manager.client.close()


if typing.TYPE_CHECKING:
    _: type[BasicPublisher] = RabbitMQBasicPublisher
