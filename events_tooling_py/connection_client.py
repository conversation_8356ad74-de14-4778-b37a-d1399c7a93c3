from abc import ABC, abstractmethod
import logging
import time
from typing import Any

from pika.adapters.blocking_connection import BlockingChannel, BlockingConnection
from pika.connection import URLParameters
from pika.exceptions import AMQPConnectionError

from events_tooling_py.config import (
    RabbitMQConfig,
)
from events_tooling_py.types import DictAny

logger = logging.getLogger(__name__)

INITIAL_RECONNECT_BACKOFF_SECONDS = 2


class RabbitMQClient(ABC):
    """Abstract base class for a RabbitMQ client."""

    connection: Any = None
    channel: Any = None

    @abstractmethod
    def connect(self) -> None:
        """Connect to RabbitMQ."""
        raise NotImplementedError

    @abstractmethod
    def close(self) -> None:
        """Close the connection to RabbitMQ."""
        raise NotImplementedError


class PikaClient(RabbitMQClient):
    """RabbitMQ client implementation using the Pika library."""

    _channel: BlockingChannel | None
    _connection: BlockingConnection | None

    def __init__(self, *, config: DictAny) -> None:
        super().__init__()
        self.config = RabbitMQConfig(**config)
        self._channel = None
        self._connection = None
        self._extra_channels: list[ChannelManager] = []

    @property
    def connection(self) -> BlockingConnection:
        """
        Returns a BlockingConnection, with every effort made to guarantee it is
        open.  If not, a new connection is created.
        """
        connection = self._connection
        if connection is None or not connection.is_open:
            connection = self._connection = self._connect()
            self._channel = None
        return connection

    @property
    def channel(self) -> BlockingChannel:
        """
        Returns a the channel for this client, with every effort made to
        guarantee it is open (and so is the underlying connection).  If not, a
        new connection / channel is created.
        """
        channel = self._channel
        if channel is None or not channel.is_open:
            channel = self._channel = self.connection.channel()
        return channel

    def new_channel_manager(self) -> "ChannelManager":
        '''
        Create a new channel manager that is managed by the client.
        '''
        new_channel = ChannelManager(self)
        self._extra_channels.append(new_channel)
        return new_channel

    def connect(self) -> None:
        self.connection

    def _connect(self) -> BlockingConnection:
        """Connect to RabbitMQ using exponential backoff if retries are required."""
        attempts = 0
        backoff = INITIAL_RECONNECT_BACKOFF_SECONDS
        while True:
            try:
                logger.info(f'Connecting to RabbitMQ ({self.config.url})...')
                url_params = URLParameters(self.config.url)
                connection = BlockingConnection(url_params)
                if connection.is_open:
                    logger.info('Successfully connected to RabbitMQ')
                    return connection
            except (AMQPConnectionError, Exception) as e:
                attempts += 1
                if attempts == self.config.reconnect_attempts:
                    logger.error(
                        f'Reached max retry attempts: {self.config.reconnect_attempts}. exiting...'
                    )
                    raise AMQPConnectionError(e)

                # Perform an exponential backoff before retrying the connection
                logger.warning(
                    f'Waiting {backoff} seconds before retrying connection...'
                )
                time.sleep(backoff)
                backoff *= 2

    def close(self) -> None:
        """Close the channel and connection to RabbitMQ."""
        if self._channel is not None and self.channel.is_open:
            self.channel.close()
            self._channel = None
        for channel in self._extra_channels:
            channel.close()
        if self._connection is not None and self._connection.is_open:
            self.connection.close()
            self._connection = None


class ChannelManager:
    """A class to manage a channel for a Pika client."""

    _channel: BlockingChannel | None
    _closed: bool

    def __init__(self, client: PikaClient) -> None:
        self.client = client
        self._channel = None
        self._closed = False

    @property
    def channel(self) -> BlockingChannel:
        """
        Returns a the channel for this client, with every effort made to
        guarantee it is open (and so is the underlying connection).  If not, a
        new connection / channel is created.
        """
        if self._closed:
            raise RuntimeError('Channel is closed')
        channel = self._channel
        if channel is None or not channel.is_open:
            channel = self._channel = self.client.connection.channel()
        return channel

    def close(self) -> None:
        if self._closed:
            return
        if self._channel is not None and self._channel.is_open:
            self._channel.close()
        self._channel = None
