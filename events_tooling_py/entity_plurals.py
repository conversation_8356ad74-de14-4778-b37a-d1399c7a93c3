class EntityError(ValueError):
    def __init__(self, entity_name: str) -> None:
        self.entity_name = entity_name

    def __repr__(self) -> str:
        return f"{type(self).__name__}({self.entity_name!r})"


class UnknownEntityError(EntityError):
    def __str__(self) -> str:
        return f"plural for entity {self.entity_name!r} not defined - please define it in entity_plurals.py"


class NonConformingEntityError(EntityError):
    def __str__(self) -> str:
        return f"entity {self.entity_name!r} does not conform - use legacy config"


ENTITY_PLURALS: dict[str, str | type[EntityError]] = {
    "account": "accounts",
    "apprenticeship": "apprenticeships",
    #
    # Entity is plural already
    "apprenticeship_applications": "apprenticeship_applications",
    #
    # doesn't conform (multiple entities)
    # https://github.com/Multiverse-io/atlas/blob/8582bb85633583c3b54c470419374a5d1e0dc368/config/config.exs#L232
    "atlas": NonConformingEntityError,
    #
    # Exchange doesn't use plural
    "authentication": "authentication",
    #
    "calendar_invite": "calendar_invites",
    #
    # Exchange doesn't use plural
    "checker_result": "checker_result",
    #
    "company": "companies",
    "company_membership": "company_memberships",
    "competency": "competencies",
    "competency_domain": "competency_domains",
    "competency_framework": "competency_frameworks",
    "competency_grouping": "competency_groupings",
    "competency_module": "competency_modules",
    "curriculum": "curricula",
    "customer_diagnosis": "customer_diagnoses",
    "customer_goal": "customer_goals",
    #
    # Exchange doesn't use plural
    "dw_api": "dw_api",
    #
    # Exchange uses a different name altogether
    # https://github.com/Multiverse-io/hello_app/blob/main/src/server/rabbitmq/types.ts
    "embedded_meeting": "hello_app",
    #
    "enabler": "enablers",
    #
    # This doesn't follow the conventions for routing keys (allocation_case is used instead of enabler_allocation_case)
    # https://github.com/Multiverse-io/enabler-allocation-engine/blob/main/config/config.exs#L106C16-L106C32
    "enabler_allocation_case": NonConformingEntityError,
    #
    "evidence": "evidences",
    "feedback_form": "feedback_forms",
    "feedback_form_submission": "feedback_form_submissions",
    #
    # Doesn't seem to conform - the exchange is
    # intervention_changelog.topic_exchange and I think the routing key is
    # intervention_changelog.
    # https://github.com/Multiverse-io/learner_risk/blob/main/lib/learner_risk/events/intervention_changelog/publisher.ex
    "intervention": NonConformingEntityError,
    #
    "ksb": "ksbs",
    "ksb_grouping": "ksb_groupings",
    "learning_domain": "learning_domains",
    "learning_objective": "learning_objectives",
    "pathway": "pathways",
    "project": "projects",
    "project_review": "project_reviews",
    #
    #  Exchange does not conform
    # https://github.com/Multiverse-io/companies_service/blob/main/config/config.exs#L89
    "salesforce_company": "companies",
    #
    "skillset": "skillsets",
    "support_factor": "support_factors",
    "support_factor_snapshot": "support_factor_snapshots",
    "sync_session": "sync_sessions",
    "tool": "tools",
    "unit": "units",
    "unit_template": "unit_templates",
    "user": "users",
    "user_goal": "user_goals",
    "user_skills_scan": "user_skills_scans",
    "knowledge_check": "knowledge_checks",
    "learning_content_draft": "learning_content_drafts",
    #
    # Exchange does not conform
    # https://github.com/Multiverse-io/learner_risk/blob/main/config/config.exs#L61C29-L61C50
    "withdrawal_risk_prediction": "withdrawal_risk_model",
    #
    "uk_apprenticeship_standard": "uk_apprenticeship_standards",
    "qualification": "qualifications",
}


def entity_plural(entity_name: str) -> str:
    try:
        plural_or_error = ENTITY_PLURALS[entity_name]
    except KeyError:
        raise UnknownEntityError(entity_name)
    if isinstance(plural_or_error, str):
        return plural_or_error
    raise plural_or_error(entity_name)
