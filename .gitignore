# macOS
.DS_store

# Python bytecode
*.pyc
__pycache__/

# Python distribution / packages
*.egg-info/
*.egg
pip-wheel-metadata/

# Generic log output
*.log

# Virtualenv
/.venv*

# py.test
.cache
.pytest_cache/

# mypy
.mypy_cache/

# cache-loader
.cache-loader/

# Celery ev pid
*.pid

# jupyter / ipython notebooks
.ipynb_checkpoints/

# editors
.vscode/
.vs/
.vim/
.idea/

# PEM files (e.g. local cloudfront key)
*.pem

# Build directory

build/