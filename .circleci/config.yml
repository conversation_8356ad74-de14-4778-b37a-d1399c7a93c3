# Use the latest 2.1 version of CircleCI pipeline process engine.
# See: https://circleci.com/docs/configuration-reference
version: 2.1

orbs:
  upload-techdocs: multiverse/upload-techdocs@1.1.0

commands:
  install_python_dependencies:
    description: "Install poetry and dependencies"
    steps:
      - restore_cache:
          key: py-deps-{{ .Branch }}-{{ .Environment.CACHE_VERSION }}-{{ checksum "poetry.lock" }}
      - run:
          name: Install poetry and dependencies
          command: |
            echo "${MULTIVERSE_DEPLOYER_SSH_KEY_ENCODED}" > ~/.ssh/encoded_id_key
            base64 -d -i ~/.ssh/encoded_id_key > ~/.ssh/id_rsa
            eval $(ssh-agent)
            ssh-add ~/.ssh/id_rsa
            if ! command -v poetry &> /dev/null || [[ "$(poetry --version | grep -oP '\d+\.\d+\.\d+')" != "1.8.3" ]]; then
              poetry self update --no-ansi 1.8.3
            fi
            poetry install --no-ansi
      - save_cache:
          key: py-deps-{{ .Branch }}-{{ .Environment.CACHE_VERSION }}-{{ checksum "poetry.lock" }}
          paths:
            - ~/project/.venv


jobs:
  lint:
    docker:
      - image: cimg/python:3.12.5
    steps:
      - checkout
      - install_python_dependencies
      - run:
          name: Lint with ruff check .
          command: poetry run ruff check .
      - run:
          name: Lint with ruff format --check .
          command: poetry run ruff format . --check
      - run:
          name: Check with mypy
          command: poetry run mypy 
      - run:
          name: Security check with bandit (python)
          command: poetry run bandit -r events_tooling_py -ll --skip B308,B703 # Takes ~1:30
  
  tests-unit:
    docker:
      - image: cimg/python:3.12.5
    steps:
      - checkout
      - install_python_dependencies
      - run:
          name: Run tests with pytest
          command: |
            poetry run py.test events_tooling_py \
              --ignore=events_tooling_py/requirement_tests \
              --color=yes \
              --tb=short \
              --reruns 2 --reruns-delay 3 \
              --capture=sys \
              -lvv \
              --junitxml=/tmp/test-results/results.xml
      - store_test_results:
          path: /tmp/test-results

  requirement-tests:
    docker:
      - image: cimg/python:3.12.5
      - image: rabbitmq:3.13.7-management
    steps:
      - checkout
      - install_python_dependencies
      - run:
          name: Run requirement tests
          command: |
            until curl -f -s -o /dev/null **********************************/api/aliveness-test/%2F; do
              echo Waiting for RabbitMQ...
              sleep 2
            done
            echo RabbitMQ OK
            source $(poetry env info --path)/bin/activate
            poetry run pytest events_tooling_py/requirement_tests \
              -n 10 \
              --color=yes \
              --tb=short \
              --reruns 2 --reruns-delay 3 \
              --capture=sys \
              -lvv \
              --junitxml=/tmp/test-results/requirement-results.xml
      - store_test_results:
          path: /tmp/test-results


workflows:
  test:
    jobs:
      - lint:
          context:
            - multiverse_deployer_ssh_key

      - tests-unit:
          context:
            - multiverse_deployer_ssh_key
          requires:
            - lint
      - requirement-tests:
          context:
            - multiverse_deployer_ssh_key
          requires:
            - lint
      - upload-techdocs/upload_techdocs:
          requires:
            - tests-unit
          repo_name: events_tooling_py
          filters:
            branches:
              only:
                - main
