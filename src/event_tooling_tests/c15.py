from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import (
    declare_topic_exchange,
    wait_for_api_to_catch_up,
)

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r23&view=center


class C15Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events 'multiverse.foo.changelog.v2', service name
       'test_c15'.  The consumer queue should be named
       'test_c15.foo_events.queue` and should define the dead letter queue /
       routing key as per MV conventions

    2. print "started"

    The test will check that the dead letter exchange is correctly configured
    for the queue.
    """

    requirement_code = "C-15"
    test_object_prefix = "req_c15"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")

        async with ctx.proc as proc:
            # Wait till the consumer is started
            await proc.expect("started", timeout=1)

            await wait_for_api_to_catch_up()

            # Check the consumer queue
            queue = await ctx.vhost.queue(
                "test_c15.foo_events.queue", disable_stats=False
            )

            assert (
                queue.arguments["x-dead-letter-exchange"]
                == "default-dead-letter-exchange"
            )
            assert (
                queue.arguments["x-dead-letter-routing-key"]
                == "test_c15.foo_events.dead_letter_queue"
            )

            # Check the dlq has been created.
            await ctx.vhost.queue("test_c15.foo_events.dead_letter_queue")

            # Check the queue binding has been created
            dlq_bindings = await ctx.vhost.bindings(
                from_exchange="default-dead-letter-exchange",
                to_queue="test_c15.foo_events.dead_letter_queue",
            )

            assert len(dlq_bindings) == 1
            dlq_binding = dlq_bindings[0]
            assert dlq_binding.routing_key == "test_c15.foo_events.dead_letter_queue"
