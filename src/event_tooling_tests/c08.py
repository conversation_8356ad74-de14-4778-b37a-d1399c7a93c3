from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import (
    declare_topic_exchange,
    publish_event,
    wait_for_api_to_catch_up,
)

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r16&view=modal


class C08Test:
    """
    The test object for this requirement should do this:

    1. setup two consumers for events 'multiverse.foo.changelog.v1' and 'multiverse.bar.changelog.v1

    2. Print "ready"

    3. Wait for "stop consumer 1" on stdin, stop the consumer for 'multiverse.foo.changelog.v1'
       and print "consumer 1 stopped" on stdout

    4. Wait for "restart consumer 1" on stdin and restart the consumer for 'multiverse.foo.changelog.v1'
    """

    requirement_code = "C-08"
    test_object_prefix = "req_c08"

    async def run(self, ctx: TestContext) -> None:
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")
        await declare_topic_exchange(ctx.vhost, "bars.topic_exchange")

        async with ctx.proc as proc:
            await proc.expect("ready", timeout=1)

            # Ensure that there are two active consumers
            await wait_for_api_to_catch_up()
            queue1 = await ctx.vhost.queue(
                "test_c08.foo_events.queue", disable_stats=False
            )
            queue2 = await ctx.vhost.queue(
                "test_c08.bar_events.queue", disable_stats=False
            )
            assert len(queue1.consumer_details) == 1
            assert len(queue2.consumer_details) == 1

            # Instruct the test object to stop consumer 1
            await proc.send("stop consumer 1")
            # Wait for consumer 1 to stop
            await proc.expect("consumer 1 stopped")

            # Publish an event to the exchange that consumer 1 was consuming from
            await publish_event(
                ctx.vhost,
                exchange="foos.topic_exchange",
                routing_key="foo.changelog",
                event={"id": "123"},
                source="/req_c08",
                type="multiverse.foo.changelog.v1",
                dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                entity_type="foo",
            )

            # Ensure that the event was not consumed
            await wait_for_api_to_catch_up()
            queue1 = await ctx.vhost.queue(
                "test_c08.foo_events.queue", disable_stats=False
            )
            assert len(queue1.consumer_details) == 0
            assert queue1.messages_ready == 0

            # Check that consumer 2 is still active
            queue2 = await ctx.vhost.queue(
                "test_c08.bar_events.queue", disable_stats=False
            )
            assert len(queue2.consumer_details) == 1

            # Instruct the test object to restart consumer 1
            await proc.send("restart consumer 1")

            # Check that consumer 1 is active again and there is one message ready in the queue
            await wait_for_api_to_catch_up()
            queue1 = await ctx.vhost.queue(
                "test_c08.foo_events.queue", disable_stats=False
            )
            assert len(queue1.consumer_details) == 1
            assert queue1.messages_ready == 1
