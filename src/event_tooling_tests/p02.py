from event_tooling_test_engine.errors import RabbitMQNotFound
from event_tooling_test_engine.test_context_protocol import TestContext
import json
import uuid


class P02Test:
    """
    Test automatic payload validation against schema when publishing.  The test object should

    1. Start a publisher for 'multiverse.foo.changelog.v2'
    2. Print "ready"
    3. expect event payloads on stdin and publish them
      - if the message is invalid, print "validation_error_raised"
      - if the message is valid, print "message_published"
    """

    requirement_code = "P-02"
    test_object_prefix = "req_p02"

    async def run(self, ctx: TestContext) -> None:
        # Check the exchange doesn't exist initially
        try:
            await ctx.vhost.exchange("foos.topic_exchange")
        except RabbitMQNotFound:
            pass
        else:
            assert False, "Exchange not expected to exist yet"

        async with ctx.proc as proc:
            # Wait for ready signal
            await proc.expect("ready", timeout=1)

            # Create the queue and bind it to the exchange
            await ctx.vhost.ensure_queue("test_queue")
            await ctx.vhost.ensure_binding(
                from_exchange="foos.topic_exchange",
                to_queue="test_queue",
                routing_key="foo.changelog",
            )

            # Create invalid payload that doesn't match the schema
            invalid_payload = {
                "id": "123",  # Invalid UUID format
                "name": 123,  # Wrong type (number instead of string)
            }

            # Send invalid payload and expect validation error
            await proc.send(json.dumps(invalid_payload))
            await proc.expect("validation_error_raised", timeout=1)

            # Create and send valid payload
            valid_payload = {
                "id": str(uuid.uuid4()),
                "name": "Test foo",
            }

            # Send valid payload and expect successful publish
            await proc.send(json.dumps(valid_payload))
            await proc.expect("message_published")

            messages = await ctx.vhost.get_messages("test_queue", count=10)

            assert len(messages) == 1
