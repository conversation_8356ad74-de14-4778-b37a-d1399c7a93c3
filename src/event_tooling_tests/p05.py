from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import wait_for_api_to_catch_up

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r5&view=modal


class P05Test:
    """
    The test object for this test should do this:

    1. Start an event publisher that will publish multiple events using a single channel
    2. Print "started"
    3. Expect "ready" on stdin
    4. Publish multiple events in a loop for >5 seconds
    5. Print number of events sent
    6. Print "finished" when done
    """

    requirement_code = "P-05"
    test_object_prefix = "req_p05"

    async def run(self, ctx: TestContext) -> None:
        # Check no connections or channels exist initially
        connections = await ctx.vhost.connections()
        channels = await ctx.vhost.channels()
        assert not len(connections), "No connections are expected to exist yet"
        assert not len(channels), "No channels are expected to exist yet"

        async with ctx.proc as proc:
            # Wait till the publisher is set up
            await proc.expect("started")

            await wait_for_api_to_catch_up()

            # Get initial connection and channel counts
            initial_connections = await ctx.vhost.connections()
            initial_channels = await ctx.vhost.channels()
            assert len(initial_connections) == 1, "1 connection is expected to exist"
            assert len(initial_channels) == 1, "1 channel is expected to exist"

            # Notify the test object to start publishing
            await proc.send("ready")

            await wait_for_api_to_catch_up()

            # Check final connection and channel counts
            final_connections = await ctx.vhost.connections()
            final_channels = await ctx.vhost.channels()

            assert len(final_connections) == 1, "Should maintain exactly one connection"
            assert len(final_channels) == 1, "Should maintain exactly one channel"
