import asyncio
from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import (
    declare_topic_exchange,
    publish_event,
    wait_for_api_to_catch_up,
)


# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r10&view=modal


class C02Test:
    """
    The test object for this requirement should do this:

    1. Set up a consumer for events of type 'multiverse.foo.changelog.v1',
       'multiverse.bar.changelog.v1', 'multiverse.baz.changelog.v1' (so they will
       happen on different queues).  The service name is "test_c02", but do not
       start consuming messages.

    2. Print "ready" on stdout

    3. wait for "consume" on stdin

    4. Start consuming.  The consumer for each queue should do blocking work for
       20ms for each message before acknowledging it.
    """

    requirement_code = "C-02"
    test_object_prefix = "req_c02"

    async def run(self, ctx: TestContext) -> None:
        # Set up the topic exchanges

        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")
        await declare_topic_exchange(ctx.vhost, "bars.topic_exchange")
        await declare_topic_exchange(ctx.vhost, "bazs.topic_exchange")

        # With the service running
        async with ctx.proc as test_proc:
            # Wait for service to be configured
            await test_proc.expect("ready")

            # Publish 200 messages of each type
            for i in range(200):
                await publish_event(
                    ctx.vhost,
                    exchange="foos.topic_exchange",
                    routing_key="foo.changelog",
                    event={"id": "123"},
                    source="/req_c02",
                    type="multiverse.foo.changelog.v1",
                    dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                    entity_type="foo",
                )
                await publish_event(
                    ctx.vhost,
                    exchange="bars.topic_exchange",
                    routing_key="bar.changelog",
                    event={"id": "123"},
                    source="/req_c02",
                    type="multiverse.bar.changelog.v1",
                    dataschema="https://schema.multiverse.io/bar/changelog/1.0.0",
                    entity_type="bar",
                )
                await publish_event(
                    ctx.vhost,
                    exchange="bazs.topic_exchange",
                    routing_key="baz.changelog",
                    event={"id": "123"},
                    source="/req_c02",
                    type="multiverse.baz.changelog.v1",
                    dataschema="https://schema.multiverse.io/baz/changelog/1.0.0",
                    entity_type="baz",
                )

            # Notify the service it can start consuming
            await test_proc.send("consume")

            # Let the service consume messages for 4 seconds
            await asyncio.sleep(4)

        # After the service stopped, wait for the rabbitmq API to catch up.
        await wait_for_api_to_catch_up()

        queue_names = [
            "test_c02.foo_events.queue",
            "test_c02.bar_events.queue",
            "test_c02.baz_events.queue",
        ]

        # Get the number of messages consumed in each queue
        queues = [await ctx.vhost.queue(name) for name in queue_names]

        consumed_messages = [200 - queue.messages for queue in queues]

        # Check the difference is less than 10%
        max_count = max(consumed_messages)
        min_count = min(consumed_messages)

        assert (
            max_count - min_count
        ) / max_count < 0.1, f"max_count = {max_count}, min_count = {min_count}"

        ctx.logger.info(consumed_messages)
