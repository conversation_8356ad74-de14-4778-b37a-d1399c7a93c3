import json

from event_tooling_test_engine.test_context_protocol import TestContext

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r24&view=modal


class P09Test:
    """
    The test object for this test should do this

    1. Set up a backfill publisher for the event type 'multiverse.foo.count',

    2. Print "started"

    3. Expect "ready" on stdin

    4. Publish an 10 instances of that event to the default exchange using
      'test_p09.backfill_events.queue' as the routing key. The event "id" and "count"
       values should be the index of the event being sent.

    5. Print "events sent"
    """

    requirement_code = "P-09"
    test_object_prefix = "req_p09"

    async def run(self, ctx: TestContext) -> None:
        async with ctx.proc as proc:
            # Wait until the publisher is set up
            await proc.expect("started")

            # Create a queue
            await ctx.vhost.ensure_queue(
                "test_p09.backfill_events.queue",
                arguments={"x-queue-type": "classic"},
            )

            # Notify the test object we're ready
            await proc.send("ready")

            # Wait until the test object has sent the event
            await proc.expect("events sent")

            # Expect 10 messages on the queue with the expected payload
            messages = await ctx.vhost.get_messages(
                "test_p09.backfill_events.queue", count=10
            )
            assert len(messages) == 10
            for i, msg in enumerate(messages):
                payload = json.loads(msg.payload)["data"]
                assert payload == {"id": str(i), "count": i}
