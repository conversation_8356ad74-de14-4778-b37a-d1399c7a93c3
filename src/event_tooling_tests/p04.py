from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import wait_for_api_to_catch_up

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r4&view=modal


class P04Test:
    """
    The test object for this test should do this

    1. Start 3 event publishers - 1 connection with 3 channels (1 per publisher)
    2. Print "started"
    3. Expect "finished" on stdin
    """

    requirement_code = "P-04"
    test_object_prefix = "req_p04"

    async def run(self, ctx: TestContext) -> None:
        # Check no connections or channels exist initially
        connections = await ctx.vhost.connections()
        channels = await ctx.vhost.channels()
        assert not len(connections), "No connections are expected to exist yet"
        assert not len(channels), "No channels are expected to exist yet"

        async with ctx.proc as proc:
            # Wait till the publisher is set up
            await proc.expect("started")

            await wait_for_api_to_catch_up()

            connections = await ctx.vhost.connections()
            channels = await ctx.vhost.channels()
            assert len(connections) == 1, "1 connection is expected to exist"
            assert len(channels) == 3, "3 channels are expected to exist yet"

            # Notify the test object we're done
            await proc.send("finished")
