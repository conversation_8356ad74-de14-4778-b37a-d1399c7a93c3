import json
from event_tooling_test_engine.test_context_protocol import TestContext


# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r1&view=modal

# This test checks more than the above, it verifies that the published event has
# the correct headers and that the envelope is correctly formed.


class P01Test:
    """
    The test object for this test should do this

    1. start an event publisher for events of type
       'multiverse.foo.changelog.v2', service name "test_p01"

    2. print "started"

    3. expect "ready" on stdin

    4. publish an instance on that event

    5. print "event sent"
    """

    requirement_code = "P-01"
    test_object_prefix = "req_p01"

    async def run(self, ctx: TestContext) -> None:
        async with ctx.proc as proc:
            await proc.expect("started")

            await ctx.vhost.ensure_queue("test_queue")
            await ctx.vhost.ensure_binding(
                from_exchange="foos.topic_exchange",
                to_queue="test_queue",
                routing_key="foo.changelog",
            )

            await proc.send("ready")
            await proc.expect("event sent")

            messages = await ctx.vhost.get_messages("test_queue", count=10)

            assert len(messages) == 1

            message = messages[0]

            ctx.logger.info(message)

            props = message.properties

            assert props.content_type == "application/json"

            payload = json.loads(message.payload)

            assert payload["id"]
            assert payload["source"] == "/test_p01"
            assert payload["specversion"] == "1.0"
            assert payload["type"] == "multiverse.foo.changelog.v2"
            assert payload["datacontenttype"] == "application/json"
            assert (
                payload["dataschema"]
                == "https://schema.multiverse.io/foo/changelog/2.0.0"
            )
            assert payload["time"]
