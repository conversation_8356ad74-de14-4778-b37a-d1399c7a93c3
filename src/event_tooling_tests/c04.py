from event_tooling_test_engine.test_context_protocol import TestContext

from event_tooling_tests.utils import (
    declare_topic_exchange,
    publish_event,
    wait_for_api_to_catch_up,
)


# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r12&view=modal


class C04Test:
    """
    The test object for this requirement should do this:

    1. Set up a consumer for events of type
       'multiverse.foo.changelog.v1'. The service name is
       "test_c04". The consumer should:
       - Print the line "message id = xxx" for each consumed message,
         where xxx is the value of the "id" field of the event schema.
       - Sleep for 300ms
       - Acknowledge the message.

    2. Print "ready" on stdout

    3. Wait for "consume" on stdin

    4. Start consuming, keep consuming for ever
    """

    requirement_code = "C-04"
    test_object_prefix = "req_c04"

    async def run(self, ctx: TestContext) -> None:
        # Declare exchange for consumer to bind to
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")

        async with ctx.proc as proc:
            await proc.expect("ready")

            # Publish 100 messages
            total_messages = 100
            for i in range(1, total_messages + 1):
                ctx.logger.info(f"publish message {i}")
                await publish_event(
                    ctx.vhost,
                    exchange="foos.topic_exchange",
                    routing_key="foo.changelog",
                    event={"id": f"{i}"},
                    source="/req_c04",
                    type="multiverse.foo.changelog.v1",
                    dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                )

            await proc.send("consume")

            # Wait for first 10 messages to be processed in order
            for i in range(1, 11):
                await proc.expect(f"message id = {i}", timeout=5)

            # Kill the consumer abruptly - it won't acknowledge message 10
            proc.kill()

        # Allow all messages to drain before checking queue stats
        await wait_for_api_to_catch_up()

        queue = await ctx.vhost.queue("test_c04.foo_events.queue")
        expected_ready = total_messages - 9
        assert (
            queue.messages_ready == expected_ready
        ), f"Expected {expected_ready} messages ready, got {queue.messages_ready}"

        # Start a new context manager for the second consumer
        async with ctx.proc as proc:
            await proc.expect("ready")
            await proc.send("consume")

            # Expect messages 10, 11, 12, ...
            for i in range(10, 16):
                await proc.expect(f"message id = {i}")
