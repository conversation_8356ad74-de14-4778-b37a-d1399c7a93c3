import ast
import asyncio
from typing import Literal

from event_tooling_test_engine import errors as events_tooling_errors
from event_tooling_test_engine.process_driver_protocol import ProcessDriver
from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import publish_event, wait_for_api_to_catch_up

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r17&view=modal

MESSAGES_TOTAL_COUNT = 100


async def get_latest_message(proc: ProcessDriver) -> str:
    latest_message_raw = await proc.getline()
    while True:
        try:
            next_message = await proc.getline()
            if not next_message:
                break
            latest_message_raw = next_message
        except events_tooling_errors.Timeout:
            break
    return latest_message_raw


async def publish_messages(ctx: TestContext) -> None:
    # Publish 100 messages
    for i in range(MESSAGES_TOTAL_COUNT):
        await publish_event(
            ctx.vhost,
            exchange="foos.topic_exchange",
            routing_key="foo.changelog",
            event={"id": str(i + 1)},
            dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
            type="multiverse.foo.changelog.v1",
            source="/req_c09",
        )


def end_process(proc: ProcessDriver, signal: Literal["kill", "terminate"]) -> None:
    if signal == "kill":
        proc.kill()
    elif signal == "terminate":
        proc.terminate()
    else:
        raise ValueError(f"Invalid signal: {signal}")


async def _run(ctx: TestContext, signal: Literal["kill", "terminate"]) -> None:
    async with ctx.proc as proc:
        # Wait for the consumer to be ready
        await proc.expect("started")
        await publish_messages(ctx)
        # Notify the service it can start consuming
        await proc.send("consume")
        # Let the service consume messages for 100ms
        await asyncio.sleep(0.1)
        end_process(proc, signal)
        # Get last event message logged by the consumer
        latest_message_raw = await get_latest_message(proc)
        # Wait for the process to end
        await proc.wait(timeout=10)

    # Annoying but waiting any less than 7 seconds sometimes causes the test to fail
    await wait_for_api_to_catch_up(7)
    queue = await ctx.vhost.queue(
        "test_c09.foo_events.queue",
        enable_queue_totals=True,
        disable_stats=True,
    )

    latest_message_id = int(ast.literal_eval(latest_message_raw)["id"])
    messages_ready = queue["messages_ready"]
    assert (
        messages_ready > 0 and messages_ready < MESSAGES_TOTAL_COUNT
    ), "Some messages should be consumed"
    assert (
        MESSAGES_TOTAL_COUNT - messages_ready == latest_message_id
    ), "The last consumed message should have an id equal to the difference between the total messages and the ready messages"
    assert (
        "consumers" not in queue or queue["consumers"] == 0
    ), "There shouldn't be any active consumers"

    async with ctx.proc as proc:
        # Wait for the consumer to be ready
        await proc.expect("started")
        # Notify the service it can start consuming
        await proc.send("consume")
        # Get last message printed by the consumer
        first_message_raw = await proc.getline()
        latest_message_raw = await get_latest_message(proc)

    # Annoying but waiting any less than 7 seconds sometimes causes the test to fail
    await wait_for_api_to_catch_up(7)
    queue = await ctx.vhost.queue("test_c09.foo_events.queue")

    first_message_id = int(ast.literal_eval(first_message_raw)["id"])
    assert (
        first_message_id == latest_message_id + 1
    ), "The first consumed message should be the message after the last consumed message"

    latest_message = ast.literal_eval(latest_message_raw)
    latest_message_id = int(latest_message["id"])
    assert (
        latest_message_id == MESSAGES_TOTAL_COUNT
    ), "The last message should have an id equal to the total messages"
    assert queue["messages_ready"] == 0, "All messages should be consumed"


class C09Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events 'multiverse.foo.changelog.v1'

    2. Print "started"

    3. Wait for "consume" on stdin

    4. Start consuming. Each message should be acknowledged after 1ms of blocking work and
       the message body should be printed to stdout.
    """

    requirement_code = "C-09"
    test_object_prefix = "req_c09"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await ctx.vhost.ensure_exchange(
            "foos.topic_exchange", exchange_type="topic", durable=True
        )
        # Run tests for terminate signal
        await _run(ctx, "terminate")
        # Run tests for terminate signal
        await _run(ctx, "kill")
