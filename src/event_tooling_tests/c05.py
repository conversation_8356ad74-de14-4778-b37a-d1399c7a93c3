from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import wait_for_api_to_catch_up

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r13&view=modal


class C05Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events 'multiverse.foo.changelog.v1'
       'mulitverse.foo.changelog.v2', 'multiverse.foo.deleted.v1', service name 'test_c05'

    2. print "started"
    """

    requirement_code = "C-05"
    test_object_prefix = "req_c05"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await ctx.vhost.ensure_exchange(
            "foos.topic_exchange", exchange_type="topic", durable=True
        )

        async with ctx.proc as proc:
            await proc.expect("started", timeout=1)

            await wait_for_api_to_catch_up()

            bindings = await ctx.vhost.bindings(to_queue="test_c05.foo_events.queue")

            assert any(
                b.routing_key == "foo.changelog" and b.source == "foos.topic_exchange"
                for b in bindings
            )
            assert any(
                b.routing_key == "foo.deleted" and b.source == "foos.topic_exchange"
                for b in bindings
            )
