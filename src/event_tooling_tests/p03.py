from event_tooling_test_engine.errors import RabbitMQNotFound
from event_tooling_test_engine.test_context_protocol import TestContext


# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r3&view=modal


class P03Test:
    """
    The test object for this test should do this

    1. start an event publisher for events of type 'multiverse.foo.changelog.v2'
    2. print "ready"
    """

    requirement_code = "P-03"
    test_object_prefix = "req_p03"

    async def run(self, ctx: TestContext) -> None:
        # Check the exchange doesn't exist initially
        try:
            await ctx.vhost.exchange("foos.topic_exchange")
        except RabbitMQNotFound:
            pass
        else:
            assert False, "Exchange not expected to exist yet"

        # Start the test object
        test_proc = await ctx.proc.start()

        # Make sure it's ready (i.e. the published is in place), but don't wait
        # more than 1 second
        await test_proc.expect("ready", timeout=1)

        # Now check the exchange again
        new_exchange = await ctx.vhost.exchange("foos.topic_exchange")

        assert new_exchange.durable
        assert new_exchange.type == "topic"
        assert new_exchange.name == "foos.topic_exchange"
