from event_tooling_test_engine.requirement_tests import RequirementTestSuite
from event_tooling_tests.c01 import C01Test
from event_tooling_tests.c02 import C02Test
from event_tooling_tests.c03 import C03Test
from event_tooling_tests.c04 import C04Test
from event_tooling_tests.c05 import C05Test
from event_tooling_tests.c06 import C06Test
from event_tooling_tests.c07 import C07Test
from event_tooling_tests.c08 import C08Test
from event_tooling_tests.c09 import C09Test
from event_tooling_tests.c10 import C10Test
from event_tooling_tests.c11 import C11Test
from event_tooling_tests.c12 import C12Test
from event_tooling_tests.c13 import C13Test
from event_tooling_tests.c15 import C15Test
from event_tooling_tests.p01 import P01Test
from event_tooling_tests.p02 import P02Test
from event_tooling_tests.p03 import P03Test
from event_tooling_tests.p04 import P04Test
from event_tooling_tests.p05 import P05Test
from event_tooling_tests.p06 import P06Test
from event_tooling_tests.p07 import P07Test
from event_tooling_tests.p08 import P08Test
from event_tooling_tests.p09 import P09Test

testsuite = RequirementTestSuite()

testsuite.register(P01Test())
testsuite.register(P02Test())
testsuite.register(P03Test())
testsuite.register(P04Test())
testsuite.register(P05Test())
testsuite.register(P06Test())
testsuite.register(P07Test())
testsuite.register(P08Test())
testsuite.register(P09Test())

testsuite.register(C01Test())
testsuite.register(C02Test())
testsuite.register(C03Test())
testsuite.register(C04Test())
testsuite.register(C05Test())
testsuite.register(C06Test())
testsuite.register(C07Test())
testsuite.register(C08Test())
testsuite.register(C09Test())
testsuite.register(C10Test())
testsuite.register(C11Test())
testsuite.register(C12Test())
testsuite.register(C13Test())
testsuite.register(C15Test())
