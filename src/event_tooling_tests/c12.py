from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import (
    declare_topic_exchange,
    publish_event,
    wait_for_api_to_catch_up,
)

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r20&view=center


class C12Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events 'multiverse.foo.changelog.v2', service name
       'test_c12'

    2. print "started"

    3. Start consuming events.  Events that have incorrect type or events whose
       data payload does not conform to the event schema definition should be
       rejected automatically.  Correctly formed events should be acked and
       their entity id printed to stdout.

    """

    requirement_code = "C-12"
    test_object_prefix = "req_c12"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")

        async with ctx.proc as proc:
            await proc.expect("started", timeout=1)

            # This event has invalid type (should be
            # 'multiverse.foo.changelog.v2')
            await publish_event(
                ctx.vhost,
                "foos.topic_exchange",
                routing_key="foo.changelog",
                event={"id": "111", "name": "name"},
                dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                type="foo.changelog.v2",
                source="/req_c12",
            )

            # This event has invalid payload (should be required "name" field is
            # missing)
            await publish_event(
                ctx.vhost,
                "foos.topic_exchange",
                routing_key="foo.changelog",
                event={"id": "222"},
                dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                type="multiverse.foo.changelog.v2",
                source="/req_c12",
            )

            # This event is valid
            await publish_event(
                ctx.vhost,
                "foos.topic_exchange",
                routing_key="foo.changelog",
                event={"id": "333", "name": "name"},
                dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                type="multiverse.foo.changelog.v2",
                source="/req_c12",
            )

            # Expect client to only print the third message (first two have been rejected)
            await proc.expect("333")

            await wait_for_api_to_catch_up()

            # Check that there are no messages is the queue now
            queue = await ctx.vhost.queue("test_c12.foo_events.queue")
            assert queue.messages == 0

            # Check that all messages were delivered
            queue = await ctx.vhost.queue(
                "test_c12.foo_events.queue", disable_stats=False
            )
            message_stats = queue.message_stats

            # Only the valid message was acknowledged
            assert message_stats.ack == 1, f"ACK == {message_stats.ack}, expected 1"
            # No messages were nacked as there were no redelivered messages
            assert message_stats.redeliver == 0
            # Exactly 3 messages were delivered
            assert message_stats.deliver == 3
