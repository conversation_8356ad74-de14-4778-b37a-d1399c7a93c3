from collections import Counter
from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import publish_event

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r14&view=modal


class C06Test:
    """
    The test object for this requirement should do this:

    1. setup a consumer for events 'multiverse.foo.count.v1', service name
       'test_c06'.  It should be configured so that nacked messages will be
       redelivered only 5 times after which they will be rejected.

    2. Print "started"

    3. Start consuming events. For each received event
        - if count is a multiple of 5, print "{count}!" and nack the message
        - otherwise, print "{count}" and ack the message
    """

    requirement_code = "C-06"
    test_object_prefix = "req_c06"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await ctx.vhost.ensure_exchange(
            "foos.topic_exchange", exchange_type="topic", durable=True
        )

        async with ctx.proc as proc:
            # Wait for the test object to be ready
            await proc.expect("started", timeout=1)

            # Send 20 events with count = 1, 2 ... 20
            for i in range(1, 21):
                await publish_event(
                    ctx.vhost,
                    exchange="foos.topic_exchange",
                    routing_key="foo.count",
                    event={"id": f"{i}", "count": i},
                    source="/req_c06",
                    type="multiverse.foo.count.v1",
                    dataschema="https://schema.multiverse.io/foo/count/1.0.0",
                )

            counter = Counter[str]()

            # Expect all numbers to be output, but multiples of 5 to be output 6
            # times so that makes a total of 16 + 6*4 = 40 lines expected.
            for i in range(40):
                counter[await proc.getline()] += 1

            # Check that the correct lines were output.
            for i in range(1, 21):
                if i % 5 == 0:
                    assert counter[f"{i}!"] == 6
                else:
                    assert counter[f"{i}"] == 1
