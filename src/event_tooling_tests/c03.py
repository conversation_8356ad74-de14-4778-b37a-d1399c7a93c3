from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import (
    declare_topic_exchange,
    publish_event,
    wait_for_api_to_catch_up,
)

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r11&view=modal


class C03Test:
    """
    The test object for this requirement should do this:

    1. Configure channel prefetch count to 5 and set up a consumer for events of
       type 'multiverse.foo.changelog.v1' on exchange 'foos.topic_exchange'.
    2. Print "started" on stdout
    3. Expect "ready" on stdin
    4. Consume the first event, but do not ack it, instead print "consuming" and
       wait indefinitely
    """

    requirement_code = "C-03"
    test_object_prefix = "req_c03"

    async def run(self, ctx: TestContext) -> None:
        # Declare exchange for consumer for bind to
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")

        async with ctx.proc as proc:
            await proc.expect("started", timeout=1)

            # Publish 10 messages
            for i in range(0, 10):
                await publish_event(
                    ctx.vhost,
                    exchange="foos.topic_exchange",
                    routing_key="foo.changelog",
                    event={"id": f"{i + 1}"},
                    source="/req_c03",
                    type="multiverse.foo.changelog.v1",
                    dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                )

            # Notify the test object we're ready
            await proc.send("ready")

            await proc.expect("consuming")

            await wait_for_api_to_catch_up(delay=6)

            channels = await ctx.vhost.channels()
            assert len(channels) == 1, "1 channel is expected to exist"

            # Check the consumer count, prefetch count and unacknowledged messages
            channel = channels[0]
            assert channel.consumer_count == 1, "1 consumer is expected to exist"
            assert channel.prefetch_count == 5, "Prefetch count is expected to be 5"
            assert (
                channel.messages_unacknowledged == 5
            ), "5 messages are expected to be unacknowledged"

            queue = await ctx.vhost.queue(
                "test_c03.foo_events.queue", disable_stats=False
            )
            assert queue.message_stats.ack == 0
            assert queue.message_stats.deliver == 5
