from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import wait_for_api_to_catch_up

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r7&view=modal


class P07Test:
    """
    The test object for this test should:

    1. start an event publisher for events of type
       'multiverse.foo.changelog.v2' and publish one event

    2. wait for "publish_again" on stdin (before that the connection will be closed by the broker)

    3. publish another event with the same publisher (it should recover the lost connection automatically)

    The test verifies that the publisher can automatically recover from connection failures
    by ensuring both messages are delivered successfully.
    """

    requirement_code = "P-07"
    test_object_prefix = "req_p07"

    async def run(self, ctx: TestContext) -> None:
        # Initially there are no connections
        connections = await ctx.vhost.connections()
        assert len(connections) == 0

        # With the test object running
        async with ctx.proc as proc:
            # Wait for it to be set up
            await proc.expect("started")

            # Wait for admin API to catch up
            await wait_for_api_to_catch_up()

            # Create a queue to receive the message
            await ctx.vhost.ensure_queue("test_p07.retry_events.queue")
            await ctx.vhost.ensure_binding(
                from_exchange="foos.topic_exchange",
                to_queue="test_p07.retry_events.queue",
                routing_key="foo.changelog",
            )

            # Signal ready to publish first message
            await proc.send("ready")

            # Check first message is delivered
            messages = await ctx.vhost.get_messages(
                "test_p07.retry_events.queue", count=1
            )
            assert len(messages) == 1, f"Expected 1 message, got {len(messages)}"

            # Get the current connection
            connections = await ctx.vhost.connections()
            assert len(connections) == 1
            connection = connections[0]

            # Close the connection to simulate RabbitMQ being down
            await ctx.vhost.close_connection(connection.name)

            await wait_for_api_to_catch_up(delay=10)

            # Signal to publish again after connection closed
            await proc.send("publish_again")

            # Wait for the second message
            messages = await ctx.vhost.get_messages(
                "test_p07.retry_events.queue", count=1
            )
            assert len(messages) == 1, f"Expected 1 message, got {len(messages)}"

            # Signal completion
            await proc.send("done")
