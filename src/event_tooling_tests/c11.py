from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import declare_topic_exchange, publish_event

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r19&view=modal


class C11Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events 'multiverse.foo.changelog.v1'
       'mulitverse.foo.deleted.v1', 'multiverse.foo.count.v1', all on the same
       queue, service name 'test_c11'

    2. print "started"

    3. For each event received, print one line of the form
          <schema type>
       E.g.
          multiverse.foo.deleted.v1
    """

    requirement_code = "C-11"
    test_object_prefix = "req_c11"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")

        async with ctx.proc as proc:
            await proc.expect("started", timeout=1)

            # Publish 30 messages of alternating type (different routing keys)
            for i in range(10):
                await publish_event(
                    ctx.vhost,
                    "foos.topic_exchange",
                    routing_key="foo.changelog",
                    event={"id": "123", "name": f"name_{i}"},
                    dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                    type="multiverse.foo.changelog.v1",
                    source="/req_c11",
                )
                await publish_event(
                    ctx.vhost,
                    "foos.topic_exchange",
                    routing_key="foo.deleted",
                    event={"id": "123"},
                    dataschema="https://schema.multiverse.io/foo/deleted/1.0.0",
                    type="multiverse.foo.deleted.v1",
                    source="/req_c11",
                )
                await publish_event(
                    ctx.vhost,
                    "foos.topic_exchange",
                    routing_key="foo.count",
                    event={"id": "123", "count": i},
                    dataschema="https://schema.multiverse.io/foo/count/1.0.0",
                    type="multiverse.foo.count.v1",
                    source="/req_c11",
                )

            # Expect the events to be processed in the same order
            for i in range(10):
                await proc.expect("multiverse.foo.changelog.v1")
                await proc.expect("multiverse.foo.deleted.v1")
                await proc.expect("multiverse.foo.count.v1")
