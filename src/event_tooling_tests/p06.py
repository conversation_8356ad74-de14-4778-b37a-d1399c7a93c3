from datetime import UTC, datetime, timedelta

from event_tooling_test_engine.test_context_protocol import TestContext

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r6&view=modal


class P06Test:
    """
    The test object for this test should do this

    1. start an event publisher for events of type
       'multiverse.foo.changelog.v2', service name "test_p01"

    2. print "started"

    3. expect "ready" on stdin

    4. publish an instance on that event

    5. print "event sent"
    """

    requirement_code = "P-06"
    test_object_prefix = "req_p06"

    async def run(self, ctx: TestContext) -> None:
        # With the test object running
        async with ctx.proc as proc:
            # Wait till the publisher is set up
            await proc.expect("started")

            # Create a queue and bind it
            await ctx.vhost.ensure_queue("test_queue")
            await ctx.vhost.ensure_binding(
                from_exchange="foos.topic_exchange",
                to_queue="test_queue",
                routing_key="foo.changelog",
            )

            # Notify the test object we're ready
            await proc.send("ready")

            # Wait until the test object has sent the event
            await proc.expect("event sent")

            # Expect a single message on the queue
            messages = await ctx.vhost.get_messages("test_queue", count=10)

            assert len(messages) == 1

            message = messages[0]

            # Check the message headers are as expected
            props = message.properties
            headers = props.headers

            assert props.delivery_mode == 2  # This means PERSISTENT
            assert props.content_type == "application/json"
            assert props.app_id == "/test_p06"

            assert headers.schema.startswith(
                "https://schema.multiverse.io/foo/changelog/2.0.0"
            )
            assert headers.entity_type == "foo"
            assert isinstance(headers.entity_id, str) and headers.entity_id

            timestamp = datetime.fromtimestamp(props.timestamp, UTC)
            assert datetime.now(UTC) - timestamp < timedelta(seconds=5)

            now_microseconds = int(datetime.now(UTC).timestamp() * 1_000_000)
            assert abs(headers.timestamp_microseconds - now_microseconds) <= 1_000_000
