from event_tooling_test_engine.errors import RabbitMQNotFound
from event_tooling_test_engine.test_context_protocol import Test<PERSON>ontext
from event_tooling_tests.utils import declare_topic_exchange


# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r9&view=modal


class C01Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events of type
       'multiverse.foos.changelog.v2'.  The service name is
       "test_c01".

    2. Print "ready" on stdout
    """

    requirement_code = "C-01"
    test_object_prefix = "req_c01"

    async def run(self, ctx: TestContext) -> None:
        # Declare the exchange
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")

        # Check the queue does not exist
        try:
            await ctx.vhost.queue("test_c01.foo_events.queue")
        except RabbitMQNotFound:
            pass
        else:
            assert False, "queue not expected to exist yet"

        # Start the service with the consumer
        test_proc = await ctx.proc.start()

        await test_proc.expect("ready", timeout=1)

        # Check the queue exists with correct properties
        queue = await ctx.vhost.queue("test_c01.foo_events.queue")

        assert queue.arguments["x-queue-type"] == "quorum"
        assert queue.arguments["x-single-active-consumer"]
        assert queue.durable
        assert queue.name == "test_c01.foo_events.queue"
