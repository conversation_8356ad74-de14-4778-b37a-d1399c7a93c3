import random
import string
from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import (
    declare_topic_exchange,
    publish_event,
    wait_for_api_to_catch_up,
)

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r21&view=modal


class C13Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events 'multiverse.foo.changelog.v2', service name
       'test_c13'.  The consumer queue should be named
       'test_c13.foo_events.queue` as per MV conventions.

    2. print "started"

    3. For each event received, print one line containing the "name" field of
       the received changelog event.  Note that the test driver will close the
       consumer connection at some point to test that the client automatically
       re-establishes the connection.
    """

    requirement_code = "C-13"
    test_object_prefix = "req_c13"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await declare_topic_exchange(ctx.vhost, "foos.topic_exchange")

        async with ctx.proc as proc:
            # Wait till the consumer is started
            await proc.expect("started", timeout=1)

            await wait_for_api_to_catch_up()

            # Check the consumer queue
            queue = await ctx.vhost.queue(
                "test_c13.foo_events.queue", disable_stats=False
            )

            assert len(queue.consumer_details) == 1

            consumer = queue.consumer_details[0]
            connection_name = consumer.channel_details.connection_name

            # Check the queue connection (consumer connection)
            connection = await ctx.vhost.connection(connection_name)

            assert connection.state == "running"

            # Close the connection
            await ctx.vhost.close_connection(connection_name)

            # Publish a multiverse.foo.changelog.v2 message with a random name
            name = "".join(random.sample(string.ascii_letters, 10))

            await publish_event(
                ctx.vhost,
                exchange="foos.topic_exchange",
                routing_key="foo.changelog",
                event={"id": "123", "name": name},
                dataschema="https://schema.multiverse.io/foo/changelog/2.0.0",
                type="multiverse.foo.changelog.v2",
                source="/req_c01",
            )

            # Give some time for the consumer to recover the connection and
            # consume the event. It should print the value of the name field of
            # the event
            await proc.expect(name, timeout=10)

            await wait_for_api_to_catch_up()

            # Check the new connection
            connections = await ctx.vhost.connections()
            assert len(connections) == 1

            new_connection = connections[0]

            # Verify that it is not the same connection as before.
            assert connection.connected_at < new_connection.connected_at
