import asyncio
import json
from datetime import datetime

from event_tooling_test_engine import errors
from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import wait_for_api_to_catch_up
from event_tooling_tests.utils import publish_event


class C07Test:
    """
    The test object for this requirement should do this:

    1. Start a consumer for events 'multiverse.foo.changelog.v1' with rate limit of 2 messages per second,
       service name 'test_c07'

    2. For each consumed message print: "Received message (time with ms precision): [body-of-the-message]"
       Example: "Received message (11:12:13.456): {"id": 1}"

    3. Configure the consumer's rate limit to 2 messages per second

    4. print "started"

    5. Wait for "ready" signal

    The test framework will then:

    6. Create and bind a queue to receive messages

    7. Publish 20 messages to the exchange with format {"id": 1}, {"id": 2}, etc.

    8. Check the consumer's logs to verify:
       - Messages are consumed at exactly 2/second
       - Time difference between messages is ~0.5 seconds
       - All messages are eventually processed
       - Messages are processed in order

    9. Send "done" signal
    """

    requirement_code = "C-07"
    test_object_prefix = "req_c07"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await ctx.vhost.ensure_exchange(
            "foos.topic_exchange", exchange_type="topic", durable=True
        )

        async with ctx.proc as proc:
            await proc.expect("started")

            await wait_for_api_to_catch_up()

            # Create a queue to receive the messages
            await ctx.vhost.ensure_queue("test_c07.events.queue")
            await ctx.vhost.ensure_binding(
                from_exchange="foos.topic_exchange",
                to_queue="test_c07.events.queue",
                routing_key="foo.changelog",
            )

            # Send ready signal
            await proc.send("ready")

            # Publish 20 messages
            for i in range(20):
                await publish_event(
                    ctx.vhost,
                    exchange="foos.topic_exchange",
                    routing_key="foo.changelog",
                    event={
                        "id": str(i)
                    },  # id should be string according to ChangelogSchema
                    source="/req_c07",
                    type="multiverse.foo.changelog.v1",
                    dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                )

            # Wait and collect timestamps for at least 10 seconds
            await asyncio.sleep(10)

            # Get the output lines
            timestamps = []
            message_ids = []

            # Keep reading lines until we timeout
            while True:
                try:
                    line = await proc.getline(
                        timeout=0.1
                    )  # Short timeout to check for more messages
                    if "Received message" in line:
                        # Extract timestamp between parentheses
                        time_str = line[line.find("(") + 1 : line.find(")")]
                        timestamps.append(datetime.strptime(time_str, "%H:%M:%S.%f"))

                        # Extract message ID from the simplified message format
                        message_data = json.loads(line.split(": ", 1)[1])
                        message_ids.append(int(message_data["id"]))
                except errors.Timeout:
                    break  # No more lines to read

            # Verify rate limiting
            assert len(timestamps) > 0, "No messages were received"

            # Check intervals between messages
            for i in range(1, len(timestamps)):
                delta = timestamps[i] - timestamps[i - 1]
                delta_seconds = delta.total_seconds()

                # Should be approximately 0.5 seconds (2 messages per second)
                assert (
                    delta_seconds >= 0.4  # Allow small margin of error below 0.5
                ), f"Message interval {delta_seconds} seconds is too short for rate limit of 2 messages per second"

            # Verify message order
            assert message_ids == list(
                range(20)
            ), "Messages were not processed in order"

            # Signal completion
            await proc.send("done")
