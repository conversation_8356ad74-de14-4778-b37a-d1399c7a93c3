import asyncio
from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import wait_for_api_to_catch_up

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r8&view=modal


class P08Test:
    """
    The test object for this test should do this

    1. start an event publisher for events of type
       'multiverse.foo.changelog.v2'

    2. print "started"

    3. wait for "ready" on stdin (during this time the connection will be
       closed, so it should recover)

    4. publish an event

    5. print "event sent"

    6. Keep running until receiveing "done" on stdin
    """

    requirement_code = "P-08"
    test_object_prefix = "req_p08"

    async def run(self, ctx: TestContext) -> None:
        # Initially there are no connections
        connections = await ctx.vhost.connections()
        assert len(connections) == 0

        # With the test object running
        async with ctx.proc as proc:
            # Wait for it to be set up
            await proc.expect("started")

            # Wait for admin API to catch up
            await wait_for_api_to_catch_up()

            # Check there is a connection
            connections = await ctx.vhost.connections()
            assert len(connections) == 1
            connection1 = connections[0]

            # Create a queue to receive the message
            await ctx.vhost.ensure_queue("test_queue")
            await ctx.vhost.ensure_binding(
                from_exchange="foos.topic_exchange",
                to_queue="test_queue",
                routing_key="foo.changelog",
            )

            # Close the connection the test object set up to rabbitmq
            await ctx.vhost.close_connection(connection1.name)

            await asyncio.sleep(1)

            # Signal to the test object that they can send the event
            await proc.send("ready")

            # Wait for the test object to have sent one message
            await proc.expect("event sent", timeout=5)

            # Wait for admin API to catch up and the test object to recover a
            # connection
            await wait_for_api_to_catch_up()

            # Check there is a connection again
            connections2 = await ctx.vhost.connections()
            assert (
                len(connections2) == 1
            ), f"expected 1 connection, got {len(connections2)}"
            connection2 = connections2[0]

            # Check this is not the same connection as before!
            assert connection1.connected_at < connection2.connected_at

            # Check there is one message on the queue
            messages = await ctx.vhost.get_messages("test_queue", 10)
            assert len(messages) == 1
