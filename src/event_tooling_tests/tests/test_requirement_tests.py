import pathlib

from pytest import Metafunc
from event_tooling_test_engine.requirement_tests import (
    RequirementTestRunner,
)
from event_tooling_tests import testsuite

test_object_dir = pathlib.Path(__file__).parent.resolve() / "requirement_test_objects"  # noqa: F821


async def test_requirment_test(
    runner: RequirementTestRunner,
) -> None:
    result = await runner.run_test(capture_logs=False)
    error = result.error
    if error is None and "_fail." in result.test_object_path.name:
        raise Exception(f"Unexpected success: {result}")
    if error is not None and "_ok." in result.test_object_path.name:
        raise Exception(f"Unexpected failure: {result}") from error


def pytest_generate_tests(metafunc: Metafunc) -> None:
    if "runner" in metafunc.fixturenames:
        runners = testsuite.get_test_runners(test_object_dir)
        metafunc.parametrize("runner", argvalues=runners, ids=str)
