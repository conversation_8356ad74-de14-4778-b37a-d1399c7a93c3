#!/usr/bin/env -S python -u

import asyncio
from datetime import UTC, datetime
import json
import logging
import uuid

import aio_pika

from event_tooling_tests.tests.test_object_utils import connect_robust


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare exchange
        exchange = await channel.declare_exchange("foos.topic_exchange")

        print("started")

        assert input() == "ready"

        foo_id = str(uuid.uuid4())
        event = {
            "id": str(uuid.uuid4()),
            "source": "/test_p01",
            "specversion": "1.0",
            "type": "multiverse.foo.changelog.v2",
            "datacontenttype": "application/json",
            "dataschema": "https://schema.multiverse.io/foo/changelog/2.0.0",
            "data": {"id": foo_id},
            "time": datetime.now(UTC).isoformat(),
        }

        await exchange.publish(
            aio_pika.Message(
                json.dumps(event).encode(),
                content_type="application/json",
                content_encoding="utf-8",
                headers={
                    "schema": "https://schema.multiverse.io/foo/changelog/2.0.0",
                    "entity_type": "foo",
                    "entity_id": foo_id,
                },
            ),
            "foo.changelog",
        )

        print("event sent")


if __name__ == "__main__":
    asyncio.run(main())
