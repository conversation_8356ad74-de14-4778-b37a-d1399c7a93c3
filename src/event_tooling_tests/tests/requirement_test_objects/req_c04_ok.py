#!/usr/bin/env -S python -u

import asyncio
import json
import logging

from aio_pika.abc import AbstractIncomingMessage

from event_tooling_tests.tests.test_object_utils import ainput, connect_robust

EXCHANGE = "foos.topic_exchange"
ROUTING_KEY = "foo.changelog"


async def process_message(message: AbstractIncomingMessage) -> None:
    # Decode and print just the id
    content = json.loads(message.body.decode())
    print(f"message id = {content['data']['id']}", flush=True)

    # Simulate some processing time
    await asyncio.sleep(0.3)

    # Acknowledge the message
    await message.ack()


async def main() -> None:
    logging.basicConfig(level=logging.ERROR)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Set Quality of Service to process 10 unacknowledged message at a time
        # To apply parallel processing of messages.
        await channel.set_qos(prefetch_count=10)

        # Declare queue
        queue = await channel.declare_queue(
            "test_c04.foo_events.queue",
            durable=True,  # Make sure queue persists
        )

        # Bind queue to exchange
        await queue.bind(EXCHANGE, ROUTING_KEY)

        print("ready")

        await ainput() == "consume"
        # Process messages until killed
        async with queue.iterator() as messages:
            async for message in messages:
                await process_message(message)


if __name__ == "__main__":
    asyncio.run(main())
