#!/usr/bin/env -S python -u

import asyncio
import logging
import uuid
from datetime import UTC, datetime

import aio_pika

from event_tooling_tests.tests.test_object_utils import connect_robust


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare exchange
        exchange = await channel.declare_exchange("foos.topic_exchange")

        print("started")

        assert input() == "ready"

        lo_id = str(uuid.uuid4())
        now = datetime.now(UTC)

        await exchange.publish(
            aio_pika.Message(
                b"{}",
                content_type="application/json",
                content_encoding="utf-8",
                timestamp=now,
                app_id="/test_p06",
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                headers={
                    "schema": "https://schema.multiverse.io/foo/changelog/2.0.0",
                    "entity_type": "foo",
                    "entity_id": lo_id,
                    "timestamp_microseconds": int(now.timestamp() * 1_000_000),
                },
            ),
            "foo.changelog",
        )

        print("event sent")


if __name__ == "__main__":
    asyncio.run(main())
