#!/usr/bin/env -S python -u

import asyncio
import json
import logging
from datetime import datetime

from event_tooling_tests.tests.test_object_utils import (
    connect_robust,
    declare_consumer_queue,
)

logger = logging.getLogger("test_c07")


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        channel = await connection.channel()

        queue = await declare_consumer_queue(channel, "test_c07.foo_events.queue")

        await queue.bind("foos.topic_exchange", "foo.changelog")

        print("started")

        async with queue.iterator() as messages:
            async for message in messages:
                # Get current time with millisecond precision
                current_time = datetime.now().strftime("%H:%M:%S.%f")[:-3]

                # Parse the full CloudEvent message and extract just the data portion
                cloud_event = json.loads(message.body.decode())
                data = cloud_event["data"]

                print(f"Received message ({current_time}): {json.dumps(data)}")

                # Rate limit using asyncio.sleep
                await asyncio.sleep(
                    0.5
                )  # 2 messages per second = 0.5s between messages

                await message.ack()


if __name__ == "__main__":
    asyncio.run(main())
