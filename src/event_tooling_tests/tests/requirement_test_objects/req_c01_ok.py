#!/usr/bin/env -S python -u

import asyncio
import logging

from event_tooling_tests.tests.test_object_utils import connect_robust


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare queue
        await channel.declare_queue(
            "test_c01.foo_events.queue",
            durable=True,
            arguments={"x-queue-type": "quorum", "x-single-active-consumer": True},
        )

        print("ready")


if __name__ == "__main__":
    asyncio.run(main())
