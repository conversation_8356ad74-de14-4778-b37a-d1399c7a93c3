#!/usr/bin/env -S python -u

import asyncio
import json
import logging

from event_tooling_tests.tests.test_object_utils import (
    connect_robust,
    declare_consumer_queue,
)


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        channel = await connection.channel()

        queue = await declare_consumer_queue(
            channel, "test_c06.foo_events.queue", arguments={"x-delivery-limit": 5}
        )

        await queue.bind("foos.topic_exchange", "foo.count")

        print("started")

        async with queue.iterator() as messages:
            async for msg in messages:
                payload = json.loads(msg.body.decode())
                count = payload["data"]["count"]
                if count % 5 == 0:
                    print(f"{count}!")
                    await msg.nack()
                else:
                    print(count)
                    await msg.ack()


if __name__ == "__main__":
    asyncio.run(main())
