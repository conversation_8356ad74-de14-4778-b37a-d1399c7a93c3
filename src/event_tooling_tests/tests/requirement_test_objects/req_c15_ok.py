#!/usr/bin/env -S python -u

import asyncio
import logging

from aio_pika import ExchangeType

from event_tooling_tests.tests.test_object_utils import (
    connect_robust,
    declare_consumer_queue,
)


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        channel = await connection.channel()

        # Create the consumer queue
        queue = await declare_consumer_queue(
            channel,
            "test_c15.foo_events.queue",
            {
                "x-dead-letter-exchange": "default-dead-letter-exchange",
                "x-dead-letter-routing-key": "test_c15.foo_events.dead_letter_queue",
            },
        )
        await queue.bind("foos.topic_exchange", "foo.changelog")

        # Create the dead letter queue
        await channel.declare_exchange(
            "default-dead-letter-exchange", type=ExchangeType.DIRECT, durable=True
        )
        dlq = await channel.declare_queue("test_c15.foo_events.dead_letter_queue")
        await dlq.bind(
            "default-dead-letter-exchange",
            "test_c15.foo_events.dead_letter_queue",
        )

        print("started")


if __name__ == "__main__":
    asyncio.run(main())
