#!/usr/bin/env -S python -u

import asyncio
import logging

from event_tooling_tests.tests.test_object_utils import (
    connect_robust,
    declare_consumer_queue,
)


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        channel = await connection.channel()

        queue = await declare_consumer_queue(channel, "test_c05.foo_events.queue")

        await queue.bind("foos.topic_exchange", "foo.changelog")
        await queue.bind("foos.topic_exchange", "foo.deleted")

        print("started")


if __name__ == "__main__":
    asyncio.run(main())
