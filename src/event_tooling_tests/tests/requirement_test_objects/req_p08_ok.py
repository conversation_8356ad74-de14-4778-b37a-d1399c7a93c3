#!/usr/bin/env -S python -u

import asyncio
from datetime import UTC, datetime
import logging
import uuid

import aio_pika

from event_tooling_tests.tests.test_object_utils import (
    ainput,
    connect_robust,
    declare_exchange,
)


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare exchange
        exchange = await declare_exchange(channel, "foos.topic_exchange")

        print("started")

        msg = await ainput()

        assert msg == "ready"

        foo_id = str(uuid.uuid4())
        now = datetime.now(UTC)

        await exchange.publish(
            aio_pika.Message(
                b"{}",
                content_type="application/json",
                content_encoding="utf-8",
                timestamp=now,
                app_id="/test_p08",
                headers={
                    "schema": "https://schema.multiverse.io/foo/changelog/2.0.0",
                    "entity_type": "foo",
                    "entity_id": foo_id,
                },
            ),
            "foo.changelog",
        )

        print("event sent")

        msg = await ainput()
        assert msg == "done"


if __name__ == "__main__":
    asyncio.run(main())
