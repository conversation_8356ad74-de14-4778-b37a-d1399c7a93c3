#!/usr/bin/env -S python -u

import asyncio
from datetime import UTC, datetime
import logging

import aio_pika

from event_tooling_tests.tests.test_object_utils import connect_robust


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        print("started")

        assert input() == "ready"

        # Publish to default exchange using the queue name as the routing key
        exchange = channel.default_exchange
        for i in range(10):
            await exchange.publish(
                aio_pika.Message(
                    f'{{"data": {{"id":"{i}", "count": {i}}}}}'.encode(),
                    content_type="application/json",
                    content_encoding="utf-8",
                    timestamp=datetime.now(UTC),
                    app_id="/test_p08",
                    headers={
                        "schema": "https://schema.multiverse.io/foo/count/1.0.0",
                        "entity_type": "foo",
                        "entity_id": f"{i}",
                    },
                ),
                "test_p09.backfill_events.queue",
            )

        print("events sent")


if __name__ == "__main__":
    asyncio.run(main())
