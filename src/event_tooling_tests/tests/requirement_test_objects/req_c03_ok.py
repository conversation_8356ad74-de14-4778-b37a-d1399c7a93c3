#!/usr/bin/env -S python -u

import asyncio
import logging

from event_tooling_tests.tests.test_object_utils import (
    ainput,
    connect_robust,
    declare_consumer_queue,
)

logger = logging.getLogger("test_c03")


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Set prefetch count
        await channel.set_qos(prefetch_count=5)

        # Declare queue, bind it to exchange and start consuming
        queue = await declare_consumer_queue(channel, "test_c03.foo_events.queue")

        await queue.bind(
            "foos.topic_exchange",
            "foo.changelog",
        )

        print("started")

        sig = await ainput()
        assert sig == "ready"

        async with queue.iterator() as q:
            async for msg in q:
                print("consuming")
                await asyncio.sleep(60)


if __name__ == "__main__":
    asyncio.run(main())
