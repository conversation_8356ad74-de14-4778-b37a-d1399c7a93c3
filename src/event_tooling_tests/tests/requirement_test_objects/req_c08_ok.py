#!/usr/bin/env -S python -u

import asyncio
import logging

from event_tooling_tests.tests.test_object_utils import (
    ainput,
    connect_robust,
    declare_consumer_queue,
    declare_exchange,
)

logger = logging.getLogger("req_c02_ok")


class Consumer:
    def __init__(self, exchange: str, routing_key: str, queue: str):
        self.exchange_name = exchange
        self.routing_key = routing_key
        self.queue_name = queue
        self.stop_event = asyncio.Event()

    async def setup(self) -> None:
        self.connection = await connect_robust()
        self.channel = await self.connection.channel()
        await self.channel.set_qos(prefetch_count=5)
        exchange = await declare_exchange(self.channel, self.exchange_name)
        self.queue = await declare_consumer_queue(self.channel, self.queue_name)
        await self.queue.bind(exchange=exchange, routing_key=self.routing_key)

    async def start(self) -> None:
        self.stop_event.clear()
        async with self.queue.iterator() as q:
            async for msg in q:
                if self.stop_event.is_set():
                    break
                await msg.ack()

    async def stop(self) -> None:
        self.stop_event.set()
        await self.channel.close()
        await self.connection.close()


async def main() -> None:
    logging.basicConfig(level=logging.INFO)

    consumers = [
        Consumer("foos.topic_exchange", "foo.changelog", "test_c08.foo_events.queue"),
        Consumer("bars.topic_exchange", "bar.changelog", "test_c08.bar_events.queue"),
    ]

    for c in consumers:
        await c.setup()

    consumer_tasks = [asyncio.create_task(c.start()) for c in consumers]

    print("ready")

    msg = await ainput()
    assert msg == "stop consumer 1"
    await consumers[0].stop()
    consumer_tasks[0].cancel()
    try:
        await consumer_tasks[0]
    except asyncio.CancelledError:
        print("consumer 1 stopped")

    msg = await ainput()
    assert msg == "restart consumer 1"
    await consumers[0].setup()
    consumer_tasks[0] = asyncio.create_task(consumers[0].start())

    await asyncio.gather(*consumer_tasks)


if __name__ == "__main__":
    asyncio.run(main())
