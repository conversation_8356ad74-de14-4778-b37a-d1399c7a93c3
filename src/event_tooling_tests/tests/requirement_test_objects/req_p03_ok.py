#!/usr/bin/env -S python -u

import asyncio
import logging

from event_tooling_tests.tests.test_object_utils import connect_robust, declare_exchange


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare exchange
        await declare_exchange(channel, "foos.topic_exchange")

        print("ready")


if __name__ == "__main__":
    asyncio.run(main())
