#!/usr/bin/env -S python -u

import asyncio
import json
import logging
from typing import Any

from event_tooling_tests.tests.test_object_utils import (
    connect_robust,
    declare_consumer_queue,
)


def validate_event(event: dict[str, Any]) -> bool:
    return event["type"] == "multiverse.foo.changelog.v2" and set(event["data"]) == {
        "id",
        "name",
    }


logger = logging.getLogger("test_c12")


async def main() -> None:
    logging.basicConfig(level=logging.INFO)
    connection = await connect_robust()

    async with connection:
        channel = await connection.channel()

        queue = await declare_consumer_queue(channel, "test_c12.foo_events.queue")

        await queue.bind("foos.topic_exchange", "foo.changelog")

        print("started")

        async with queue.iterator() as messages:
            async for msg in messages:
                event = json.loads(msg.body.decode())
                if validate_event(event):
                    await msg.ack()
                    print(event["data"]["id"])
                else:
                    await msg.reject()
                    logger.info("Invalid")


if __name__ == "__main__":
    asyncio.run(main())
