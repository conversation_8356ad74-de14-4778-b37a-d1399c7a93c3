#!/usr/bin/env -S python -u

import asyncio
import json
import logging
import time

from event_tooling_tests.tests.test_object_utils import (
    connect_robust,
    declare_consumer_queue,
)

logger = logging.getLogger("req_c09_ok")


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create and bind queue
        channel = await connection.channel()
        queue = await declare_consumer_queue(channel, "test_c09.foo_events.queue")
        await queue.bind("foos.topic_exchange", "foo.changelog")

        # Notify the requirement test that the consumer is ready
        print("started")

        # Wait for the requirement test to tell us to start consuming
        input() == "consume"

        # Start consuming messages but block for 1ms for each message
        async with queue.iterator() as q:
            async for msg in q:
                # Do some busy work
                time.sleep(0.001)
                data = json.loads(msg.body)["data"]
                logger.info(data)
                print(data)
                await msg.ack()


if __name__ == "__main__":
    asyncio.run(main())
