#!/usr/bin/env -S python -u

import asyncio
import json
import uuid
from datetime import UTC, datetime

import aio_pika

from event_tooling_tests.tests.test_object_utils import connect_robust, declare_exchange


def is_valid_message(payload: dict) -> bool:
    try:
        return isinstance(payload["id"], str) and isinstance(payload["name"], str)
    except (ValueError, TypeError, KeyError):
        return False


async def main() -> None:
    connection = await connect_robust()

    async with connection:
        channel = await connection.channel()
        exchange = await declare_exchange(channel, "foos.topic_exchange")

        print("ready")

        while True:
            line = input()
            if not line:
                break
            try:
                payload = json.loads(line)
                if not is_valid_message(payload):
                    print("validation_error_raised")
                    continue

                event = {
                    "id": str(uuid.uuid4()),
                    "source": "/test_p02",
                    "specversion": "1.0",
                    "type": "multiverse.foo.changelog.v2",
                    "datacontenttype": "application/json",
                    "dataschema": "https://schema.multiverse.io/foo/changelog/2.0.0",
                    "data": payload,
                    "time": datetime.now(UTC).isoformat(),
                }

                message = aio_pika.Message(
                    body=json.dumps(event).encode(),
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                )
                await exchange.publish(message, routing_key="foo.changelog")
                print("message_published")
            except Exception as e:
                print(e)
                continue

        print("test_complete")


if __name__ == "__main__":
    asyncio.run(main())
