#!/usr/bin/env -S python -u

import asyncio
from datetime import UTC, datetime
import logging
import uuid

import aio_pika
from aio_pika.abc import DeliveryMode

from event_tooling_tests.tests.test_object_utils import (
    ainput,
    connect_robust,
    declare_exchange,
)


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)

    # Use RobustConnection for automatic reconnection
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()
        await channel.set_qos(prefetch_count=1)

        # Declare exchange
        exchange = await declare_exchange(channel, "foos.topic_exchange")

        print("started")

        # Wait for signal to start
        assert await ainput() == "ready"

        # First message
        message1 = aio_pika.Message(
            b"{}",
            content_type="application/json",
            content_encoding="utf-8",
            timestamp=datetime.now(UTC),
            app_id="/test_p07",
            delivery_mode=DeliveryMode.PERSISTENT,
            headers={
                "schema": "https://schema.multiverse.io/foo/changelog/2.0.0",
                "entity_type": "foo",
                "entity_id": str(uuid.uuid4()),
            },
        )

        # Publish first message
        await exchange.publish(message1, routing_key="foo.changelog", mandatory=True)

        # Wait for signal to publish again (connection will be closed externally)
        assert await ainput() == "publish_again"

        # Second message
        message2 = aio_pika.Message(
            b"{}",
            content_type="application/json",
            content_encoding="utf-8",
            timestamp=datetime.now(UTC),
            app_id="/test_p07",
            delivery_mode=DeliveryMode.PERSISTENT,
            headers={
                "schema": "https://schema.multiverse.io/foo/changelog/2.0.0",
                "entity_type": "foo",
                "entity_id": str(uuid.uuid4()),
            },
        )

        # Publish second message - should auto-reconnect
        await exchange.publish(message2, routing_key="foo.changelog", mandatory=True)


if __name__ == "__main__":
    asyncio.run(main())
