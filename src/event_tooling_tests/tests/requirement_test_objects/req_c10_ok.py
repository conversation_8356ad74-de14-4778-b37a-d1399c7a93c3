#!/usr/bin/env -S python -u

import asyncio
import json
import logging

from event_tooling_tests.tests.test_object_utils import (
    connect_robust,
    declare_consumer_queue,
)


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        channel = await connection.channel()

        queue = await declare_consumer_queue(channel, "test_c10.foo_events.queue")

        await queue.bind("foos.topic_exchange", "foo.changelog")

        print("started")

        async with queue.iterator() as messages:
            async for msg in messages:
                props = msg.properties
                props_dict = {
                    "content_type": props.content_type,
                    "content_encoding": props.content_encoding,
                    "app_id": props.app_id,
                    "timestamp": props.timestamp and props.timestamp.timestamp(),
                }
                print(json.dumps(props_dict))
                print(json.dumps(msg.headers))
                await msg.ack()


if __name__ == "__main__":
    asyncio.run(main())
