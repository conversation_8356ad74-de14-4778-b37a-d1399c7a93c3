#!/usr/bin/env -S python -u

import asyncio
from datetime import UTC, datetime
import uuid

import aio_pika

from event_tooling_tests.tests.test_object_utils import connect_robust, declare_exchange


async def main() -> None:
    # logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare exchange
        exchange = await declare_exchange(channel, "foos.topic_exchange")

        print("started")
        assert input() == "ready"

        message_count = 0

        # Run for at least 6 seconds
        while True:
            lo_id = str(uuid.uuid4())
            now = datetime.now(UTC)

            await exchange.publish(
                aio_pika.Message(
                    b"{}",
                    content_type="application/json",
                    content_encoding="utf-8",
                    timestamp=now,
                    app_id="/test_p05",
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                    headers={
                        "schema": "https://schema.multiverse.io/learning_objective/changelog/2.0.1",
                        "entity_type": "learning_objective",
                        "entity_id": lo_id,
                    },
                ),
                "learning_objective.changelog",
            )
            message_count += 1
            await asyncio.sleep(0.001)  # Small delay to prevent overwhelming the system


if __name__ == "__main__":
    asyncio.run(main())
