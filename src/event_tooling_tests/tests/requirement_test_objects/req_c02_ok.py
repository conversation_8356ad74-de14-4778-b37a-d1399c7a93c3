#!/usr/bin/env -S python -u

import asyncio
import logging
import time

from event_tooling_tests.tests.test_object_utils import (
    ainput,
    connect_robust,
    declare_consumer_queue,
    declare_exchange,
)

logger = logging.getLogger("req_c02_ok")


class Consumer:
    def __init__(self, exchange: str, routing_key: str, queue: str):
        self.exchange_name = exchange
        self.routing_key = routing_key
        self.queue_name = queue
        self.message_count = 0

    async def setup(self) -> None:
        connection = await connect_robust()
        channel = await connection.channel()
        await channel.set_qos(prefetch_count=5)
        exchange = await declare_exchange(channel, self.exchange_name)
        self.queue = await declare_consumer_queue(channel, self.queue_name)
        await self.queue.bind(exchange=exchange, routing_key=self.routing_key)

    async def start(self) -> None:
        async with self.queue.iterator() as q:
            async for msg in q:
                self.message_count += 1
                logger.info(f"{self.queue_name}: {self.message_count}")
                # Do some busy work
                time.sleep(0.02)
                await msg.ack()


async def main() -> None:
    logging.basicConfig(level=logging.INFO)

    consumers = [
        Consumer("foos.topic_exchange", "foo.changelog", "test_c02.foo_events.queue"),
        Consumer("bars.topic_exchange", "bar.changelog", "test_c02.bar_events.queue"),
        Consumer("bazs.topic_exchange", "baz.changelog", "test_c02.baz_events.queue"),
    ]

    for c in consumers:
        await c.setup()

    print("ready")

    await ainput("consume")

    await asyncio.gather(*[c.start() for c in consumers])


if __name__ == "__main__":
    asyncio.run(main())
