#!/usr/bin/env -S python -u

import asyncio
import logging

from event_tooling_tests.tests.test_object_utils import connect_robust


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await connect_robust()

    async with connection:
        # Create channels (1 for each publisher)
        await connection.channel()
        await connection.channel()
        await connection.channel()

        print("started")
        assert input() == "finished"


if __name__ == "__main__":
    asyncio.run(main())
