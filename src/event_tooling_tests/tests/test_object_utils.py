import asyncio
import os

import aio_pika
from aio_pika.abc import (
    AbstractRobustConnection,
    AbstractChannel,
    AbstractQueue,
    AbstractExchange,
    Arguments,
)


async def connect_robust() -> AbstractRobustConnection:
    return await aio_pika.connect_robust(
        host=os.getenv("RABBITMQ_HOST", "localhost"),
        port=int(os.getenv("RABBITMQ_PORT", 5672)),
        login=os.getenv("RABBITMQ_USERNAME", "guest"),
        password=os.getenv("RABBITMQ_PASSWORD", "guest"),
        virtualhost=os.getenv("RABBITMQ_VHOST", "/"),
    )


async def ainput(prompt: str = "") -> str:
    return await asyncio.to_thread(input, prompt)


async def declare_consumer_queue(
    channel: AbstractChannel, name: str, arguments: Arguments | None = None
) -> AbstractQueue:
    return await channel.declare_queue(
        name,
        durable=True,
        arguments={
            "x-queue-type": "quorum",
            "x-single-active-consumer": True,
            **(arguments or {}),
        },
    )


async def declare_exchange(channel: AbstractChannel, name: str) -> AbstractExchange:
    return await channel.declare_exchange(
        name, type=aio_pika.ExchangeType.TOPIC, durable=True
    )
