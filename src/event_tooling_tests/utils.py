import asyncio
from datetime import UTC, datetime
import json
from typing import Any
import uuid

from event_tooling_test_engine.rabbitmq_vhost_driver_protocol import RabbitMQVHostDriver


async def wait_for_api_to_catch_up(delay: float = 5) -> None:
    await asyncio.sleep(delay)


async def declare_topic_exchange(vhost: RabbitMQVHostDriver, name: str) -> None:
    await vhost.ensure_exchange(name, exchange_type="topic", durable=True)


def envelop(
    event: Any,
    *,
    id: uuid.UUID | None = None,
    source: str,
    type: str,
    dataschema: str,
    time: datetime | None = None,
) -> dict[str, Any]:
    """
    Wrap an event payload in a cloud envelope
    """
    return {
        "id": str(id or uuid.uuid4()),
        "source": source,
        "specversion": "1.0",
        "type": type,
        "datacontenttype": "application/json",
        "dataschema": dataschema,
        "data": event,
        "time": (time or datetime.now(UTC)).isoformat(),
    }


async def publish_event(
    vhost: RabbitMQVHostDriver,
    exchange: str,
    routing_key: str,
    event: Any,
    *,
    event_id: uuid.UUID | None = None,
    source: str,
    type: str,
    dataschema: str,
    time: datetime | None = None,
    entity_id: str | None = None,
    entity_type: str | None = None,
) -> None:
    """
    Publish an event to an exchange, wrapping it in a cloud envelope first and
    setting all necessary properties and headers
    """
    time = time or datetime.now(UTC)
    await vhost.publish_message(
        exchange=exchange,
        routing_key=routing_key,
        payload=json.dumps(
            envelop(
                event,
                id=event_id,
                source=source,
                type=type,
                dataschema=dataschema,
                time=time,
            )
        ),
        properties={
            "delivery_mode": 2,
            "content_type": "application/json",
            "content_encoding": "UTF-8",
            "app_id": source,
            "timestamp": int(time.timestamp()),
        },
        headers={
            "schema": dataschema,
            "entity_id": entity_id or str(event["id"]),
            "entity_type": entity_type or routing_key.split(".")[0],
        },
    )
