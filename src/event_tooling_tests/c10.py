from datetime import UTC, datetime
import json
from event_tooling_test_engine.test_context_protocol import TestContext
from event_tooling_tests.utils import publish_event

# https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r18&view=modal


class C10Test:
    """
    The test object for this requirement should do this:

    1. Setup a consumer for events 'multiverse.foo.changelog.v1', service name
       'test_c10'

    2. print "started"

    3. Consume messages - for each message print two lines
        - first line contains the json-serialized dictionary of the message properties
        - second line contains the json-serialized dictionary of the message headers
    """

    requirement_code = "C-10"
    test_object_prefix = "req_c10"

    async def run(self, ctx: TestContext) -> None:
        # Create the exchange so the consumer can bind the queue
        await ctx.vhost.ensure_exchange(
            "foos.topic_exchange", exchange_type="topic", durable=True
        )

        async with ctx.proc as proc:
            await proc.expect("started", timeout=1)

            now = int(datetime.now(UTC).timestamp())
            await publish_event(
                ctx.vhost,
                exchange="foos.topic_exchange",
                routing_key="foo.changelog",
                event={"id": "123"},
                source="/req_c10",
                dataschema="https://schema.multiverse.io/foo/changelog/1.0.0",
                type="multiverse.foo.changelog.v1",
                entity_type="foo",
            )

            props = json.loads(await proc.getline())
            headers = json.loads(await proc.getline())

            assert props["content_type"] == "application/json"
            assert props["content_encoding"] == "UTF-8"
            assert props["app_id"] == "/req_c10"
            assert props["timestamp"] == now

            assert (
                headers["schema"] == "https://schema.multiverse.io/foo/changelog/1.0.0"
            )
            assert headers["entity_type"] == "foo"
            assert headers["entity_id"] == "123"
