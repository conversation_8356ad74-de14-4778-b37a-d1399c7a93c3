import pathlib

from event_tooling_test_engine.test_context_impl import (
    TestContextImpl as TstContextImpl,
)


scripts_dir = pathlib.Path(__file__).parent.resolve() / "scripts"


async def test_test_context_with_consumer() -> None:
    # In the test context
    async with TstContextImpl(test_object_path=scripts_dir / "consumer.py") as ctx:
        # With the consumer script running
        async with ctx.proc as proc:
            # Expect the exchange name on the script's stdout
            exchange = await proc.getline()

            # Expect the routing key on the script's stdout
            routing_key = await proc.getline()

            # Publish a message to the queue
            await ctx.vhost.publish_message(
                exchange, routing_key=routing_key, payload="hello", properties={}
            )

            # Expect it to be echoed on the script's stdout
            await proc.expect("hello")

            # Publish a done to the queue
            await ctx.vhost.publish_message(
                exchange, routing_key=routing_key, payload="done", properties={}
            )

            # Expect the script to terminate
            await proc.wait()


async def test_test_context_with_producer() -> None:
    exchange_name = "test_exchange"
    routing_key = "routing_key"
    queue_name = "test_queue"
    async with TstContextImpl(test_object_path=scripts_dir / "producer.py") as ctx:
        # Prepare an exchange, a queue and a binding
        await ctx.vhost.ensure_exchange(exchange_name, exchange_type="direct")
        await ctx.vhost.ensure_queue(queue_name)
        await ctx.vhost.ensure_binding(
            from_exchange=exchange_name,
            to_queue=queue_name,
            routing_key=routing_key,
        )

        async with ctx.proc as proc:
            # Send the exchange to publish to on script's stdin
            await proc.send(exchange_name)

            # Send the routing key to use on script's stdin
            await proc.send(routing_key)

            # Send two message payloads to publish on script's stdin
            await proc.send("hello")
            await proc.send("bye")

            # Tell the script to stop when done
            await proc.send("done")
            await proc.wait()

            # Check the messages were published (they should now be on the queue)
            messages = await ctx.vhost.get_messages(queue_name, 2)

            assert len(messages) == 2
            assert [msg.payload for msg in messages] == ["hello", "bye"]
