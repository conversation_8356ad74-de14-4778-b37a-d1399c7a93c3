import logging
import pathlib
import pytest
from event_tooling_test_engine import errors
from event_tooling_test_engine.process_driver_impl import ProcessDriverImpl


scripts_dir = pathlib.Path(__file__).parent.resolve() / "scripts"


@pytest.mark.asyncio
async def test_expect_correct_output() -> None:
    proc = ProcessDriverImpl(scripts_dir / "ready.py")
    await proc.start()
    await proc.expect("ready")
    await proc.stop()


@pytest.mark.asyncio
async def test_expect_incorrect_output() -> None:
    proc = ProcessDriverImpl(scripts_dir / "ready.py")
    await proc.start()
    with pytest.raises(errors.ExpectationFailed):
        await proc.expect("not ready")
    await proc.stop()


@pytest.mark.asyncio
async def test_getline() -> None:
    async with ProcessDriverImpl(scripts_dir / "ready.py") as proc:
        for _ in range(10):
            line = await proc.getline()
            assert line == "ready"

        # Now the process has finished so the line returned is empty
        line = await proc.getline()
        assert line == ""


@pytest.mark.asyncio
async def test_send() -> None:
    async with ProcessDriverImpl(scripts_dir / "slow_echo.py") as proc:
        for line in "echo ECHO ...cho".split():
            await proc.send(line)
            recv = await proc.getline()
            assert recv == line


@pytest.mark.asyncio
async def test_getline_timeout() -> None:
    async with ProcessDriverImpl(scripts_dir / "slow_echo.py") as proc:
        await proc.send("echo")
        with pytest.raises(errors.Timeout):
            await proc.getline(timeout=0.05)


@pytest.mark.asyncio
async def test_expect_timeout() -> None:
    async with ProcessDriverImpl(scripts_dir / "slow_echo.py") as proc:
        await proc.send("echo")
        with pytest.raises(errors.Timeout):
            await proc.expect("echo", timeout=0.05)


@pytest.mark.asyncio
async def test_env_vars() -> None:
    env = {
        "TEST_ID": "123",
        "HOSTNAME": "snapper",
    }
    proc = ProcessDriverImpl(scripts_dir / "print_env.py", env=env)
    proc.update_env(VHOST="vhost_x13Y")
    async with proc:
        for name, value in env.items():
            await proc.send(name)
            await proc.expect(value)
        await proc.send("VHOST")
        await proc.expect("vhost_x13Y")


@pytest.mark.asyncio
async def test_logging_of_process(caplog: pytest.LogCaptureFixture) -> None:
    with caplog.at_level(logging.INFO):
        logger = logging.getLogger("test")
        async with ProcessDriverImpl(scripts_dir / "ready.py", logger=logger) as proc:
            for i in range(10):
                await proc.expect("ready")
                assert f"[log {i}]" in caplog.text
