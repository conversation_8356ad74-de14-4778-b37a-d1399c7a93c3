import pathlib
import pytest
from event_tooling_test_engine.requirement_tests import RequirementTestSuite
from event_tooling_test_engine.test_context_protocol import TestContext as TstContext


scripts_dir = pathlib.Path(__file__).parent.resolve() / "scripts"  # noqa: F821


@pytest.fixture
def requirement_testsuite1() -> RequirementTestSuite:
    class P01Test:
        requirement_code = "P-01"
        test_object_prefix = "req_p01"

        async def run(self, ctx: TstContext) -> None:
            async with ctx.proc as proc:
                await proc.expect("p01 ok")

    class P02Test:
        requirement_code = "P-02"
        test_object_prefix = "req_p02"

        async def run(self, ctx: TstContext) -> None:
            async with ctx.proc as proc:
                ctx.logger.debug("sending 'foo'")
                await proc.send("foo")
                ctx.logger.info("expecting 'foo' in return")
                await proc.expect("foo")

    suite = RequirementTestSuite()
    suite.register(P01Test())
    suite.register(P02Test())

    return suite


async def test_run_test_suite1_with_no_tests(
    requirement_testsuite1: RequirementTestSuite,
) -> None:
    results = await requirement_testsuite1.run_tests(scripts_dir)
    assert not results


async def test_run_test_suite1_with_shell_scripts(
    requirement_testsuite1: RequirementTestSuite,
) -> None:
    results = await requirement_testsuite1.run_tests(
        scripts_dir / "suite1_test_objects"
    )
    assert len(results) == 4
    assert sum(1 for result in results if result.passed) == 2
    assert all(
        "_ok." in result.test_object_path.name for result in results if result.passed
    )
    assert all(
        "_fail." in result.test_object_path.name
        for result in results
        if not result.passed
    )
    for result in results:
        if result.requirement_test.requirement_code == "P-02":
            assert result.logs
            assert "sending 'foo'" in result.logs
            assert "expecting 'foo' in return" in result.logs
