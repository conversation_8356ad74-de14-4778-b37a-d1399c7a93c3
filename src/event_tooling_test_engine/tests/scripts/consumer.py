#!/usr/bin/env -S python -u

import asyncio
import logging
import os

import aio_pika


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await aio_pika.connect_robust(
        host=os.getenv("RABBITMQ_HOST", "localhost"),
        port=int(os.getenv("RABBITMQ_PORT", 5672)),
        login=os.getenv("RABBITMQ_USERNAME", "guest"),
        password=os.getenv("RABBITMQ_PASSWORD", "guest"),
        virtualhost=os.getenv("RABBITMQ_VHOST", "/"),
    )

    exchange_name = "test_exchange"
    queue_name = "test_queue"
    routing_key = "test_routing_key"

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare exchange
        exchange = await channel.declare_exchange(exchange_name)

        # Send its name on stdout
        print(exchange_name)

        # Declare queue
        queue = await channel.declare_queue(queue_name, auto_delete=True)

        # Bind it to the exchange
        await queue.bind(exchange=exchange, routing_key=routing_key)

        # Send the routing key on stdout
        print(routing_key)

        # Echo incoming messages on the queue, until "done" is received
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    body = message.body.decode()
                    if body == "done":
                        return
                    print(body)


if __name__ == "__main__":
    asyncio.run(main())
