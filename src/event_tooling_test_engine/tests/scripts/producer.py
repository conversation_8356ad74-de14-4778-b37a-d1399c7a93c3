#!/usr/bin/env -S python -u

import asyncio
import logging
import os

import aio_pika


async def main() -> None:
    logging.basicConfig(level=logging.DEBUG)
    connection = await aio_pika.connect_robust(
        host=os.getenv("RABBITMQ_HOST", "localhost"),
        port=int(os.getenv("RABBITMQ_PORT", 5672)),
        login=os.getenv("RABBITMQ_USERNAME", "guest"),
        password=os.getenv("RABBITMQ_PASSWORD", "guest"),
        virtualhost=os.getenv("RABBITMQ_VHOST", "/"),
    )

    # Expect the exchange to publish to on stdin
    exchange_name = input()

    # Expect the routing key on stdin
    routing_key = input()

    async with connection:
        # Create channel
        channel = await connection.channel()

        # Declare exchange
        exchange = await channel.declare_exchange(exchange_name)

        while True:
            # Expect a message to publish on stdin
            message = input()
            if message == "done":
                return

            # Publish it
            await exchange.publish(
                aio_pika.Message(body=message.encode()), routing_key=routing_key
            )


if __name__ == "__main__":
    asyncio.run(main())
