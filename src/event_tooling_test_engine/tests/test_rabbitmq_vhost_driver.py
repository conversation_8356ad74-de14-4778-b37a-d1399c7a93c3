import asyncio
import random
import string
from typing import As<PERSON><PERSON>enerator
import aio_pika
import pytest

from event_tooling_test_engine import errors
from event_tooling_test_engine.rabbitmq_driver import RabbitMQDriver
from event_tooling_test_engine.rabbitmq_vhost_driver_impl import RabbitMQVHostDriverImpl


@pytest.fixture
async def rabbitmq() -> AsyncGenerator[RabbitMQDriver, None]:
    async with RabbitMQDriver() as driver:
        yield driver


@pytest.fixture
async def vhost(rabbitmq: RabbitMQDriver) -> RabbitMQVHostDriverImpl:
    rand_suffix = "".join(random.choices(string.ascii_letters + string.digits, k=8))
    return RabbitMQVHostDriverImpl(rabbitmq, "test_vhost_" + rand_suffix)


@pytest.mark.asyncio
async def test_prerequisites(
    rabbitmq: RabbitMQDriver, vhost: RabbitMQVHostDriverImpl
) -> None:
    try:
        await rabbitmq.overview()
    except errors.RabbitMQServerError:
        raise Exception(
            "Cannot reach /api/overview, check you have a RabbitMQ cluster running "
            "and valid credentials are in RABBITMQ_HOST, RABBITMQ_USERNAME, RABBITMQ_PASSWORD."
        )


@pytest.mark.asyncio
async def test_vhost_created_and_removed(
    rabbitmq: RabbitMQDriver, vhost: RabbitMQVHostDriverImpl
) -> None:
    vhost_list_before = await rabbitmq.vhosts()
    assert vhost.name not in [item.name for item in vhost_list_before]

    await vhost.start()
    vhost_list_during = await rabbitmq.vhosts()
    assert vhost.name in [item.name for item in vhost_list_during]

    await vhost.stop()
    vhost_list_after = await rabbitmq.vhosts()
    assert vhost.name not in [item.name for item in vhost_list_after]


@pytest.mark.asyncio
async def test_vhost_created_and_removed_in_context_processor(
    rabbitmq: RabbitMQDriver, vhost: RabbitMQVHostDriverImpl
) -> None:
    vhost_list_before = await rabbitmq.vhosts()
    assert vhost.name not in [item.name for item in vhost_list_before]

    async with vhost:
        vhost_list_during = await rabbitmq.vhosts()
        assert vhost.name in [item.name for item in vhost_list_during]

    vhost_list_after = await rabbitmq.vhosts()
    assert vhost.name not in [item.name for item in vhost_list_after]


@pytest.mark.parametrize("durable", (True, False))
@pytest.mark.parametrize("auto_delete", (True, False))
@pytest.mark.asyncio
async def test_vhost_creates_queue_with_parameters(
    vhost: RabbitMQVHostDriverImpl, durable: bool, auto_delete: bool
) -> None:
    async with vhost:
        await vhost.ensure_queue(
            "test_queue",
            durable=durable,
            auto_delete=auto_delete,
        )
        queue = await vhost.queue("test_queue")

        assert queue.name == "test_queue"
        assert queue.durable == durable
        assert queue.auto_delete == auto_delete


@pytest.mark.asyncio
async def test_vhost_creates_queue_with_arguments(
    vhost: RabbitMQVHostDriverImpl,
) -> None:
    async with vhost:
        xarg1 = 12
        xarg2 = "abs"
        arguments = {"x-arg1": xarg1, "x-arg2": xarg2}
        await vhost.ensure_queue("test_queue", arguments=arguments)
        queue = await vhost.queue("test_queue")

        assert queue.arguments["x-arg1"] == xarg1
        assert queue.arguments["x-arg2"] == xarg2


@pytest.mark.asyncio
async def test_vhost_deletes_queue(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        await vhost.ensure_queue("test_queue")

        queue = await vhost.queue("test_queue")

        assert queue.name == "test_queue"
        assert queue.durable is False
        assert queue.auto_delete is False

        await vhost.delete_queue("test_queue")

        with pytest.raises(errors.RabbitMQNotFound):
            await vhost.queue("test_queue")


@pytest.mark.parametrize("durable", (True, False))
@pytest.mark.parametrize("auto_delete", (True, False))
@pytest.mark.parametrize("exchange_type", ("direct", "fanout", "headers", "topic"))
@pytest.mark.asyncio
async def test_vhost_creates_exchange_with_parameters(
    vhost: RabbitMQVHostDriverImpl, durable: bool, auto_delete: bool, exchange_type: str
) -> None:
    async with vhost:
        await vhost.ensure_exchange(
            "test_exchange",
            exchange_type=exchange_type,
            durable=durable,
            auto_delete=auto_delete,
        )
        exchange = await vhost.exchange("test_exchange")

        assert exchange.name == "test_exchange"
        assert exchange.durable is durable
        assert exchange.auto_delete is auto_delete
        assert exchange.type == exchange_type


@pytest.mark.asyncio
async def test_vhost_creates_exchange_with_arguments(
    vhost: RabbitMQVHostDriverImpl,
) -> None:
    async with vhost:
        arguments = {"x-arg1": 12, "x-arg2": "abs"}
        await vhost.ensure_exchange(
            "test_exchange", exchange_type="direct", arguments=arguments
        )
        exchange = await vhost.exchange("test_exchange")

        assert exchange.arguments["x-arg1"] == 12
        assert exchange.arguments["x-arg2"] == "abs"


@pytest.mark.asyncio
async def test_vhost_deletes_exchange(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        await vhost.ensure_exchange("test_exchange", exchange_type="direct")

        exchange = await vhost.exchange("test_exchange")

        assert exchange.name == "test_exchange"
        assert exchange.durable is False
        assert exchange.auto_delete is False

        await vhost.delete_exchange("test_exchange")

        with pytest.raises(errors.RabbitMQNotFound):
            await vhost.exchange("test_exchange")


@pytest.mark.asyncio
async def test_vhost_lists_exchanges(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        exchange_names = [f"test_exchange_{i}" for i in range(10)]
        for name in exchange_names:
            await vhost.ensure_exchange(name, exchange_type="direct")

        exchanges = await vhost.exchanges()

        assert set(exchange.name for exchange in exchanges) >= set(exchange_names)


async def test_vhost_creates_binding(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        await vhost.ensure_exchange("test_exchange", exchange_type="direct")
        await vhost.ensure_queue("test_queue")
        await vhost.ensure_binding(
            from_exchange="test_exchange",
            to_queue="test_queue",
            routing_key="abc",
            arguments={"test_arg": "test_arg_value"},
        )
        bindings = await vhost.bindings(
            from_exchange="test_exchange", to_queue="test_queue"
        )

        assert len(bindings) == 1
        binding = bindings[0]

        assert binding.source == "test_exchange"
        assert binding.destination == "test_queue"
        assert binding.destination_type == "queue"
        assert binding.routing_key == "abc"
        assert binding.arguments == {"test_arg": "test_arg_value"}


@pytest.mark.asyncio
async def test_vhost_publishes_message_no_queue(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        await vhost.ensure_exchange("test_exchange", exchange_type="direct")

        result = await vhost.publish_message(
            "test_exchange", routing_key="abc", payload="hello", properties={}
        )
        assert not result.routed


@pytest.mark.asyncio
async def test_vhost_publishes_message(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        await vhost.ensure_exchange("test_exchange", exchange_type="direct")
        await vhost.ensure_queue("test_queue")
        await vhost.ensure_binding(
            from_exchange="test_exchange", to_queue="test_queue", routing_key="abc"
        )

        result = await vhost.publish_message(
            "test_exchange", routing_key="abc", payload="hello", properties={}
        )
        assert result.routed


@pytest.mark.asyncio
async def test_vhost_gets_messages(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        await vhost.ensure_exchange("test_exchange", exchange_type="direct")
        await vhost.ensure_queue("test_queue")
        await vhost.ensure_binding(
            from_exchange="test_exchange", to_queue="test_queue", routing_key="abc"
        )

        for i in range(10):
            await vhost.publish_message(
                "test_exchange",
                routing_key="abc",
                payload="hello",
                properties={"message_id": f"msg-{i}"},
                headers={"x_foo": "bar"},
            )

        received = await vhost.get_messages("test_queue", count=100)

        assert len(received) == 10

        for i, msg in enumerate(received):
            assert msg.payload == "hello"
            assert msg.properties.message_id == f"msg-{i}"
            assert msg.properties.headers.x_foo == "bar"


async def test_vhost_connections(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        connections_before = await vhost.connections()
        assert len(connections_before) == 0

        connections = [await aio_pika.connect(virtualhost=vhost.name) for _ in range(5)]

        await wait_for_api_to_catch_up()

        connections_during = await vhost.connections()
        assert len(connections_during) == 5

        for c in connections:
            await c.close()

        await wait_for_api_to_catch_up()

        connections_after = await vhost.connections()
        assert len(connections_after) == 0


async def test_vhost_closes_connection(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        connection = await aio_pika.connect(virtualhost=vhost.name)

        assert connection.connected.is_set()

        await wait_for_api_to_catch_up()

        connections = await vhost.connections()
        assert len(connections) == 1
        for c in connections:
            await vhost.close_connection(c.name)

        await wait_for_api_to_catch_up()

        assert not connection.connected.is_set()

        connections_after = await vhost.connections()
        assert len(connections_after) == 0


async def test_vhost_channels(vhost: RabbitMQVHostDriverImpl) -> None:
    async with vhost:
        connection = await aio_pika.connect(virtualhost=vhost.name)

        channels = [await connection.channel() for _ in range(5)]

        await wait_for_api_to_catch_up()

        channels = await vhost.channels()
        assert len(channels) == 5


async def wait_for_api_to_catch_up() -> None:
    await asyncio.sleep(5)
