import typing
from types import TracebackType
from typing import Any

from event_tooling_test_engine.rabbitmq_driver import RabbitMQDriver
from event_tooling_test_engine.rabbitmq_types import DictView, ListView, Pagination
from event_tooling_test_engine.rabbitmq_vhost_driver_protocol import RabbitMQVHostDriver


class RabbitMQVHostDriverImpl:
    _name: str
    _rabbitmq: RabbitMQDriver

    def __init__(self, api: RabbitMQDriver, name: str):
        self._rabbitmq = api
        self._name = name

    @property
    def name(self) -> str:
        return self._name

    async def start(self) -> "RabbitMQVHostDriverImpl":
        await self._rabbitmq.ensure_vhost(self._name)
        return self

    async def stop(self) -> None:
        await self._rabbitmq.delete_vhost(self._name)

    async def __aenter__(self) -> "RabbitMQVHostDriverImpl":
        return await self.start()

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        await self.stop()

    async def get(self) -> DictView:
        return await self._rabbitmq.vhost(name=self._name)

    async def queue(
        self,
        name: str,
        enable_queue_totals: bool = True,
        disable_stats: bool = True,
    ) -> DictView:
        return await self._rabbitmq.queue(
            vhost=self._name,
            name=name,
            disable_stats=disable_stats,
            enable_queue_totals=enable_queue_totals,
        )

    async def ensure_queue(
        self,
        name: str,
        auto_delete: bool = False,
        durable: bool = False,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        await self._rabbitmq.ensure_queue(
            vhost=self._name,
            name=name,
            auto_delete=auto_delete,
            durable=durable,
            arguments=arguments,
        )

    async def delete_queue(
        self,
        name: str,
        if_empty: bool | None = None,
        if_unused: bool | None = None,
    ) -> None:
        await self._rabbitmq.delete_queue(
            vhost=self._name, name=name, if_empty=if_empty, if_unused=if_unused
        )

    async def get_messages(
        self,
        queue: str,
        count: int,
        ackmode: str = "ack_requeue_true",
        encoding: str = "auto",
        truncate: int | None = None,
    ) -> ListView:
        return await self._rabbitmq.get_messages(
            vhost=self._name,
            queue=queue,
            count=count,
            ackmode=ackmode,
            encoding=encoding,
            truncate=truncate,
        )

    async def publish_message(
        self,
        exchange: str,
        routing_key: str,
        payload: str | bytes,
        properties: dict[str, Any],
        headers: dict[str, Any] | None = None,
    ) -> DictView:
        return await self._rabbitmq.publish_message(
            self._name,
            exchange=exchange,
            routing_key=routing_key,
            payload=payload,
            properties=properties,
            headers=headers,
        )

    async def exchanges(self, pagination: Pagination | None = None) -> ListView:
        return await self._rabbitmq.exchanges(vhost=self._name, pagination=pagination)

    async def exchange(self, name: str) -> DictView:
        return await self._rabbitmq.exchange(vhost=self._name, name=name)

    async def ensure_exchange(
        self,
        name: str,
        exchange_type: str,
        auto_delete: bool | None = None,
        durable: bool | None = None,
        internal: bool | None = None,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        return await self._rabbitmq.ensure_exchange(
            self._name,
            name,
            exchange_type,
            auto_delete=auto_delete,
            durable=durable,
            internal=internal,
            arguments=arguments,
        )

    async def delete_exchange(self, name: str, if_unused: bool | None = None) -> None:
        return await self._rabbitmq.delete_exchange(self._name, name, if_unused)

    async def bindings(
        self,
        from_exchange: str | None = None,
        to_queue: str | None = None,
        to_exchange: str | None = None,
    ) -> ListView:
        return await self._rabbitmq.bindings(
            vhost=self._name,
            from_exchange=from_exchange,
            to_queue=to_queue,
            to_exchange=to_exchange,
        )

    async def ensure_binding(
        self,
        from_exchange: str,
        to_queue: str | None = None,
        to_exchange: str | None = None,
        routing_key: str | None = None,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        return await self._rabbitmq.ensure_binding(
            self._name,
            from_exchange=from_exchange,
            to_queue=to_queue,
            to_exchange=to_exchange,
            routing_key=routing_key,
            arguments=arguments,
        )

    async def connections(
        self, user: str | None = None, pagination: Pagination | None = None
    ) -> ListView:
        return await self._rabbitmq.connections(
            self._name, user=user, pagination=pagination
        )

    async def connection(self, name: str) -> DictView:
        return await self._rabbitmq.connection(name)

    async def close_connection(self, name: str) -> None:
        return await self._rabbitmq.close_connection(name)

    async def channels(
        self,
        connection: str | None = None,
        pagination: Pagination | None = None,
    ) -> ListView:
        return await self._rabbitmq.channels(
            self._name, connection=connection, pagination=pagination
        )


if typing.TYPE_CHECKING:
    _: type[RabbitMQVHostDriver] = RabbitMQVHostDriverImpl
