import logging
import os
import pathlib
import random
import string
from types import TracebackType
import typing
from event_tooling_test_engine.process_driver_impl import ProcessDriverImpl
from event_tooling_test_engine.rabbitmq_driver import RabbitMQDriver
from event_tooling_test_engine.rabbitmq_vhost_driver_impl import RabbitMQVHostDriverImpl
from event_tooling_test_engine.test_context_protocol import TestContext


class TestContextImpl:
    _rabbitmq_host: str
    _rabbitmq_port: int
    _rabbitmq_http_port: int
    _rabbitmq_username: str
    _rabbitmq_password: str
    _rabbitmq: RabbitMQDriver
    _vhost_name: str
    _vhost: RabbitMQVHostDriverImpl
    _logger: logging.Logger
    _process: ProcessDriverImpl

    def __init__(
        self,
        test_object_path: str | pathlib.Path,
        rabbitmq_host: str = os.getenv("RABBITMQ_HOST", "localhost"),
        rabbitmq_http_port: int = int(os.getenv("RABBITMQ_HTTP_PORT", "15672")),
        rabbitmq_port: int = int(os.getenv("RABBITMQ_PORT", "5672")),
        rabbitmq_username: str = os.getenv("RABBITMQ_USERNAME", "guest"),
        rabbitmq_password: str = os.getenv("RABBITMQ_PASSWORD", "guest"),
        vhost_name_prefix: str = "test_vhost_",
    ):
        self._rabbitmq_host = rabbitmq_host
        self._rabbitmq_port = rabbitmq_port
        self._rabbitmq_http_port = rabbitmq_http_port
        self._rabbitmq_username = rabbitmq_username
        self._rabbitmq_password = rabbitmq_password
        test_identifier = "".join(
            random.choices(string.ascii_letters + string.digits, k=8)
        )
        self._logger = logging.getLogger(f"test_{test_identifier}")
        self._vhost_name = vhost_name_prefix + test_identifier
        self._process = ProcessDriverImpl(
            exec_path=test_object_path, logger=self._logger
        )
        self._process.update_env(
            RABBITMQ_HOST=rabbitmq_host,
            RABBITMQ_PORT=rabbitmq_port,
            RABBITMQ_USERNAME=rabbitmq_username,
            RABBITMQ_PASSWORD=rabbitmq_password,
            RABBITMQ_VHOST=self._vhost_name,
            TEST_ID=test_identifier,
        )

    async def start(self) -> "TestContextImpl":
        self._rabbitmq = RabbitMQDriver(
            host=f"http://{self._rabbitmq_host}:{self._rabbitmq_http_port}",
            username=self._rabbitmq_username,
            password=self._rabbitmq_password,
            logger=self._logger,
        )
        self._logger.info(f"Creating vhost {self._vhost_name}...")
        self._vhost = RabbitMQVHostDriverImpl(self._rabbitmq, self._vhost_name)
        await self._vhost.start()
        return self

    async def stop(self) -> None:
        self._logger.info(f"Deleting vhost {self._vhost_name}")
        await self._vhost.stop()
        del self._vhost
        await self._rabbitmq.close()
        await self._process.stop()

    async def __aenter__(self) -> "TestContextImpl":
        return await self.start()

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        await self.stop()

    @property
    def vhost(self) -> RabbitMQVHostDriverImpl:
        return self._vhost

    @property
    def proc(self) -> ProcessDriverImpl:
        return self._process

    @property
    def logger(self) -> logging.Logger:
        return self._logger


if typing.TYPE_CHECKING:
    _: type[TestContext] = TestContextImpl
