class Base(Exception):
    pass


class Timeout(Base):
    pass


class ExpectationFailed(Base):
    pass


class RabbitMQError(Base):
    pass


class RabbitMQClientError(RabbitMQError):
    def __init__(self, status: int, reason: str):
        self.status = status
        self.reason = reason

    def __str__(self) -> str:
        return f"{self.__class__.__name__}: {self.reason} (status = {self.status})"


class RabbitMQServerError(RabbitMQError):
    pass


class RabbitMQNotFound(RabbitMQClientError):
    pass
