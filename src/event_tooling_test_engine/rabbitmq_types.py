from collections.abc import Iterator
from dataclasses import dataclass
from typing import Any, SupportsIndex


@dataclass
class Pagination:
    page: int
    page_size: int
    name: str
    use_regex: bool


class DictView(dict):
    def __getattr__(self, item: Any) -> Any:
        return as_view(self[item])


class ListView(list):
    def __getitem__(self, index: SupportsIndex | slice) -> Any:
        if isinstance(index, SupportsIndex):
            return as_view(super().__getitem__(index))
        else:
            raise NotImplementedError(f"cannot take slice of ListView: {index!r}")

    def __iter__(self) -> Iterator:
        return IteratorView(super().__iter__())


class IteratorView(Iterator):
    def __init__(self, iterator: Iterator):
        self.iterator = iterator

    def __iter__(self) -> Iterator:
        return self

    def __next__(self) -> Any:
        return as_view(next(self.iterator))


def as_view(obj: Any) -> Any:
    match obj:
        case dict():
            return DictView(obj)
        case list():
            return ListView(obj)
        case _:
            return obj
