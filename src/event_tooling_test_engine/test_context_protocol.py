import logging
import typing

from event_tooling_test_engine.process_driver_protocol import ProcessDriver
from event_tooling_test_engine.rabbitmq_vhost_driver_protocol import RabbitMQVHostDriver


class TestContext(typing.Protocol):
    @property
    def proc(self) -> ProcessDriver:
        """
        A process running the test object that is being tested.
        """

    @property
    def vhost(self) -> RabbitMQVHostDriver:
        """
        The RabbitMQ vhost that is used for the test (typically this vhost
        would be created just before the test and deleted straight after).
        """

    @property
    def logger(self) -> logging.Logger:
        """
        A logger that can be use for notifications and debugging purposes.
        """
