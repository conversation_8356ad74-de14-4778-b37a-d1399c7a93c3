import asyncio
from dataclasses import dataclass
import io
import logging
import pathlib
import sys
import traceback
import typing

from event_tooling_test_engine.test_context_impl import TestContextImpl
from event_tooling_test_engine.test_context_protocol import TestContext


class RequirementTest(typing.Protocol):
    requirement_code: str

    # Filename prefix to look for when searching for test objects for this test.
    test_object_prefix: str

    async def run(self, ctx: TestContext) -> None:
        """
        The test implementation
        """


@dataclass
class RequirementTestResult:
    requirement_test: RequirementTest
    test_object_path: pathlib.Path
    error: Exception | None
    logs: str | None

    @property
    def passed(self) -> bool:
        return self.error is None

    def __str__(self) -> str:
        status = "[OK  ]" if self.passed else "[FAIL]"
        return f"{status} {self.requirement_test.requirement_code}/{self.test_object_path.name}"


@dataclass
class RequirementTestRunner:
    requirement_test: RequirementTest
    test_object_path: pathlib.Path

    async def run_test(
        self, capture_logs: bool = True, log_level: int = logging.DEBUG
    ) -> RequirementTestResult:
        """
        Run a single test found at `test_object_path` against the given
        RequirementTest instance.  The result contains
        - an `error` field that is an exception if the test failed, None otherwise
        - a `logs` field that contains the logs emitted by the test run
        """
        ctx = TestContextImpl(test_object_path=self.test_object_path)
        if capture_logs:
            log_stream = io.StringIO()
            handler = logging.StreamHandler(log_stream)
            ctx.logger.addHandler(handler)
        ctx.logger.setLevel(log_level)
        error = None

        try:
            async with ctx:
                await self.requirement_test.run(ctx)
        except Exception as exc:
            error = exc

        logs = None
        if capture_logs:
            logs = log_stream.getvalue()
            log_stream.close()

        return RequirementTestResult(
            requirement_test=self.requirement_test,
            test_object_path=self.test_object_path,
            error=error,
            logs=logs,
        )

    def __str__(self) -> str:
        return f"{self.requirement_test.requirement_code}/{self.test_object_path.name}"


class RequirementTestSuite:
    """
    A suite of requirement tests.
    """

    _tests: list[RequirementTest]

    def __init__(self) -> None:
        self._tests = []

    def register(self, test: RequirementTest) -> None:
        """
        Register a new RequirementTest instance with the test suite.
        """
        self._tests.append(test)

    def get_test_runners(
        self,
        test_object_dir: str | pathlib.Path,
        requirement_codes: list[str] | None = None,
    ) -> list[RequirementTestRunner]:
        """
        Find test objects in `test_object_dir` (which must be a directory) and
        runs them against the registered RequirementTest instances they
        correspond to.  The tests are run concurrently as it is assumed that
        they are mostly waiting on network IO.

        Return a list of test runners, each a coroutine returning a
        RequirementTestResult instance detailing the outcome of the test. The
        logs are captured and included in the `logs` property of the result.
        """
        tests = self._tests
        if requirement_codes is not None:
            tests = [
                test for test in tests if test.requirement_code in requirement_codes
            ]
        dir_path = pathlib.Path(test_object_dir)
        if not dir_path.is_dir():
            raise ValueError(f"'{dir_path}' is not a directory")
        runners: list[RequirementTestRunner] = []
        for item_path in dir_path.iterdir():
            if not item_path.is_file():
                continue
            for test in tests:
                if item_path.name.startswith(test.test_object_prefix):
                    runners.append(RequirementTestRunner(test, item_path))
        return runners

    async def run_tests(
        self,
        test_object_dir: str | pathlib.Path,
        requirement_codes: list[str] | None = None,
    ) -> list[RequirementTestResult]:
        """
        Find test objects in `test_object_dir` (which must be a directory) and
        runs them against the registered RequirementTest instances they
        correspond to.  The tests are run concurrently as it is assumed that
        they are mostly waiting on network IO.

        Return a list of RequirementTestResult instances, each detailing the
        outcome of the test. The logs are captured and included in the `logs`
        property of the result.
        """
        runners = [
            r.run_test()
            for r in self.get_test_runners(test_object_dir, requirement_codes)
        ]
        return await asyncio.gather(*runners)


def print_report(
    results: list[RequirementTestResult],
    file: io.TextIOBase = sys.stdout,  # type:ignore
    detailed: bool = False,
) -> None:
    successes = [r for r in results if r.passed]

    print(f"{len(successes)} passed", file=file)

    failures = [r for r in results if not r.passed]
    print(f"{len(failures)} failed", file=file)

    if not detailed:
        return

    for r in failures:
        print("-" * 40, file=file)
        print(
            f"{r.requirement_test.requirement_code}: {r.test_object_path.name}",
            file=file,
        )
        traceback.print_exception(r.error, file=file)
        print(f"\nlogs\n{r.logs}", file=file)
