import typing
from types import TracebackType


class ProcessDriver(typing.Protocol):
    """
    A protocol for controlling a process.
    """

    async def start(self) -> "ProcessDriver":
        """
        Start the process. After returning the process is started
        """

    async def stop(self, timeout: float = 1) -> None:
        """
        Stop the process gracefully, i.e. send it a SIGTERM and wait for it to terminate.
        """

    async def __aenter__(self) -> "ProcessDriver":
        """
        This allows using a process as an async context manager.  When entering the
        context manager, the process is started, when leaving it the process is
        stopped.

            async with proc:
                # Now the process is started
                proc.send('hello')
                proc.expect('hello back')

            # Now the process has terminated
        """

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """
        This allows using a process as an async context manager. See __aenter__
        for details.
        """

    async def getline(self, timeout: float = 1) -> str:
        """
        Wait for a line to be written by the process on its stdout and return it,
        with trailing whitespace removed.
        A `Timeout` exception will be raised if `timeout` is exceeded
        (timeout is measured in seconds).
        """

    async def expect(self, msg: str, timeout: float = 1) -> None:
        """
        Expect the next line to be output on the process's stdout to be equal
        to `msg` (trailing whitespace is removed before comparison).
        If `timeout` is exceeded, raise a `Timeout` exception
        If the output is not the one expected, raise an exception.
        """

    async def send(self, msg: str, timeout: float = 1) -> None:
        """
        Send the string `msg` to the process's stdin.  If `timeout` is exceeded,
        raise a Timeout exception.
        """

    def kill(self) -> None:
        """
        Send the process a SIGKILL signal (forceful exit).
        """

    def terminate(self) -> None:
        """
        Send the preocess a SIGTERM signal (graceful exit).
        """

    async def expect_match(self, pattern: str, timeout: float = 1) -> typing.Match[str]:
        """
        Wait for a line matching the given regex pattern to be written by the process
        on its stdout and return the match object.
        A `Timeout` exception will be raised if `timeout` is exceeded
        (timeout is measured in seconds).
        """

    async def wait(self, timeout: float = 1) -> None:
        """
        Wait for process to end
        """
