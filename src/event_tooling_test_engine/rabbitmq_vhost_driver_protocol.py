import typing
from typing import Any

from event_tooling_test_engine.rabbitmq_types import DictView, ListView, Pagination


class RabbitMQVHostDriver(typing.Protocol):
    """
    A protocol for monitoring and controlling a RabbitMQ vhost.
    """

    @property
    def name(self) -> str:
        """
        Return the name of the vhost
        """

    async def get(self) -> DictView:
        """
        Return configuration and status details about the vhost.
        """

    async def queue(
        self,
        name: str,
        enable_queue_totals: bool = True,
        disable_stats: bool = True,
    ) -> DictView:
        """
        Return configuration and status details about the queue called `name`.
        """

    async def ensure_queue(
        self,
        name: str,
        auto_delete: bool = False,
        durable: bool = False,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        """
        Set up a queue called `name` in the vhost with the given configuration.
        """

    async def delete_queue(
        self,
        name: str,
        if_empty: bool | None = None,
        if_unused: bool | None = None,
    ) -> None:
        """
        Delete the queue called `name`.
        """

    async def get_messages(
        self,
        queue: str,
        count: int,
        ackmode: str = "ack_requeue_true",
        encoding: str = "auto",
        truncate: int | None = None,
    ) -> ListView:
        """
        Fetch a number of messages from the `queue`.
        """

    async def publish_message(
        self,
        exchange: str,
        routing_key: str,
        payload: str | bytes,
        properties: dict[str, Any],
        headers: dict[str, Any] | None = None,
    ) -> DictView:
        """
        Publish a message to the given `exchange`.  I `payload` is a string, it
        will be sent with "string" encoding, if it is bytes it will be
        base64-encoded and sent with "base64" encoding.
        """

    async def exchanges(self, pagination: Pagination | None = None) -> ListView:
        """
        Return a list of all exchanges in the vhost.
        """

    async def exchange(self, name: str) -> DictView:
        """
        Return configuratino and status details about the exchanged called `name`.
        """

    async def ensure_exchange(
        self,
        name: str,
        exchange_type: str,
        auto_delete: bool | None = None,
        durable: bool | None = None,
        internal: bool | None = None,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        """
        Set up an exchange called `name` with type `exchange_type` and
        characteristics defined by the other arguments.
        """

    async def delete_exchange(self, name: str, if_unused: bool | None = None) -> None:
        """
        Delete the exchange named `name`.  If `if_unused` is true, the exchange
        will not be deleted if another exchange or a queue is bound to it as a
        source.
        """

    async def bindings(
        self,
        from_exchange: str | None = None,
        to_queue: str | None = None,
        to_exchange: str | None = None,
    ) -> ListView:
        """
        Return a list of bindings defined in the vhost.
        If `from_exchange` is set, only return bindings whose source is that exchange.
        If `to_queue` is set, only return bindings whose destination is that queue.
        If `to_exchange` is set, only return bindings whose destination is that exchange.
        All argument are optional, `to_queue` and `to_exchange` are mutually
        """

    async def ensure_binding(
        self,
        from_exchange: str,
        to_queue: str | None = None,
        to_exchange: str | None = None,
        routing_key: str | None = None,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        """
        Bind `from_exchange` as a source and either `to_queue` or `to_exchange`
        as a destination.  Exactly one of `to_exchange` and `to_queue` must be
        set.
        """

    async def connections(
        self, user: str | None = None, pagination: Pagination | None = None
    ) -> ListView:
        """
        Return a list of connections to the vhost.
        If `user` is set, filter by that user.
        If `pagination` is set, paginate the results according to the pagination configuration.
        """

    async def connection(self, name: str) -> DictView:
        """
        Return configuration and status details about the connection called `name`.
        """

    async def close_connection(self, name: str) -> None:
        """
        Close the connection called `name`
        """

    async def channels(
        self,
        connection: str | None = None,
        pagination: Pagination | None = None,
    ) -> ListView:
        """
        Return a list of channels in connections in the vhost.
        If `connection` is set, only return channels in this connection.
        If `pagination` is set, paginate the results according ot the pagination configuration.
        """
