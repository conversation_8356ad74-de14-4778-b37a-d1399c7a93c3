import asyncio
import logging
import os
import pathlib
from types import TracebackType
import typing
import re

from event_tooling_test_engine import errors
from event_tooling_test_engine.process_driver_protocol import ProcessDriver


class ProcessDriverImpl:
    _exec_path: str
    _exec_env: dict[str, str] | None
    _logger: logging.Logger | None
    _proc: asyncio.subprocess.Process

    def __init__(
        self,
        exec_path: str | pathlib.Path,
        env: dict[str, str] | None = None,
        logger: logging.Logger | None = None,
    ):
        self._exec_path = str(exec_path)
        self._exec_env = env and dict(env)
        self._logger = logger

    def _log(self, msg: str) -> None:
        if self._logger is not None:
            self._logger.info(msg)

    async def _forward_stderr(self) -> None:
        stderr = self._proc.stderr
        if stderr is None or self._logger is None:
            return
        while True:
            line_bytes = await stderr.readline()
            if not line_bytes:
                return
            self._log(f"process stderr: {line_bytes.decode().rstrip()}")

    def update_env(self, **kwargs: typing.Any) -> None:
        if self._exec_env is None:
            self._exec_env = {}
        for k, v in kwargs.items():
            self._exec_env[k] = str(v)

    async def start(self) -> "ProcessDriverImpl":
        if hasattr(self, "_proc"):
            return self
        self._log("Starting process...")
        self._proc = await asyncio.create_subprocess_exec(
            self._exec_path,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env=self._exec_env and dict(os.environ, **self._exec_env),
        )
        asyncio.create_task(self._forward_stderr())
        return self

    async def stop(self, timeout: float = 1) -> None:
        if hasattr(self, "_proc"):
            self.terminate()
            await self.wait(timeout=timeout)
            del self._proc

    async def __aenter__(self) -> "ProcessDriverImpl":
        return await self.start()

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        await self.stop()

    async def getline(self, timeout: float = 1) -> str:
        stdout = self._proc.stdout
        if stdout is None:
            raise RuntimeError("Cannot read from process stdout")
        self._log("Waiting for line...")
        try:
            line_bytes = await asyncio.wait_for(stdout.readline(), timeout)
            line = line_bytes.decode().rstrip()
        except asyncio.TimeoutError:
            raise errors.Timeout("Timeout when reading from process stdout")
        self._log(f"Got line: {line}")
        return line

    async def expect(self, msg: str, timeout: float = 1) -> None:
        line = await self.getline(timeout)
        if line.strip() != msg:
            raise errors.ExpectationFailed(f"Expected {msg}, got {line}")

    async def send(self, msg: str, timeout: float = 1) -> None:
        stdin = self._proc.stdin
        if stdin is None:
            raise RuntimeError("Cannot write process stdin")
        stdin.write(msg.encode())
        stdin.write(b"\n")
        try:
            await asyncio.wait_for(stdin.drain(), timeout)
        except asyncio.TimeoutError:
            raise errors.Timeout("Timeout when writing to process stdin")

    def kill(self) -> None:
        self._log("Killing process")
        try:
            self._proc.kill()
        except ProcessLookupError:
            pass

    def terminate(self) -> None:
        self._log("Terminating process")
        try:
            self._proc.terminate()
        except ProcessLookupError:
            pass

    async def wait(self, timeout: float = 1) -> None:
        self._log("Waiting for process to end...")
        try:
            await asyncio.wait_for(self._proc.wait(), timeout)
        except asyncio.TimeoutError:
            raise errors.Timeout("Timeout when waiting for process to end")

    async def expect_match(self, pattern: str, timeout: float = 1) -> typing.Match[str]:
        line = await self.getline(timeout)

        self._log(f"Line '{line}'")
        match = re.match(pattern, line)
        if match is None:
            raise errors.ExpectationFailed(
                f"Line '{line}' did not match pattern '{pattern}'"
            )
        return match


if typing.TYPE_CHECKING:
    # Ensure ProcessDriverImpl implements the ProcessDriver protocol
    _: type[ProcessDriver] = ProcessDriverImpl
