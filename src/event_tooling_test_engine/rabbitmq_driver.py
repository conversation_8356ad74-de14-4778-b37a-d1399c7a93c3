import base64
import logging
import os
from dataclasses import asdict
from types import TracebackType
from typing import Any
from urllib.parse import quote

import aiohttp

from event_tooling_test_engine import errors
from event_tooling_test_engine.rabbitmq_types import (
    DictView,
    ListView,
    Pagination,
)


def format_path(path: str, **kwargs: str) -> str:
    return path.format(
        **{name: quote(value, safe="") for name, value in kwargs.items()}
    )


def first_match(*paths: str | None) -> str | None:
    for path in paths:
        if path is not None:
            return path
    return None


def path_variant(
    default: str | None = None,
    with_vhost: str | None = None,
    with_user: str | None = None,
    with_name: str | None = None,
    vhost: str | None = None,
    user: str | None = None,
    name: str | None = None,
) -> str:
    if vhost is not None:
        if with_vhost is None:
            raise ValueError("Expected with_vhost")
        return format_path(with_vhost, vhost=vhost)
    if user is not None:
        if with_user is None:
            raise ValueError("Expected with_user")
        return format_path(with_user, user=user)
    if name is not None:
        if with_name is None:
            raise ValueError("Expected with_name")
        return format_path(with_name, name=name)
    if default is None:
        raise ValueError("Expected default")
    return default


def preprocess_query_params(params: dict[str, Any] | None) -> dict[str, str] | None:
    if params is None:
        return None
    processed = {}
    for k, v in params.items():
        match v:
            case None:
                pass
            case True:
                processed[k] = "true"
            case False:
                processed[k] = ""
            case _:
                processed[k] = str(v)
    return processed


def preprocess_params(params: dict[str, Any] | None) -> dict[str, Any] | None:
    if params is None:
        return None
    return {k: v for k, v in params.items() if v is not None}


async def raise_error_for_status(response: aiohttp.ClientResponse) -> None:
    match response.status // 100:
        case 4:
            if response.status == 404:
                raise errors.RabbitMQNotFound(
                    response.status, response.reason or "not found"
                )
            raise errors.RabbitMQClientError(
                response.status, response.reason or "client error"
            )
        case 5:
            raise errors.RabbitMQServerError()
        case _:
            return


class RabbitMQDriver:
    _session: aiohttp.ClientSession
    _logger: logging.Logger | None

    def __init__(
        self,
        host: str = os.getenv("RABBITMQ_HOST", "http://localhost:15672"),
        username: str = os.getenv("RABBITMQ_USERNAME", "guest"),
        password: str = os.getenv("RABBITMQ_PASSWORD", "guest"),
        logger: logging.Logger | None = None,
    ):
        auth = aiohttp.BasicAuth(login=username, password=password)
        self._session = aiohttp.ClientSession(
            host, auth=auth, raise_for_status=raise_error_for_status
        )
        self._logger = logger

    async def close(self) -> None:
        await self._session.close()

    async def __aenter__(self) -> "RabbitMQDriver":
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        await self.close()

    async def get(
        self,
        path: str,
        params: dict[str, Any] | None = None,
        pagination: Pagination | None = None,
    ) -> Any:
        if pagination is not None:
            params = dict(params or {}, **asdict(pagination))
        params = preprocess_query_params(params)
        # print(f"GET {path}", params)
        async with self._session.get(path, params=params) as resp:
            return await resp.json()

    async def put(
        self,
        path: str,
        params: dict[str, Any] | None = None,
    ) -> str:
        params = preprocess_params(params)
        # print(f"PUT {path}", params)
        async with self._session.put(path, json=params) as resp:
            return await resp.text()

    async def post(
        self,
        path: str,
        params: dict[str, Any] | None = None,
        raw_response: bool = False,
    ) -> Any:
        params = preprocess_params(params)
        # print(f"POST {path}", params)
        async with self._session.post(path, json=params) as resp:
            if raw_response:
                return await resp.text()
            else:
                return await resp.json()

    async def delete(
        self,
        path: str,
        params: dict[str, Any] | None = None,
    ) -> str:
        # print(f"DELETE {path}")
        params = preprocess_query_params(params)
        async with self._session.delete(path, params=params) as resp:
            return await resp.text()

    async def overview(self) -> DictView:
        return DictView(await self.get("/api/overview"))

    async def cluster_name(self) -> DictView:
        return DictView(await self.get("/api/cluster_name"))

    async def nodes(self) -> ListView:
        return ListView(await self.get("/api/nodes"))

    async def node(self, name: str) -> DictView:
        return DictView(await self.get(format_path("/api/nodes/{name}", name=name)))

    async def node_memory(self, name: str) -> DictView:
        return DictView(
            await self.get(format_path("/api/nodes/{name}/memory", name=name))
        )

    async def extensions(self) -> ListView:
        return ListView(await self.get("/api/extensions"))

    async def connections(
        self,
        vhost: str | None = None,
        user: str | None = None,
        pagination: Pagination | None = None,
    ) -> ListView:
        path = path_variant(
            default="/api/connections",
            with_vhost="/api/vhosts/{vhost}/connections",
            with_user="/api/connections/username/{user}",
            vhost=vhost,
            user=user,
        )
        return ListView(await self.get(path, pagination=pagination))

    async def connection(self, name: str) -> DictView:
        return DictView(
            await self.get(format_path("/api/connections/{name}", name=name))
        )

    async def close_connection(self, name: str) -> None:
        await self.delete(format_path("/api/connections/{name}", name=name))

    async def close_user_connections(self, user: str) -> None:
        await self.delete(
            format_path("/api/connections/username/{username}", username=user)
        )

    async def channels(
        self,
        vhost: str | None = None,
        connection: str | None = None,
        pagination: Pagination | None = None,
    ) -> ListView:
        path = path_variant(
            default="/api/channels",
            with_vhost="/api/vhosts/{vhost}/channels",
            with_name="/api/connections/{name}/channels",
            vhost=vhost,
            name=connection,
        )
        return ListView(await self.get(path, pagination=pagination))

    async def consumers(self, vhost: str | None = None) -> ListView:
        path = path_variant(
            default="/api/consumers",
            with_vhost="/api/consumers/{vhost}",
            vhost=vhost,
        )
        return ListView(await self.get(path))

    async def exchanges(
        self, vhost: str | None = None, pagination: Pagination | None = None
    ) -> ListView:
        path = path_variant(
            default="/api/exchanges", with_vhost="/api/exchanges/{vhost}", vhost=vhost
        )
        return ListView(await self.get(path, pagination=pagination))

    async def exchange(self, vhost: str, name: str) -> DictView:
        return DictView(
            await self.get(
                format_path("/api/exchanges/{vhost}/{name}", vhost=vhost, name=name)
            )
        )

    async def ensure_exchange(
        self,
        vhost: str,
        name: str,
        exchange_type: str,
        auto_delete: bool | None = None,
        durable: bool | None = None,
        internal: bool | None = None,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        await self.put(
            format_path("/api/exchanges/{vhost}/{name}", vhost=vhost, name=name),
            params={
                "type": exchange_type,
                "auto_delete": auto_delete,
                "durable": durable,
                "internal": internal,
                "arguments": arguments,
            },
        )

    async def delete_exchange(
        self, vhost: str, name: str, if_unused: bool | None = None
    ) -> None:
        await self.delete(
            format_path("/api/exchanges/{vhost}/{name}", vhost=vhost, name=name),
            params={"if-unused": if_unused},
        )

    async def publish_message(
        self,
        vhost: str,
        exchange: str,
        routing_key: str,
        payload: str | bytes,
        properties: dict[str, Any],
        headers: dict[str, Any] | None = None,
    ) -> DictView:
        if headers is not None:
            properties = {"headers": headers, **properties}
        params = {
            "properties": properties,
            "routing_key": routing_key,
        }
        if isinstance(payload, str):
            params["payload"] = payload
            params["payload_encoding"] = "string"
        else:
            params["payload"] = base64.b64encode(payload).decode()
            params["payload_encoding"] = "base64"

        return DictView(
            await self.post(
                format_path(
                    "/api/exchanges/{vhost}/{name}/publish", vhost=vhost, name=exchange
                ),
                params=params,
            )
        )

    async def queues(
        self,
        vhost: str | None = None,
        detailed: bool = False,
        enable_queue_totals: bool | None = None,
        disable_stats: bool | None = None,
        pagination: Pagination | None = None,
    ) -> ListView:
        if detailed:
            return ListView(
                await self.get("/api/queues/details", pagination=pagination)
            )

        path = path_variant(
            default="/api/queues",
            with_vhost="/api/queues/{vhost}",
            vhost=vhost,
        )
        params = {
            "enable_queue_totals": enable_queue_totals,
            "disable_stats": disable_stats,
        }
        return ListView(await self.get(path, pagination=pagination, params=params))

    async def queue(
        self,
        vhost: str,
        name: str,
        enable_queue_totals: bool = True,
        disable_stats: bool = True,
    ) -> DictView:
        params = {
            "enable_queue_totals": enable_queue_totals,
            "disable_stats": disable_stats,
        }
        return DictView(
            await self.get(
                format_path(
                    "/api/queues/{vhost}/{name}",
                    vhost=vhost,
                    name=name,
                ),
                params=params,
            )
        )

    async def ensure_queue(
        self,
        vhost: str,
        name: str,
        auto_delete: bool | None = None,
        durable: bool | None = None,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        await self.put(
            format_path("/api/queues/{vhost}/{name}", vhost=vhost, name=name),
            params={
                "auto_delete": auto_delete,
                "durable": durable,
                "arguments": arguments,
            },
        )

    async def delete_queue(
        self,
        vhost: str,
        name: str,
        if_empty: bool | None = None,
        if_unused: bool | None = None,
    ) -> None:
        await self.delete(
            format_path("/api/queues/{vhost}/{name}", vhost=vhost, name=name),
            params={
                "if-empty": if_empty,
                "if-unused": if_unused,
            },
        )

    async def purge_queue(self, vhost: str, name: str) -> None:
        await self.delete(
            format_path("/api/queues/{vhost}/{name}/contents", vhost=vhost, name=name)
        )

    async def perform_queue_action(self, vhost: str, name: str, action: str) -> None:
        # POST /api/queues/vhost/name/actions
        raise NotImplementedError

    async def get_messages(
        self,
        vhost: str,
        queue: str,
        count: int,
        ackmode: str = "ack_requeue_true",
        encoding: str = "auto",
        truncate: int | None = None,
    ) -> ListView:
        return ListView(
            await self.post(
                format_path("/api/queues/{vhost}/{name}/get", vhost=vhost, name=queue),
                params={
                    "count": count,
                    "ackmode": ackmode,
                    "encoding": encoding,
                    "truncate": truncate,
                },
            )
        )

    async def bindings(
        self,
        vhost: str | None = None,
        from_exchange: str | None = None,
        to_queue: str | None = None,
        to_exchange: str | None = None,
    ) -> ListView:
        if from_exchange is not None and to_queue is not None:
            assert vhost is not None
            assert to_exchange is None
            path = format_path(
                "/api/bindings/{vhost}/e/{exchange}/q/{queue}",
                vhost=vhost,
                exchange=from_exchange,
                queue=to_queue,
            )
        elif from_exchange is not None and to_exchange is not None:
            assert vhost is not None
            path = format_path(
                "/api/bindings/{vhost}/e/{source}/e/{destination}",
                vhost=vhost,
                source=from_exchange,
                destination=to_exchange,
            )
        elif from_exchange is not None:
            assert vhost is not None
            path = format_path(
                "/api/exchanges/{vhost}/{name}/bindings/source",
                vhost=vhost,
                name=from_exchange,
            )
        elif to_exchange is not None:
            assert vhost is not None
            path = format_path(
                "/api/exchanges/{vhost}/{name}/bindings/destination",
                vhost=vhost,
                name=to_exchange,
            )
        elif to_queue is not None:
            assert vhost is not None
            path = format_path(
                "/api/queues/{vhost}/{name}/bindings", vhost=vhost, name=to_queue
            )
        else:
            path = path_variant(
                default="/api/bindings", with_vhost="/api/bindings/{vhost}", vhost=vhost
            )
        return ListView(await self.get(path))

    async def ensure_binding(
        self,
        vhost: str,
        from_exchange: str,
        to_queue: str | None = None,
        to_exchange: str | None = None,
        routing_key: str | None = None,
        arguments: dict[str, Any] | None = None,
    ) -> None:
        if to_queue is not None:
            assert to_exchange is None
            path = format_path(
                "/api/bindings/{vhost}/e/{exchange}/q/{queue}",
                vhost=vhost,
                exchange=from_exchange,
                queue=to_queue,
            )
        elif to_exchange is not None:
            path = format_path(
                "/api/bindings/{vhost}/e/{source}/e/{destination}",
                vhost=vhost,
                source=from_exchange,
                destination=to_exchange,
            )
        else:
            raise ValueError("one of to_queue and to_exchange must be specified")
        await self.post(
            path,
            params={
                "routing_key": routing_key,
                "arguments": arguments,
            },
            raw_response=True,
        )

    async def binding(
        self,
        vhost: str,
        from_exchange: str,
        props: str,
        to_queue: str | None = None,
        to_exchange: str | None = None,
    ) -> DictView:
        if to_queue is not None:
            assert to_exchange is None
            path = format_path(
                "/api/bindings/{vhost}/e/{exchange}/q/{queue}/{props}",
                vhost=vhost,
                exchange=from_exchange,
                queue=to_queue,
                props=props,
            )
        elif to_exchange is not None:
            path = format_path(
                "/api/bindings/{vhost}/e/{source}/e/{destination}/{props}",
                vhost=vhost,
                source=from_exchange,
                destination=to_exchange,
                props=props,
            )
        else:
            raise ValueError("one of to_queue and to_exchange must be specified")
        return DictView(await self.get(path))

    async def vhosts(self) -> ListView:
        return ListView(await self.get("/api/vhosts"))

    async def vhost(self, name: str) -> DictView:
        return DictView(await self.get(format_path("/api/vhosts/{name}", name=name)))

    async def ensure_vhost(
        self,
        name: str,
        description: str | None = None,
        tags: list[str] | None = None,
        tracing: bool | None = None,
    ) -> None:
        await self.put(
            format_path("/api/vhosts/{name}", name=name),
            params={
                "description": description,
                "tags": None if tags is None else ",".join(tags),
                "tracing": tracing,
            },
        )

    async def delete_vhost(self, name: str) -> None:
        await self.delete(format_path("/api/vhosts/{name}", name=name))

    async def permissions(
        self, name: str, vhost: str | None, user: str | None
    ) -> ListView:
        path = path_variant(
            default="/api/users/permissions",
            with_vhost="/api/vhosts/{vhost}/permissions",
            with_user="/api/users/{user}/permissions",
            vhost=vhost,
            user=user,
        )
        return ListView(await self.get(path))

    async def topic_permissions(
        self, name: str, vhost: str | None, user: str | None
    ) -> ListView:
        path = path_variant(
            default="/api/users/permissions",
            with_vhost="/api/vhosts/{vhost}/topic-permissions",
            with_user="/api/users/{user}/topic-permissions",
            vhost=vhost,
            user=user,
        )
        return ListView(await self.get(path))

    async def start_vhost_on_node(self, vhost: str, node: str) -> None:
        # POST /api/vhosts/name/start/node
        raise NotImplementedError

    async def users(self, without_permissions: bool = False) -> ListView:
        if without_permissions:
            return ListView(await self.get("/api/users/without-permissions"))
        else:
            return ListView(await self.get("/api/users"))

    async def delete_users(self, users: list[str]) -> None:
        # POST /api/users/bulk-delete
        raise NotImplementedError

    async def user(self, name: str) -> DictView:
        return DictView(await self.get(format_path("/api/users/{name}", name=name)))

    async def ensure_user(self, name: str) -> None:
        # PUT /api/users/name
        raise NotImplementedError

    async def delete_user(self, name: str) -> None:
        # DELETE /api/users/name
        raise NotImplementedError

    async def user_limits(self, user: str | None = None) -> ListView:
        path = path_variant(
            default="/api/user-limits", with_user="/api/user-limits/{user}", user=user
        )
        return ListView(await self.get(path))

    async def ensure_user_limit(self, user: str, limit: str) -> None:
        # PUT /api/user-limits/user/name
        raise NotImplementedError

    async def delete_user_limit(self, user: str, limit: str) -> None:
        # DELETE /api/user-limits/user/name
        raise NotImplementedError

    async def whoami(self) -> DictView:
        return DictView(await self.get("/api/whoami"))

    async def permission(self, vhost: str, user: str) -> DictView:
        return DictView(
            await self.get(
                format_path("/api/permissions/{vhost}/{user}", vhost=vhost, user=user)
            )
        )

    async def ensure_permission(self, vhost: str, user: str) -> None:
        # PUT /api/permissions/vhost/user
        raise NotImplementedError

    async def delete_permission(self, vhost: str, user: str) -> None:
        # DELETE /api/permissions/vhost/user
        raise NotImplementedError

    async def ensure_topic_permission(self, vhost: str, user: str) -> None:
        # PUT /api/topic-permissions/vhost/user
        raise NotImplementedError

    async def delete_topic_permission(self, vhost: str, user: str) -> None:
        # DELETE /api/topic-permissions/vhost/user
        raise NotImplementedError

    async def parameters(
        self, component: str | None = None, vhost: str | None = None
    ) -> ListView:
        if component is not None and vhost is not None:
            path = format_path(
                "/api/parameters/{component}/{vhost}", component=component, vhost=vhost
            )
        elif vhost is not None:
            path = format_path("/api/parameters/{vhost}", vhost=vhost)
        else:
            path = "/api/components"
        return ListView(await self.get(path))

    async def parameter(self, component: str, vhost: str, name: str) -> DictView:
        return DictView(
            await self.get(
                format_path(
                    "/api/parameters/{component}/{vhost}/{name}",
                    component=component,
                    vhost=vhost,
                    name=name,
                )
            )
        )

    async def ensure_parameter(
        self, component: str, vhost: str, name: str, value: str
    ) -> None:
        # PUT /api/parameters/component/vhost/name
        raise NotImplementedError

    async def delete_parameter(self, component: str, vhost: str, name: str) -> None:
        # DELETE /api/parameters/component/vhost/name
        raise NotImplementedError

    async def global_parameters(self) -> ListView:
        return ListView(await self.get("/api/global-parameters"))

    async def global_parameter(self, name: str) -> DictView:
        return DictView(
            await self.get(format_path("/api/global-parameters/{name}", name=name))
        )

    async def ensure_global_parameter(self, name: str) -> None:
        # PUT /api/global-parameters/name
        raise NotImplementedError

    async def delete_global_parameter(self, name: str) -> None:
        # DELETE /api/global-parameters/name
        raise NotImplementedError

    async def policies(self, vhost: str | None = None) -> ListView:
        path = path_variant(
            default="/api/policies",
            with_vhost="/api/policies/{vhost}",
            vhost=vhost,
        )
        return ListView(await self.get(path))

    async def policy(self, vhost: str, name: str) -> DictView:
        return DictView(
            await self.get(
                format_path("/api/policies/{vhost}/{name}", vhost=vhost, name=name)
            )
        )

    async def ensure_policy(self, vhost: str, name: str) -> None:
        # PUT /api/policies/vhost/name
        raise NotImplementedError

    async def delete_policy(self, vhost: str, name: str) -> None:
        # DELETE /api/policies/vhost/name
        raise NotImplementedError

    async def operator_policies(self, vhost: str | None = None) -> ListView:
        path = path_variant(
            default="/api/operator-policies",
            with_vhost="/api/operator-policies/{vhost}",
            vhost=vhost,
        )
        return ListView(await self.get(path))

    async def operator_policy(self, vhost: str, name: str) -> DictView:
        return DictView(
            await self.get(
                format_path(
                    "/api/operator_policies/{vhost}/{name}", vhost=vhost, name=name
                )
            )
        )

    async def ensure_operator_policy(self, vhost: str, name: str) -> None:
        # PUT /api/operator-policies/vhost/name
        raise NotImplementedError

    async def delete_operator_policy(self, vhost: str, name: str) -> None:
        # DELETE /api/operator-policies/vhost/name
        raise NotImplementedError

    async def aliveness_test(self, vhost: str) -> DictView:
        return DictView(
            await self.get(format_path("/api/aliveness-test/{vhost}", vhost=vhost))
        )
