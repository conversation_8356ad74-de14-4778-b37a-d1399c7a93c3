apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: events_tooling_py
  description: Python tools to publish and consume RabbitMQ messages
  annotations:
    github.com/project-slug: Multiverse-io/events_tooling_py
    circleci.com/project-slug: github/Multiverse-io/events_tooling_py
    backstage.io/techdocs-ref: dir:.
  tags:
    - python
    - rabbitmq
    - events
    - messaging
    - amqp
  links:
    - url: https://github.com/Multiverse-io/events_tooling_py
spec:
  type: library
  owner: optimistic-orcas
  lifecycle: production