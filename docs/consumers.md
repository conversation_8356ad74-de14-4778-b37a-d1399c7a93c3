# Consumers

## EventConsumer
`events_tooling_py` exposes `events_tooling.event_client.EventConsumer` that abstracts away consumer configuration and basic functionality.

### Configuration
`EventConsumer` expects a config dictionary to be passed into the constructor. Internally the config dictionary is converted to `Pydantic` models. The dictionary should follow the `ConsumerConfig` schema:


Since 0.3.0, it is mosly unnecessary to configure consumer queues - it is enough
to create dispatchers (see [event handlers](#event-handlers)) and register them
with the `EventConsumer` as follows.  The configuration will happen
automatically.

```python

service_config = ServiceConfig(
    service_name = '/my_service',
    exception_handler = my_exception_handler,
    # other optional configuration
)

consumer = EventConsumer(
    service_config = service_config,
    consumer_providers = [dispatcher1, dispatcher2]
)
```

```python
class BindingConfig(BaseModel):
    """Config for a binding between a queue and an exchange."""

    exchange: str
    routing_key: str


class ArgumentsConfig(BaseModel):
    """Config for additional arguments for queue declaration."""

    x_queue_type: str = Field(..., alias='x-queue-type')
    x_single_active_consumer: bool = Field(..., alias='x-single-active-consumer')
    x_dead_letter_exchange: str = Field(default=None, alias='x-dead-letter-exchange')
    x_dead_letter_routing_key: str = Field(
        default=None, alias='x-dead-letter-routing-key'
    )


class QueueConfig(BaseModel):
    """Config for a queue."""

    name: str
    durable: bool = False
    arguments: ArgumentsConfig | None = None
    bindings: list[BindingConfig] = []
    handler: str | EventHandler | None = None
    auto_ack: bool = False


class ConsumerQueueConfig(BaseModel):
    """Config for a consumer queue and dead letter queue."""

    dlq: QueueConfig | None = None
    queue: QueueConfig


class ConsumerConfig(BaseModel):
    """Base config for event consumers."""

    exception_handler: Callable | None = None
    consumers: list[ConsumerQueueConfig]
```


### Event handlers

In the configuration above, the `QueueConfig` contains a `handler` which is of
type `str` or `EventHandler`.  If it is a string, it is treated as an import
path for a handler of type `EventHandler`.

and `EventHandler` is simply a function that takes a `CloudEventSchema` (from
the `event_schemas` package) as its single argument.  It may handle events of
different types, which it can inspect via the `type` property of the event
schema. A common example of this is when different versions of the same event
need to be handled.

There is a `EventDispatcher` class which implements the `EventHandler` type and
helps handle different types of event with the same handler.  E.g. in the
example above, `dispatcher` can be used has the handler for a queue config.

Since v0.3.0, you can do this

```python
from event_schemas.example.created.v1 import Created as CreatedV1
from event_schemas.exmaple.created.v2 import Created as CreatedV2

dispatcher = EventHandler()

@dispatcher.register_handler
def handle_example_created_v1(event: CreatedV1):
    ...

@dispatcher.register_handler
def handle_example_created_v2(event: CreatedV2):
    ...
```

It is possible to fall back to this if necessary:

```python
dispatcher = EventHandler()

@dispatcher.handle('multiverse.example.created.v1')
def handle_example_created_v1(event: CloudEventSchema):
    ...

@dispatcher.handle('multiverse.example.created.v2)
def handle_example_created_v2(event: CloudEventSchema):
    ...
```


### Legacy events

Messages which are not in the `event-schemas` repository are not wrapped in a cloud event on the wire.  When the consumer receives them, it creates a synthetic `CloudEventSchema` instance where

- `type` is the routing key on which the event was sent
- `dataschema` is the string `https://schema.multiverse.io/legacy`
- `event` is the whole message payload

For example if the event above evolved from a legacy message which was sent with routing key `example_created`, it can be dispatched to the correct handler like this:

```python

@dispatcher.handle('example_created')
def handle_legacy_example_created(event: CloudEventSchema)
    ...
```

### Usage
When instantiating `EventConsumer`, as well as the config dictionary you also need to pass in a RabbitMQ connection client such as `events_tooling.connection_client.PikaClient` (or a custom one).

```python
from events_tooling.connection_client import PikaClient
from events_tooling.event_client import EventConsumer

rabbitmq_client = PikaClient(config=settings.RABBITMQ_CONNECTION_CONFIG)
consumer = EventConsumer(
    config=settings.EVENT_CONSUMERS,
    rabbitmq_client=rabbitmq_client,
)
consumer.setup()
```

After instantiated you can call `consumer.setup()`. This handles creating the connection to the RabbitMQ server, sets up the queues and their bindings based on the config and then starts the consumer loop.

During local development you may find it useful to call the setup method with `declare_exchanges=True` - `consumer.setup(declare_exchanges=True)`. This creates any missing exchanges based of the config file. NOTE: it is **not** recommended to use this flag in production environments.


### Event Schemas
If the event is encapsulated inside a [CloudEvent schema](https://reef.tech-tools.multiverse.io/docs/default/component/event-schemas/spec/#payload-envelope) the `EventConsumer` will automatically extract the message payload before passing it to your message handler.