# Typescript support library

> [!WARNING]
> WiP: Not all functionality is available yet!

Uses the [json-schema-to-zod package](https://www.npmjs.com/package/json-schema-to-zod) to generate the TypeScript schemas.

## Local testing

To run the code gen locally, run `./scripts/codegen.sh`. Navigate to `./support/output/typescript/lib/events` to view the results.

### Method 1: Using the ts_lib_to_local_project.sh script (Recommended)

A simple way to test your schema changes in a local project is to use the provided script:

```bash
./scripts/ts_lib_to_local_project.sh <target_directory>
```

For example:
```bash
./scripts/ts_lib_to_local_project.sh ../guidance_hub
```

This script will:
1. Generate the TypeScript library from your schema changes
2. Build the package
3. Copy the built files directly to the target project's node_modules directory

### Method 2: Manual package reference

Alternatively, to generate a new package to use in a different service:

1. run `./scripts/codegen.sh typescript` (As per above)
2. navigate to `./support/output/typescript` and copy (preferably the absolute) path
3. in service where you want to use this custom package, in package.json update `@multiverse-io/event-schemas-ts` like so: `"@multiverse-io/event-schemas-ts": "file:<PATH_YOU_COPIED>",`
   (e.g.: `  "@multiverse-io/event-schemas-ts": "file:/Users/<USER>/projects/multiverse/event-schemas/support/output/typescript/",`)

## TODO

- [x] generate zod schema for each event.
- [x] derive type for each event.
- [x] schema for `event envelope`.
- [x] helper functions for wrapping and encoding events in the envelope.
- [x] decoding event envelop and events.
  - consumers should use the zod schemas for envelope and events.
  - we can consider further helper functions when it is clearer what will work for different use cases.
- [ ] consider what needs to be done to make consuming the schema easy for TypeScript clients.

### Generated zod schema example:

`.../events/account/signed_up/signedUpSchemaV1.ts`

```typescript
import { z } from "zod";

export default z.object({
  id: z.string().uuid().describe("The account's id"),
  account: z.object({
    id: z.string().uuid().describe("The account's id"),
    first_name: z.string().describe("The account's first name"),
    last_name: z.string().describe("The account's last name"),
    email: z.string().email().describe("The account's email address"),
    roles: z.array(z.string()).describe("The account's roles"),
  }),
});
```

### Generated index file example:

This re-exports the schema and derives a type from zod which is exported too.

`.../events/account/signed_up/index.ts`

```typescript
import { z } from "zod";
import schema from "./signedUpSchemaV1";

export const signedUpSchemaV1 = schema;
export type SignedUpV1 = z.infer<typeof schema>;
```

---

> [!WARNING]
> This document was written by Hoyon who isn't really a typescript dev so feel free to amend this doc if I'm talking nonsense. I have also not tested the code presented.

The TypeScript support library will be split into two libraries, `events-core` and `event-schemas`.

`events-core` is available at `support/typescript/events-core` in the `main` branch. It implements common utilities between all events and the Cloud Events encoder and decoder.

`event-schemas` is generated and is in the `typescript` branch.

## Design

The code generation script will output a `zod` schema for every schema version.

An example with the account signedup event:

```typescript
import { z } from "zod";
import { makeCloudEvent, getSource } from "events-core";

export const AccountSignedUpV1Schema = z.object({
  id: z.string().uuid().describe("The account's id"),
  account: z.object({
    id: z.string().uuid().describe("The account's id"),
    first_name: z.string().describe("The account's first name"),
    last_name: z.string().describe("The account's last name"),
    email: z.string().email().describe("The account's email"),
    roles: z.array(z.string()).describe("The account's roles"),
  }),
});

export type AccountSignedUpV1 = z.infer<typeof AccountSignedUpV1>;

export function encode(data: AccountSignedUpV1): string {
  let parsed = AccountSignedUpV1Schema.parse(data);

  let source = getSource();

  let wrapped = makeCloudEvent(
    source,
    "multiverse.account.signed_up.v1",
    "https://schema.multiverse.io/account/signed_up/1.0.0",
    parsed
  );

  return JSON.stringify(wrapped);
}

export function decode(event: CloudEvent): AccountSignedUpV1 {
  // check the envelope type is correct
  // decode and validate the payload
  // return object with type conforming to zod schema
}
```

This will be in a file named like `lib/account/signed_up/v1.ts`.

It can then be used like so:

```typescript
import {
  encode,
  AccountSignedUpV1,
} from "event-schemas/lib/account/signed_up/v1";

function send_event(account) {
  let event: AccountSignedUpV1 = {
    id: account.id,
    account: {
      id: account.id,
      first_name: account.first_name,
      last_name: account.last_name,
      email: account.email,
      roles: account.roles,
    },
  };

  let encoded: string = encode(event);

  send_event_to_amqp("some.exchange", "some.routing_key", encoded);
}
```

The main points are that:

- Each event version should have its own `encode` function which not only verifies the payload but ensures the cloud event is filled with the correct values.
- Runtime validation is always required in addition to existing typescript guarantees
- The encoder expects data passed into it is valid and errors if that's not the case
- The encoder should be strict about not accepting unknown extra fields
- The `source` field should be defined somewhere globally as it should never be changed after application initialisation
- The decoder should check the envelope has the correct type
- The decoder should take an already parsed cloud event object to give the caller the opportunity to select the correct decoder in the case that multiple event types/versions are expected in a single event channel.
