## Requirement tests

The requirement tests are implemented in
[event_tooling_py/requirement_tests/requirement_test_objects](../events_tooling_py/requirement_tests/requirement_test_objects/).

For requirement `C-01` for example, a test that is supposed to fail should be
called `req_c01_fail.py` and a test that is supposed to succeed should be called
`req_c01_ok.py`.  You can run the command

```shell
poetry run python -m events_tooling_py.requirement_tests
```

It will run all the tests and produce a report.  It is also possible to run
tests for just on requirement as follows.

```shell
poetry run python -m events_tooling_py.requirement_tests --req C-01
```
