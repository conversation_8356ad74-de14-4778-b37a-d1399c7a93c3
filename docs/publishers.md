# Publishers

## EventPublisher
`events_tooling_py` exposes `events_tooling.event_client.EventPublisher` that abstracts away publisher configuration and basic functionality.

### Configuration
`EventPublisher` expects a config dictionary to be passed into the constructor. Internally the config dictionary is converted to `Pydantic` models. The dictionary should follow the `PublisherConfig` schema:


```python
class PublisherExchangeConfig(BaseModel):
    """Config for a publisher exchange."""

    exchange: str
    exchange_type: 'direct' | 'fanout' | 'headers' | 'topic'
    durable: bool


class PublisherConfig(BaseModel):
    """Base config for event publishers."""

    exception_handler: Callable | None = None
    exchanges: list[PublisherExchangeConfig] | None = []
```

### Usage
When instantiating `EventPublisher`, as well as the config dictionary you also need to pass in a RabbitMQ connection client such as `events_tooling.connection_client.PikaClient` (or a custom one).


Since v0.3.0, `EventPublisher` can be configured just by passing it event schema
types in most cases, as follows.

```python
from event_schemas.exmaple.created.v2 import Created as CreatedV2
from event_schemas.exmaple.updated.v1 import Updated as UpdatedV1

service_config = ServiceConfig(
    service_name = '/my_service',
    exception_handler = my_exception_handler,
    # other optional configuration
)

publisher = EventPublisher(
    service_config = service_config,
    event_schemas = [CreatedV2, UpdatedV1]
)
```

The previous method still works:

```python
from events_tooling.connection_client import PikaClient
from events_tooling.event_client import EventPublisher

rabbitmq_client = PikaClient(config=settings.RABBITMQ_CONNECTION_CONFIG)
publisher = EventPublisher(
    config=settings.EVENT_PUBLISHERS,
    rabbitmq_client=rabbitmq_client,
)
publisher.setup()
```

After instantiated you can call `publisher.setup()`. This handles creating the connection to the RabbitMQ server and declares any exchanges specified in the config.

Since v0.3.0, the `publish` methods allows you to publish an event without wrapping it in a cloud event envelope or specifying the exchange or routing key.  E.g.

```python
rom event_schemas.user.ksbs_attained.v1 import (
    encode_cloudevent as encode_ksbs_attained_cloudevent,
    KsbsAttainedSchema,
)

publisher.publish(KsbsAttainedSchema(**{
        'id': user_id,
        'user': {
            'id': user_id,
            'ksbs': [
                {
                    'id': k.ksb.id,
                    'score': k.score,
                }
                for k in ksbs_with_scores
            ],
        },
    })
)
```

The `publish_to_exchange` method allows you to publish an event of type `CloudEventSchema`. E.g.

```python
from event_schemas.user.ksbs_attained.v1 import (
    encode_cloudevent as encode_ksbs_attained_cloudevent,
    KsbsAttainedSchema,
)

event = encode_ksbs_attained_cloudevent(
    event=KsbsAttainedSchema(**{
        'id': user_id,
        'user': {
            'id': user_id,
            'ksbs': [
                {
                    'id': k.ksb.id,
                    'score': k.score,
                }
                for k in ksbs_with_scores
            ],
        },
    }),
    source='/competency-service',
)
publisher.publish_to_exchange(
    exchange='users.topic_exchange',
    routing_key='user.ksbs_attained',
    event=event,
)
```