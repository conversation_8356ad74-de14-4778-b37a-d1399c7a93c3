# Event schema spec

> [!NOTE]
> This was adapted from this [RFC](https://coda.io/d/_djs0e0EYIOo/Event-Schema-Registry_su4BG)

Every event in our system should conform to a defined event schema. This document
describes how event schemas are to be specified and used. See accompanying documentation
for details about tooling to aid in implementing this.

## Note on terminology

The key words "MUST", "MUST NOT", "REQUIRED", "SHALL", "SHALL NOT", "SHOULD", "SHOULD NOT", "RECOMMENDED", "MAY", and "OPTIONAL" in this document are to be interpreted as described in [RFC 2119](https://datatracker.ietf.org/doc/html/rfc2119).

## Schema format

Event payloads will be in JSON format and be described using JSON Schema Draft 2020-12.

The JSON schemas themselves will be defined in a YAML format to ease schema authoring. These will be automatically converted into their JSON form for tooling which only accepts that format.

**Example schema definition**

```yaml
$id: https://schemas.multiverse.io/account/signed_up/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - account
properties:
  account:
    type: object
    properties:
      id:
        type: string
        description: The account's id
        format: uuid
      first_name:
        type: string
        description: The account's first name
      last_name:
        type: string
        description: The account's last name
    required:
      - id
      - first_name
      - last_name
```

**Example payload conforming to the above schema**

```json
{
  "account": {
    "id": "d36b23d1-4804-4abb-8a63-395238b44a63",
    "first_name": "Alice",
    "last_name": "Jones"
  }
}
```

- The `$id` field MUST be set to an absolute URI which begins with https://schemas.multiverse.io/ and the path must contain a namespace, name and version. The version should be specified as three numbers separated by full stops.
  - Format: https://schemas.multiverse.io/{namespace}/{event_name}/{version}
  - Regex: `^https:\/\/schemas\.multiverse\.io\/[a-z_]+\/[a-z_]+\/\d+\.\d+\.\d+$`
  - Note the spec specifies that the schema does not necessarily need to be available at the specified URI
- The `$schema` field MUST be set to https://json-schema.org/draft/2020-12/schema
- The top level `type` field SHOULD be set to `object`
- The `required` field SHOULD be used whenever fields are expected to be set, unless this will break backwards compatibility
- The `additionalProperties` field SHOULD NOT be used as this prevents adding new fields in a backwards compatible way

## Payload envelope

Event payloads will be contained in a envelope following the CloudEvents 1.0.2 spec and more specifically the JSON format.

Example of the above payload wrapped in a cloud event envelope

```json
{
  "specversion": "1.0",
  "type": "multiverse.account.signed_up.v1",
  "source": "/account",
  "id": "4f7089ff-0458-45b8-a055-1947dcd70be4",
  "datacontenttype": "application/json",
  "dataschema": "https://multiverse.io/account/signed_up/1.0.0",
  "data": {
    "account": {
      "id": "d36b23d1-4804-4abb-8a63-395238b44a63",
      "first_name": "Alice",
      "last_name": "Jones"
    }
  },
  "subject": "account:d36b23d1-4804-4abb-8a63-395238b44a63",
  "time": "2024-01-01T12:00:00.000000Z"
}
```

- `specversion` MUST be set to `1.0`
- `type` MUST follow the format `multiverse.{namespace}.{event_name}.v{major_version}`
- `source` MUST specify the service name as a relative URI
  - Example `/account`
  - `source` MAY additionally contain further metadata after a single `/` to identify the instance eg `/account/********` to specify the ip of the producing instance.
- `id` MUST be unique for every event from a single source and MAY be a UUID
  - The combination of `source` and `id` MUST be globally unique across the entire system
- `datacontenttype` SHOULD be set to `application/json`
- `dataschema` MUST be set to the `$id` field of the event schema which corresponds to the payload
- `data` MUST contain a JSON payload directly as a value of the `data` field and MUST NOT be a JSON object encoded into a string or other representation.
- `subject` MAY be set to an identifier related to the event
- `time` MAY be set to the event timestamp and MUST be specified in `YYYY-MM-DDTHH:MM:SS.mmmmmmZ` format (valid [RFC 3339](https://datatracker.ietf.org/doc/html/rfc3339) and [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601))

## Schema versioning and evolution

All schemas MUST be versioned using semantic versioning.

This is to ensure that any backwards incompatible changes are given extra consideration and make the need for adjusting producers and consumers more visible.

Any backwards incompatible changes require a major version bump.

This means that events produced using version `1.5` of a schema can be successfully decoded without error by a consumer which only knows about version `1.2` by ignoring any additional fields.

**Examples of backwards compatible changes:**

- Adding a new non-required property of any type
- Adding a new non-required object property with required sub-properties
  - eg adding a non-required address property to the account object with a required postcode sub-property

**Examples of backwards incompatible changes:**

- Adding a new required property of any type
- Removing a property
- Renaming a property
- Changing a pre-existing non-required property into a required property
- Adding a new case to an enum, or changing the value of an existing case
- Removing a case from an enum
  - From a parsing perspective this is non-breaking, but consumers may rely on the value for state transitions

## Schema evolution

Evolving schemas through major version bumps will require coordination between producers and consumers to ensure continued operation through the transition period.

Every such version bump will require in depth analysis and planning from technical leads, however they will all roughly follow this run plan:

1. Propose the change in a draft PR to the event schema registry repo
2. Ensure backwards incompatible schema changes are acceptable for all current consumers. (eg any removed fields are no longer necessary)
3. Add new schema fields into existing version as minor bump, even if some information is duplicated or in a sub-optimal format
4. Review and publish new major schema version
5. Ensure all consumers are using up to date support libraries and can parse and consume both old and new events by switching on the type envelope attribute
6. Update producer to start sending the new event format
7. Remove code to handle old schema version in consumers

## Schema deprecation

Obsolete schemas should be deprecated and eventually removed from default support libraries.

This means their definitions will still exist indefinitely in the schema registry but may be moved into an archived section and no longer included in the default support libraries (perhaps we will maintain a separate set of support libraries for decoding archived events to facilitate analysis of historic data).

## Responsibilities

It is the responsibility of a consumer to ensure that 'backwards-compatible' changes (eg adding a new optional field into an existing schema) will not break the consumer.
