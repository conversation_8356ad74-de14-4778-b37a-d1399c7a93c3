# How this repo works

## Branches

This repo has several long lived branches. The default `main` branch is where the schemas
and support tools live and this should be the target of any work and PRs.

There are also per language branches, such as the `elixir` branch which contains the
generated code for Elixir. These branches are created using `git checkout --orphan` so
have completely unrelated histories.

## Code generation flow

1. A PR with schema changes is opened
2. CI runs the validation and code gen scripts on the branch
3. The PR is approved and merged
4. The validation and code gen scripts are run again on `main`
5. The generated code is committed to the long lived branch for that language

For TypeScript, there's an additional step `6` which is a new version of the
package is published. Consumers can grab this package from [here](https://github.com/Multiverse-io/event-schemas/pkgs/npm/event-schemas-ts).

Users of the Elixir library can link directly to the elixir branch when pulling
in the event-schemas dependency.

## Structure of the generated code

The support code is split into two parts for each language. First is the events core
library which is a regular library that contains all logic and functionality common to
every event. And then there is the events library which contains all of the generated
code.

The latter should be pulled into other code bases and will itself depend on the core library.
