# Events Tooling

`events_tooling_py` is an Python package that allows you to easily create publishers and consumers for RabbitMQ.

## How to install
To add it to your application, fetch it directly from our Github private repository by adding it to your `pyproject.toml` file:

```toml
[tool.poetry.dependencies]
python = ">=3.12,<3.13"
events-tooling-py = { git = "**************:Multiverse-io/events_tooling_py.git", rev = "v0.2.1" }
```
and then installing the pacakge

```bash
poetry install
```
