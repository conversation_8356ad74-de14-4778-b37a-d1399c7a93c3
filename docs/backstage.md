# Backstage generator

The backstage backend generates catalog-info.yaml files, one per event, ready to be imported into Reef.

These are outputted into the `backstage` branch in the `event-schemas` repo.

The structure of the file is as follows:

```yaml
apiVersion: multiverse.io/v1alpha1
kind: Event
metadata:
  name: multiverse.account.changelog.v1
  annotations:
    backstage.io/source-location: url:https://github.com/Multiverse-io/event-schemas/blob/main/schemas/account/changelog/v1.yaml
  description: |
    An event
spec:
  type: jsonschema
  lifecycle: latest
  definition: |
    $id: https://schema.multiverse.io/account/changelog/1.0.0
    $schema: https://json-schema.org/draft/2020-12/schema
    # ...
```

**Fields:**
- `kind` - this is always `Event`, a custom kind implemented in our Reef instance
- `metadata.name` - this maps to the `type` field in the schemas
- `spec.type` - this is always `jsonschema`
- `spec.lifecycle` - one of `latest`, `deprecated` or `experimental`. This is automatically derived from the available schemas.
- `spec.definition` - the full definition of the schema in yaml format
