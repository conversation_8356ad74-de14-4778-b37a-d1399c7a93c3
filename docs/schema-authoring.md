# Schema authoring

## Create a new unstable (v0) schema

It is sometimes useful to create a new schema even though that schema is not stable yet.
A v0 (or unstable) schema is useful to have for various reasons:

- It allows breaking changes without a major version bump
- It allows for early feedback from consumers (who are free to opt-in to the v0 schema)
- It is a clear indicator to users that the schema is not production ready

Once a schema is stable, the version should be bumped to v1.

1. Decide on an event name and which namespace to put it in
2. Create a new branch
3. Create a new directory in `/schemas/< your namespace >/< your event name>`
4. Create a new file in that directory called `v0.yaml`
5. Add your event definition in yaml format with version in the `$id` as `0.1.0`
6. Create a PR and get it reviewed
7. Merge the PR

## Create a new schema

1. Decide on an event name and which namespace to put it in
2. Create a new branch
3. Create a new directory in `/schemas/< your namespace >/< your event name>`
4. Create a new file in that directory called `v1.yaml`
5. Add your event definition in yaml format with version in the `$id` as `1.0.0`
6. <PERSON>reate a PR and get it reviewed
7. Merge the PR

## Update a schema without breaking changes

1. Create a new branch
2. Find the latest schema definition for your event
3. Update the schema with your changes
4. Make sure to also bump the minor version in the `$id` field
5. Create a PR and get it reviewed
6. Merge the PR

## Update a schema with breaking changes

**You should discuss any backwards incompatible changes with all consumers before making any breaking changes**

1. Create a new branch
2. Find the latest schema definition for your event
3. Create a new file in your event directory with the new version number
4. Add your event definition in yaml format making sure the version in `$id` reflects the new number
5. Create a PR and get it reviewed
6. Merge the PR
