# Elixir support library

The Elixir support library is split into two separate libraries, one is the main `EventSchemas`
library and the other is the shared `EventsCore` library.

`EventsCore` is available at `support/elixir/events_core` in the `main` branch. It implements common utilities between all events and the Cloud Events encoder and decoder.

`Events` is generated and is in the `elixir` branch.

## Intended usage

### Event producers

```elixir
defmodule MyProducer do
  alias EventSchemas.Account.SignedUp.V1, as: SignedUpEvent

  def publish_event(data) do
    data
    |> build_map_with_correct_format()
    |> SignedUpEvent.encode!()
    |> push_to_amqp_exchange()
  end
end
```

The `EventSchemas.Account.SignedUp.V1` module contains an `encode!` function which expects data
to already be in the correct shape. It validates this assumption before wrapping it in a
Cloud Event envelope and then encoding it into JSON.

The result of the `encode!` function is ready to be sent to an event system like RabbitMQ.

### Event consumers

```elixir
defmodule MyConsumer do
  alias EventSchemas.Account.SignedUp.V1, as: SignedUpEvent
  alias EventsCore.CloudEvents

  def handle_event(json_payload) do
    envelope = CloudEvents.decode!(json_payload)

    # check type is as expected
    unless envelope.type == SignedUpEvent.type() do
      raise "invalid type"
    end

    data = SignedUpEvent.decode!(envelope)

    do_something_with_data(data)
  end
end
```

The decoding flow is split into two steps. One to decode the envelope, and the other to
decode the event payload itself.

The purpose of this separation is to give consumers the opportunity to either check the
event type is as expected, or to be able to handle multiple event types. This could be
completely different event types, or different versions of the same event.

Once the type is determined, the correct event module can be selected for decoding the
payload.
