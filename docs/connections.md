# Connections

## PikaClient
`events_tooling_py` comes with a default connection client - `events_tooling.connection_client.PikaClient`. This client is used to establish and manage connections with a RabbitMQ server.

### Configuration
When instantiating `PikaClient`, you need to pass in a config dictionary with the following keys:

- `url`: The RabbitMQ host URL, including credentials.
- `reconnect_attempts` (optional): The maximum number of reconnect attempts. Defaults to `5` if not provided.

Here's an example of how to instantiate `PikaClient`:

```python
rabbitmq_client = PikaClient(
    config={
        'url': 'amqp://guest:guest@localhost:5672/%2F',
        'reconnect_attempts': 5,
    }
)
```

## Defining your own connection client
If you'd like to define your own custom connection client, you can do so by implementing the `events_tooling.connection_client.RabbitMQClient` interface. This interface includes two methods:

- `connect`: Establishes a connection to RabbitMQ.
- `close`: Closes the connection to RabbitMQ.

```python
class RabbitMQClient(ABC):
    """Abstract base class for a RabbitMQ client."""

    connection: Any = None
    channel: Any = None

    @abstractmethod
    def connect(self) -> None:
        """Connect to RabbitMQ."""
        raise NotImplementedError

    @abstractmethod
    def close(self) -> None:
        """Close the connection to RabbitMQ."""
        raise NotImplementedError
```