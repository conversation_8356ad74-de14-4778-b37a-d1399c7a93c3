# Changelog

## Current

## [v0.7.2](https://github.com/Multiverse-io/events_tooling_py/compare/v0.7.1...v0.7.2) (2025-05-12)

* QueueConfig - allow arbitrary types

## [v0.7.1](https://github.com/Multiverse-io/events_tooling_py/compare/v0.7.0...v0.7.1) (2025-05-08)

* Add `learning_content_draft` entity plural

## [v0.5.4](https://github.com/Multiverse-io/events_tooling_py/compare/v0.5.3...v0.5.4) (2025-04-17)

* Update `events_tooling_py.entity_plurals.py`

## [v0.5.3](https://github.com/Multiverse-io/events_tooling_py/compare/v0.5.2...v0.5.3) (2025-03-31)

* Update `events_tooling_py.entity_plurals.py`

## [v0.5.2](https://github.com/Multiverse-io/events_tooling_py/compare/v0.5.1...v0.5.2) (2025-03-19)

* Raise `MessagingError` when there is a problem with the messaging system
* Standardise event errors (`NonRecoverableEventError` and children)
* Do not swallow `KeyboardInterrupt` when consuming messages

## [v0.5.1](https://github.com/Multiverse-io/events_tooling_py/compare/v0.5.0...v0.5.1) (2025-03-18)

* Move logging of published messages from EventPublisherOnly to RabbitMQBasicPublisher

## [v0.5.0](https://github.com/Multiverse-io/events_tooling_py/compare/v0.4.0...v0.5.0) (2025-03-18)

- the `EventPublisherOnly` class is only concerned with turning an event into a message suitable for rabbitmq - it doesn't manage the rabbitmq connection
- the `RabbitMQPublisherConfigurator` is only for setting up the RabbitMQ exchanges needed for publishing
- the `RabbitMQBasicPublisher` only knows how to send a 'raw' RabbitMQ message, and implements the retry logic.  It can be swapped out by another implementation of the `BasicPublisher` protocol
- there is also a `ChannelManager` that provides a stable channel that can be recreated in case of disconnection.

## [v0.4.0](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.11...v0.4.0) (2025-03-13)

* Re-raise unknown exceptions in `wrap_event_handler`

## [v0.3.11](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.10...v0.3.11) (2025-03-11)

* Update `events_tooling_py.entity_plurals.py`

## [v0.3.10](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.8...v0.3.10) (2025-01-06)

* Expose get_publishing_data_for_event, needed for backfills
* Make publisher reconnect when connection lost while sending message (#53)
* Implememt requirement P-04 (#54)
* SC-75023 Implement requirement C-10 (#52)

## [v0.3.9](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.8...v0.3.9) (2025-01-02)

* SC-75027 - Implement requirement C-13 (automatic reconnect) by @arnodel in #51

## [v0.3.8](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.7...v0.3.8) (2024-01-02)

* SC-75018 - Implement requirement C-06 in events_tooling_py by @arnodel in #50

## [v0.3.7](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.6...v0.3.7) (2024-12-20)

* Allow overriding queue and dql names in dispatcher

## [v0.3.6](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.5...v0.3.6) (2024-12-11)

* Adds "learning_domains" entity plural

## [v0.3.5](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.4...v0.3.5) (2024-12-11)

* Adds "timestamp_microseconds" header when publishing messages

## [v0.3.4](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.3...v0.3.4) (2024-12-04)

* Adds metadata to published messages according to the [P-06 requirement](https://coda.io/d/Engineering-hub_daQaM_fX8Uq/Validating-our-events-tooling-py-implementation_suIj7ZCW#Library-Features_tuLA-bw0/r6&view=center)

## [v0.3.3](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.2...v0.3.3) (2024-12-02)

* Fixes bug in `EventPublisher.publish` where overriding the exchange with the default exchange (empty string '') would be ignored

## [v0.3.2](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.1...v0.3.2) (2024-11-27)

* Updates `entity_plurals.py`

## [v0.3.1](https://github.com/Multiverse-io/events_tooling_py/compare/v0.3.0...v0.3.1) (2024-11-27)

* Add requirement tests and run them in CI
* Allow overriding routing key and exchange in EventPublisher.publish() 

## [v0.3.0](https://github.com/Multiverse-io/events_tooling_py/compare/v0.2.3...v0.3.0) (2024-11-14)

* SC-74964 Simpler API for consuming and publishing events by @arnodel in https://github.com/Multiverse-io/events_tooling_py/pull/13

## [v0.2.3](https://github.com/Multiverse-io/events_tooling_py/compare/v0.2.2...v0.2.3) (2024-11-05)

* Remove `event-schemas` dependency
