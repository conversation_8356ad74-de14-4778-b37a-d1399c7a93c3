---
description: 
globs: 
alwaysApply: true
---
# Events Tooling Python Stack

## Metadata
| Category | Value |
|----|----|
| Python Version | 3.12 |
| Framework | Cloud Events Library (RabbitMQ-based) |
| Package Manager | Poetry |
| Type Checking | Mypy |
| Linting/Formatting | Ruff |
| Testing | Pytest with Syrupy for snapshot testing |

## Core Tools

| Tool | Purpose | Config Location | Command Template |
|---|---|-----|---|
| Poetry | Package Management | pyproject.toml | `poetry run {cmd}` |
| Mypy | Type Checking | pyproject.toml[tool.mypy] | `poetry run mypy` |
| Ruff | Linting & Formatting | pyproject.toml[tool.ruff.{lint,format}] | `poetry run ruff check/format` |
| Pytest | Testing | pyproject.toml[tool.pytest] | `poetry run pytest` |
| Syrupy | Snapshot Testing | N/A | Via pytest |

## Project Structure
```
/                           # Root directory
├── pyproject.toml         # Poetry & package manifest & tool configurations
├── poetry.lock           # Poetry dependency lock file
├── CHANGELOG.md         # Change history
├── README.md            # Project documentation
├── .gitignore           # Git ignore patterns
├── docs/                # Documentation directory
├── events_tooling_py/   # Main package directory
│   ├── __init__.py
│   ├── cloudevent.py           # CloudEvent schema implementation 
│   ├── config.py               # Configuration classes
│   ├── connection_client.py    # RabbitMQ connection management
│   ├── defaults.py             # Default values
│   ├── dispatcher.py           # Event dispatching functionality
│   ├── entity_plurals.py       # Entity plural name handling
│   ├── event_client.py         # Main event client implementations
│   ├── py.typed                # Type hints marker file
│   ├── types.py                # Type definitions and protocols
│   ├── utils.py                # Utility functions
│   ├── tests/                  # Test directory
│   │   ├── __init__.py
│   │   ├── __snapshots__/       # Snapshot test fixtures
│   │   ├── sample_events.py     # Example events for testing
│   │   ├── test_*.py            # Test files
│   │   └── conftest.py          # Pytest fixtures and configuration
│   └── requirement_tests/      # Requirements compatibility tests
└── .circleci/               # CI/CD configuration
```

## Package Management

### Poetry Commands
All commands should be run with Poetry:
```shell
# Install dependencies (sync with poetry.lock)
poetry install --sync

# Add a new dependency
poetry add package_name

# Add a dev dependency
poetry add --group dev package_name

# Update dependencies
poetry update

# Run any command in virtual environment
poetry run COMMAND

# Activate virtual environment shell
poetry shell
```

## Development Workflow

### Standard Development Loop
1. Type Check
   ```shell
   poetry run mypy
   ```

2. Lint and Format
   ```shell
   poetry run ruff check . --fix && poetry run ruff format .
   ```

3. Test
   ```shell
   # Run all tests
   poetry run pytest
   
   # Run tests faster by ignoring requirement tests
   poetry run py.test --ignore=events_tooling_py/requirement_tests
   ```

### Error Resolution Protocol
If any step fails:
1. Review error output
2. Apply necessary fixes
3. Restart from step 1 (type checking)
4. If cycle repeats 3 times:
   - Stop and document current state
   - Note what's breaking
   - Consider reverting changes
   - Seek guidance

## Framework Details

### Event System
- Core components:
  - `EventConsumer`: Handles consuming events from RabbitMQ
  - `EventPublisher`: For publishing events to RabbitMQ
  - `EventDispatcher`: Routes events to appropriate handlers based on event type
  - `CloudEventSchema`: CloudEvents standard compliant schema

### Event Handling
The library uses a combination of decorators and handler functions:

```python
from events_tooling_py.dispatcher import EventDispatcher
from events_tooling_py.types import EventContext

# Create a dispatcher
dispatcher = EventDispatcher()

# Register handler by event type string
@dispatcher.handle('multiverse.entity.action.v1')
def handle_event(event):
    # Process event
    pass

# Or register handler for specific event class
@dispatcher.register_handler_for_type(EntityActionEvent)
def handle_typed_event(event: EntityActionEvent):
    # Process event with typing
    pass

# Register handler with context
@dispatcher.register_handler
def handle_with_context(event: EntityActionEvent, ctx: EventContext):
    # Access envelope and metadata through ctx
    pass
```

### Publishing Events
```python
from events_tooling_py.event_client import EventPublisher
from events_tooling_py.config import ServiceConfig

# Configure service
service_config = ServiceConfig(service_name="my-service")

# Create publisher
publisher = EventPublisher(
    config={},
    rabbitmq_client=pika_client,
    service_config=service_config,
    event_schemas=[MyEvent]
)

# Create and publish event
event = MyEvent(field1="value", field2=123)
publisher.publish(event)
```

### Testing
- Unit tests use pytest fixtures for mocking RabbitMQ components
- Tests are organized in the `tests/` directory with standard naming `test_*.py`
- Snapshot testing is available through syrupy:
  ```python
  def test_event_structure(snapshot):
      event = create_test_event()
      assert event == snapshot
  ```

Update snapshots: `poetry run pytest path/to/test.py --snapshot-update`

### Error Handling
The library defines several error types:
- `NonRecoverableEventError`: Base for errors that shouldn't be retried
- `UnhandledEventTypeError`: When event type has no registered handler
- `InvalidEventPayloadError`: When event data is invalid
- `MessagingError`: For RabbitMQ connection/channel issues 