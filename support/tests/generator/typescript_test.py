from event_support.generator.typescript import (
    zod_schema_name,
    typescript_type_name,
    index_file_name,
    zod_schema_file_name,
)
from event_support.id import Id
from pathlib import Path


simple = Id("https://schema.multiverse.io/my/event/1.2.3")
underscore = Id("https://schema.multiverse.io/longer/event_name/2.0.0")


def test_zod_schema_name():
    assert zod_schema_name(simple) == "eventSchema"
    assert zod_schema_name(underscore) == "eventNameSchema"


def test_zod_schema_file_name():
    assert zod_schema_file_name(simple) == Path("lib/events/my/event/v1/eventSchema.ts")
    assert zod_schema_file_name(underscore) == Path(
        "lib/events/longer/event_name/v2/eventNameSchema.ts"
    )


def test_typescript_type_name():
    assert typescript_type_name(simple) == "Event"
    assert typescript_type_name(underscore) == "EventName"


def test_index_file_name():
    assert index_file_name(simple) == Path("lib/events/my/event/v1/index.ts")
    assert index_file_name(underscore) == Path(
        "lib/events/longer/event_name/v2/index.ts"
    )
