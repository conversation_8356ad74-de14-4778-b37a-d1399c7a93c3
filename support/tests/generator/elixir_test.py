from event_support.generator.elixir import module_name, file_name, dump_json
from event_support.id import Id
from event_support.schema import <PERSON>hema


def test_module_name():
    simple = Id("https://schema.multiverse.io/my/event/1.2.3")
    assert module_name(simple) == "EventSchemas.My.Event.V1"

    underscore = Id("https://schema.multiverse.io/longer/event_name/2.0.0")
    assert module_name(underscore) == "EventSchemas.Longer.EventName.V2"


def test_file_name():
    simple = Id("https://schema.multiverse.io/my/event/1.2.3")
    assert file_name(simple) == "lib/event_schemas/my/event/v1.ex"

    underscore = Id("https://schema.multiverse.io/longer/event_name/2.0.0")
    assert file_name(underscore) == "lib/event_schemas/longer/event_name/v2.ex"


def test_dump_json():
    code = """
$id: https://schema.multiverse.io/thing/event/2.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
properties:
  id:
    type: string
    description: |
      Something which requires "double quotes"
      and
      multiple
      lines
    """
    schema = Schema(path="/thing/event/v2.yaml", code=code)

    expected = """{
  "$id": "https://schema.multiverse.io/thing/event/2.1.0",
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "required": [
    "id"
  ],
  "properties": {
    "id": {
      "type": "string",
      "description": "Something which requires \\\\"double quotes\\\\"\\\\nand\\\\nmultiple\\\\nlines\\\\n"
    }
  }
}"""

    assert dump_json(schema) == expected
