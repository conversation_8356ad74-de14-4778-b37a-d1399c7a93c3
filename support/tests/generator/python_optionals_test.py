from event_support.generator.python import find_non_nullable_optionals


# Tests for basic types


def test_empty():
    assert find_non_nullable_optionals({}) == {}


def test_basic():
    assert (
        find_non_nullable_optionals(
            {
                "type": "string",
            }
        )
        == {}
    )


# Tests for `objects`


def test_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "required": ["id"],
                "properties": {"id": {"type": "string"}},
            }
        )
        == {}
    )


def test_option():
    assert find_non_nullable_optionals(
        {"type": "object", "properties": {"id": {"type": "string"}}}
    ) == {"id": True}


def test_nullable_option():
    assert (
        find_non_nullable_optionals(
            {"type": "object", "properties": {"id": {"type": ["string", "null"]}}}
        )
        == {}
    )


def test_required_object_with_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "required": ["id"],
                "properties": {
                    "id": {
                        "type": "object",
                        "required": ["id"],
                        "properties": {"id": {"type": "string"}},
                    }
                },
            }
        )
        == {}
    )


def test_optional_object_with_required():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {
                "id": {
                    "type": "object",
                    "required": ["id"],
                    "properties": {"id": {"type": "string"}},
                }
            },
        }
    ) == {"id": True}


def test_required_object_with_optional():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "required": ["id"],
            "properties": {
                "id": {"type": "object", "properties": {"id": {"type": "string"}}}
            },
        }
    ) == {"id": {"id": True}}


def test_optional_object_with_optional():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {
                "id": {"type": "object", "properties": {"id": {"type": "string"}}}
            },
        }
    ) == {"id": {"id": True}}


# Tests for `arrays`


def test_required_array():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "required": ["a"],
                "properties": {"a": {"type": "array", "items": {"type": "string"}}},
            }
        )
        == {}
    )


def test_optional_array():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {"array": {"type": "array", "items": {"type": "string"}}},
        }
    ) == {"array": True}


def test_required_array_with_optional():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "required": ["array"],
            "properties": {
                "array": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {"id": {"type": "string"}},
                    },
                }
            },
        }
    ) == {"array": {"id": True}}


# Tests for `allOf`


def test_allof_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "name": {"type": "string"},
                    "age": {"type": "number"},
                },
                "allOf": [
                    {"required": ["id"]},
                    {"required": ["name"]},
                    {"required": ["age"]},
                ],
            }
        )
        == {}
    )


def test_allof_non_nullable_optional():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "allOf": [
                {"properties": {"id": {"type": "string"}}},
                {"properties": {"name": {"type": "string"}}},
                {"properties": {"age": {"type": "number"}}},
            ],
        }
    ) == {"id": True, "name": True, "age": True}


def test_allof_nullable_optional():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "allOf": [
                    {"properties": {"id": {"type": ["string", "null"]}}},
                    {"properties": {"name": {"type": ["string", "null"]}}},
                    {"properties": {"age": {"type": ["number", "null"]}}},
                ],
            }
        )
        == {}
    )


def test_required_name_with_allof_non_nullable_optional():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "required": ["name"],
                "allOf": [
                    {"properties": {"name": {"type": "string"}}},
                ],
            }
        )
        == {}
    )


# Tests for `anyOf`


def test_anyof_nullable_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "properties": {"id": {"type": "string"}},
                "anyOf": [
                    {"required": ["id", "name"]},
                    {"required": ["id", "age"]},
                ],
            }
        )
        == {}
    )


def test_anyof_flat_non_nullable_required():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"},
                "age": {"type": "number"},
            },
            "anyOf": [{"required": ["id", "name"]}, {"required": ["id", "age"]}],
        }
    ) == {"name": True, "age": True}


def test_anyof_nested_non_nullable_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "anyOf": [
                    {
                        "required": ["id", "name"],
                        "properties": {
                            "id": {"type": "string"},
                            "name": {"type": "string"},
                        },
                    },
                    {
                        "required": ["id", "age"],
                        "properties": {
                            "id": {"type": "string"},
                            "age": {"type": "number"},
                        },
                    },
                ],
            }
        )
        == {}
    )


def test_anyof_nested_nullable_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "anyOf": [
                    {
                        "required": ["id", "name"],
                        "properties": {
                            "id": {"type": "string"},
                            "name": {"type": ["string", "null"]},
                        },
                    },
                    {
                        "required": ["id", "age"],
                        "properties": {
                            "id": {"type": "string"},
                            "age": {"type": ["number", "null"]},
                        },
                    },
                ],
            }
        )
        == {}
    )


def test_anyof_nested_non_nullable_optional():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "required": ["id"],
            "properties": {"id": {"type": "string"}},
            "anyOf": [
                {
                    "properties": {
                        "name": {"type": "string"},
                    }
                },
                {"properties": {"age": {"type": "number"}}},
            ],
        }
    ) == {"name": True, "age": True}


def test_anyof_nested_nullable_optional():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "required": ["id"],
                "properties": {"id": {"type": "string"}},
                "anyOf": [
                    {
                        "properties": {
                            "name": {"type": ["string", "null"]},
                        }
                    },
                    {"properties": {"age": {"type": ["number", "null"]}}},
                ],
            }
        )
        == {}
    )


# Tests for `oneOf`


def test_oneof_nullable_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "properties": {"id": {"type": "string"}},
                "oneOf": [
                    {"required": ["id", "name"]},
                    {"required": ["id", "age"]},
                ],
            }
        )
        == {}
    )


def test_oneof_flat_non_nullable_required():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"},
                "age": {"type": "number"},
            },
            "oneOf": [{"required": ["id", "name"]}, {"required": ["id", "age"]}],
        }
    ) == {"name": True, "age": True}


def test_oneof_nested_non_nullable_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "oneOf": [
                    {
                        "required": ["id", "name"],
                        "properties": {
                            "id": {"type": "string"},
                            "name": {"type": "string"},
                        },
                    },
                    {
                        "required": ["id", "age"],
                        "properties": {
                            "id": {"type": "string"},
                            "age": {"type": "number"},
                        },
                    },
                ],
            }
        )
        == {}
    )


def test_oneof_nested_nullable_required():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "oneOf": [
                    {
                        "required": ["id", "name"],
                        "properties": {
                            "id": {"type": "string"},
                            "name": {"type": ["string", "null"]},
                        },
                    },
                    {
                        "required": ["id", "age"],
                        "properties": {
                            "id": {"type": "string"},
                            "age": {"type": ["number", "null"]},
                        },
                    },
                ],
            }
        )
        == {}
    )


def test_oneof_nested_non_nullable_optional():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "required": ["id"],
            "properties": {"id": {"type": "string"}},
            "oneOf": [
                {
                    "properties": {
                        "name": {"type": "string"},
                    }
                },
                {"properties": {"age": {"type": "number"}}},
            ],
        }
    ) == {"name": True, "age": True}


def test_oneof_nested_nullable_optional():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "required": ["id"],
                "properties": {"id": {"type": "string"}},
                "oneOf": [
                    {
                        "properties": {
                            "name": {"type": ["string", "null"]},
                        }
                    },
                    {"properties": {"age": {"type": ["number", "null"]}}},
                ],
            }
        )
        == {}
    )


def test_complex_schema():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "required": ["type"],
            "properties": {
                "type": {"enum": ["LEVY", "SAAS"], "default": "SAAS"},
                "id": {"type": "string"},
                "name": {"type": "string"},
                "age": {"type": "number"},
            },
            "anyOf": [
                {"required": ["id", "name"]},
                {"required": ["id", "age"]},
            ],
            "oneOf": [
                {
                    "required": ["other"],
                    "properties": {
                        "type": {"const": "LEVY"},
                        "test1": {"type": "string"},
                        "other": {"type": "string"},
                    },
                },
                {
                    "properties": {
                        "type": {"const": "SAAS"},
                        "test2": {"type": "string"},
                    },
                },
            ],
        }
    ) == {"test1": True, "test2": True, "name": True, "age": True}


def test_type_equivalent_schemas():
    assert find_non_nullable_optionals(
        {"type": "object", "properties": {"id": {"type": ["string", "number"]}}}
    ) == find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {"id": {"oneOf": [{"type": "string"}, {"type": "number"}]}},
        }
    )


def test_type_not_null_equivalent():
    assert find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {
                "id": {"type": ["string", "number", "boolean", "object", "array"]}
            },
        }
    ) == find_non_nullable_optionals(
        {
            "type": "object",
            "properties": {"id": {"not": [{"type": "null"}]}},
        }
    )


def test_type_not_any_except_null():
    assert (
        find_non_nullable_optionals(
            {
                "type": "object",
                "properties": {
                    "id": {
                        "not": [
                            {"type": ["string", "number", "boolean", "object", "array"]}
                        ]
                    }
                },
            }
        )
        == {}
    )
