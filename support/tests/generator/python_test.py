from pathlib import Path

import pytest
from event_support.generator.python import (
    pydantic_model_name,
    schema_output_dir,
)
from event_support.id import Id

simple = Id("https://schema.multiverse.io/my/event/1.2.3")
underscore = Id("https://schema.multiverse.io/longer/event_name/2.0.0")


@pytest.mark.parametrize(
    "event_id, expected",
    (
        (simple, "EventSchema"),
        (underscore, "EventNameSchema"),
    ),
)
def test_pydantic_model_name(event_id, expected):
    assert pydantic_model_name(event_id) == expected


@pytest.mark.parametrize(
    "event_id, expected",
    (
        (simple, Path("my/event/v1")),
        (underscore, Path("longer/event_name/v2")),
    ),
)
def test_schema_output_dir(event_id, expected):
    assert schema_output_dir(event_id) == expected
