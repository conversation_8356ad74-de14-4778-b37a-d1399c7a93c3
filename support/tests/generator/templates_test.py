"""Tests for template rendering and integration."""

from unittest.mock import MagicMock

from event_support.generator.python import (
    CLOUDEVENTS_TEMPLATE,
)


class TestTemplates:
    """Tests for template rendering and integration."""

    def test_cloudevents_template_includes_raw_json_schema(self):
        """Test that the cloudevents template includes the raw_json_schema attribute in EventBase."""
        # Render the template
        rendered = CLOUDEVENTS_TEMPLATE.render()

        # Check that the raw_json_schema attribute is included in the EventBase protocol
        assert "class EventBase(typing.Protocol):" in rendered
        assert "class Meta(typing.Protocol):" in rendered
        assert "raw_json_schema: " in rendered

    def test_meta_class_includes_raw_json_schema(self):
        """Test that the Meta class in generated models includes the raw_json_schema attribute."""
        from unittest.mock import patch
        from event_support.generator.python import generate_meta_class_content

        # Create a mock Schema
        mock_schema = MagicMock()
        mock_schema.id = MagicMock()
        mock_schema.id.event_name = "test_event"
        mock_schema.id.value = (
            "https://schema.multiverse.io/test_namespace/test_event/1.0.0"
        )
        mock_schema.definition = {
            "$id": "https://schema.multiverse.io/test_namespace/test_event/1.0.0",
            "type": "object",
            "properties": {"id": {}, "name": {}},
        }

        # Set up utils.event_type to return a predictable value
        with patch(
            "event_support.generator.utils.event_type", return_value="test_event"
        ):
            # Call the function directly
            meta_content = generate_meta_class_content(mock_schema)

            # Check that the Meta class is included
            assert "class Meta:" in meta_content
            assert "schema_type = " in meta_content
            assert "schema_uri = " in meta_content
            assert "raw_json_schema = " in meta_content

            # Check that the schema definition is included (not as a string)
            assert str(mock_schema.definition) in meta_content
