from textwrap import dedent

import pytest
import yaml

import event_support.schema as schema


def test_additional_properties():
    valid_simple = {"properties": {"name": {"type": "string"}}}

    assert schema.validate_additional_properties(valid_simple)

    valid_nested = {
        "properties": {
            "details": {"type": "object", "properties": {"name": {"type": "string"}}}
        }
    }

    assert schema.validate_additional_properties(valid_nested)

    invalid_simple = {
        "properties": {"name": {"type": "string"}},
        "additionalProperties": False,
    }

    with pytest.raises(RuntimeError):
        schema.validate_additional_properties(invalid_simple)

    invalid_nested = {
        "properties": {
            "details": {
                "type": "object",
                "properties": {"name": {"type": "string"}},
                "additionalProperties": False,
            }
        }
    }

    with pytest.raises(RuntimeError):
        schema.validate_additional_properties(invalid_nested)


def test_extra_required_properties():
    valid_simple = {
        "properties": {"name": {"type": "string"}},
        "required": ["name"],
    }

    assert schema.validate_required_properties(valid_simple)

    invalid_simple = {
        "properties": {"name": {"type": "string"}},
        "required": ["name", "age"],
    }

    with pytest.raises(RuntimeError):
        schema.validate_required_properties(invalid_simple)

    invalid_nested = {
        "properties": {
            "details": {
                "type": "object",
                "properties": {"name": {"type": "string"}},
                "required": ["name", "age"],
            }
        },
    }

    with pytest.raises(RuntimeError):
        schema.validate_required_properties(invalid_nested)


@pytest.mark.parametrize(
    "yaml_src, python_obj, duplicate_key",
    (
        (
            """
            a: 1
            x: 3
            a: 2
            """,
            None,
            "'a'",
        ),
        (
            """
            a: 1
            b: 2
            """,
            {"a": 1, "b": 2},
            None,
        ),
        (
            """
            - a: 1
            - a: 2
            """,
            [{"a": 1}, {"a": 2}],
            None,
        ),
        (
            """
            - a:
                b: 2
                x: 5
                c: 3
                x: 7
            """,
            None,
            "'x'",
        ),
    ),
)
def test_UniqueKeyValidatingLoader(yaml_src, python_obj, duplicate_key):
    yaml_src = dedent(yaml_src)
    if duplicate_key is not None:
        with pytest.raises(RuntimeError) as e:
            yaml.load(yaml_src, schema.UniqueKeyValidatingLoader)
        assert duplicate_key in str(e)
    if python_obj is not None:
        assert python_obj == yaml.load(yaml_src, schema.UniqueKeyValidatingLoader)
