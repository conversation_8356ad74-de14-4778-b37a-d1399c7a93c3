from event_support.compatible import check
import pytest


def test_check():
    old = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
            "b": {"type": "integer"},
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
            "b": {"type": "integer"},
            "c": {"type": "integer"},
        },
        "required": ["a"],
    }

    assert check(old, new)


def test_remove_required_field():
    old = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
        },
        "required": [],
    }

    with pytest.raises(RuntimeError):
        check(old, new)


def test_add_required_field():
    old = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
            "b": {"type": "integer"},
        },
        "required": ["a", "b"],
    }

    with pytest.raises(RuntimeError):
        check(old, new)


def test_add_optional_field():
    old = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
            "b": {"type": "integer"},
        },
        "required": ["a"],
    }

    assert check(old, new)


def test_remove_optional_field():
    old = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
            "b": {"type": "integer"},
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
        },
        "required": ["a"],
    }

    with pytest.raises(RuntimeError):
        check(old, new)


def test_add_optional_nested():
    old = {
        "type": "object",
        "properties": {},
        "required": [],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {
                "type": "object",
                "properties": {
                    "aa": {"type": "integer"},
                    "bb": {"type": "integer"},
                },
                "required": ["aa"],
            },
        },
        "required": [],
    }

    assert check(old, new)


def test_nested_add_optional():
    old = {
        "type": "object",
        "properties": {
            "a": {
                "type": "object",
                "properties": {
                    "aa": {"type": "integer"},
                },
                "required": ["aa"],
            },
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {
                "type": "object",
                "properties": {
                    "aa": {"type": "integer"},
                    "bb": {"type": "integer"},
                },
                "required": ["aa"],
            },
        },
        "required": ["a"],
    }

    assert check(old, new)


def test_nested_add_required():
    old = {
        "type": "object",
        "properties": {
            "a": {
                "type": "object",
                "properties": {
                    "aa": {"type": "integer"},
                },
                "required": ["aa"],
            },
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {
                "type": "object",
                "properties": {
                    "aa": {"type": "integer"},
                    "bb": {"type": "integer"},
                },
                "required": ["aa", "bb"],
            },
        },
        "required": ["a"],
    }

    with pytest.raises(RuntimeError):
        check(old, new)


def test_nested_remove_required():
    old = {
        "type": "object",
        "properties": {
            "a": {
                "type": "object",
                "properties": {
                    "aa": {"type": "integer"},
                },
                "required": ["aa"],
            },
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {
                "type": "object",
                "properties": {
                    "aa": {"type": "integer"},
                },
                "required": [],
            },
        },
        "required": ["a"],
    }

    with pytest.raises(RuntimeError):
        check(old, new)


def test_add_array():
    old = {
        "type": "object",
        "properties": {},
        "required": [],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "aa": {"type": "integer"},
                    },
                },
            },
        },
        "required": [],
    }

    assert check(old, new)


def test_array_add_optional():
    old = {
        "type": "object",
        "properties": {
            "a": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "aa": {"type": "integer"},
                    },
                },
            },
        },
        "required": [],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "aa": {"type": "integer"},
                        "bb": {"type": "integer"},
                    },
                },
            },
        },
        "required": [],
    }

    assert check(old, new)


def test_array_remove_optional():
    old = {
        "type": "object",
        "properties": {
            "a": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "aa": {"type": "integer"},
                    },
                },
            },
        },
        "required": [],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {},
                },
            },
        },
        "required": [],
    }

    with pytest.raises(RuntimeError):
        check(old, new)


def test_format_changed():
    old = {
        "type": "object",
        "properties": {
            "a": {"type": "string", "format": "email"},
        },
        "required": ["a"],
    }

    new = {
        "type": "object",
        "properties": {
            "a": {"type": "string", "format": "uuid"},
        },
        "required": ["a"],
    }

    with pytest.raises(RuntimeError):
        check(old, new)


def test_unchanged_empty_object():
    old = {
        "type": "object",
    }

    new = {
        "type": "object",
    }

    assert check(old, new)


def test_adding_properties_to_previously_empty_object():
    old = {
        "type": "object",
    }

    new = {
        "type": "object",
        "properties": {
            "a": {"type": "integer"},
        },
    }

    assert check(old, new)
