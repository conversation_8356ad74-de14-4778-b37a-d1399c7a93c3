from event_support.id import Id
import pytest

invalid_ids = [
    "https://schema.multiverse.io/no/version/",
    "  https://schema.multiverse.io/with/padding/1.0.0",
    "https://schema.multiverse.io/with/padding/2.0.0  ",
    "https://schema.multiverse.io/singleslash/1.0.0",
    "https://schema.multiverse.io/too/many/slashes/1.0.0",
    "https://schema.multiverse.io/semver/wrong/1.0",
    "https://schema.multiverse.io/semver/wrong/2",
]


def test_valid_id():
    id1 = Id(
        "https://schema.multiverse.io/account/signed_up/1.0.0",
        "schemas/account/signed_up/v1.yaml",
    )
    assert id1.namespace == "account"
    assert id1.event_name == "signed_up"
    assert id1.major == 1
    assert id1.minor == 0
    assert id1.patch == 0


def test_invalid_ids():
    for id in invalid_ids:
        with pytest.raises(RuntimeError):
            Id(id)


def test_invalid_path():
    with pytest.raises(RuntimeError):
        Id(
            "https://schema.multiverse.io/account/signed_up/1.0.0",
            "otherservice/signed_up/v1.yaml",
        )

    with pytest.raises(RuntimeError):
        Id(
            "https://schema.multiverse.io/account/signed_up/1.0.0",
            "account/signed_up/v2.yaml",
        )

    with pytest.raises(RuntimeError):
        Id(
            "https://schema.multiverse.io/account/signed_up/1.0.0",
            "account/closed_account/v1.yaml",
        )


def test_is_minor_bump():
    a = Id("https://schema.multiverse.io/account/signed_up/1.0.0")
    b = Id("https://schema.multiverse.io/account/signed_up/1.1.0")
    c = Id("https://schema.multiverse.io/account/signed_up/1.0.1")
    d = Id("https://schema.multiverse.io/account/signed_up/1.1.1")
    e = Id("https://schema.multiverse.io/account/signed_up/2.0.0")

    assert a.is_minor_bump(b)
    assert a.is_minor_bump(c)
    assert b.is_minor_bump(d)

    assert not a.is_minor_bump(e)
    assert not b.is_minor_bump(a)


def test_allow_breaking_changes():
    unstable_old = Id("https://schema.multiverse.io/account/signed_up/0.1.0")
    unstable_new = Id("https://schema.multiverse.io/account/signed_up/0.2.0")
    unstable_bad = Id("https://schema.multiverse.io/account/signed_up/0.1.1")

    stable_old = Id("https://schema.multiverse.io/account/signed_up/1.0.0")
    stable_new = Id("https://schema.multiverse.io/account/signed_up/2.0.0")
    stable_bad = Id("https://schema.multiverse.io/account/signed_up/1.1.0")

    assert unstable_old.allow_breaking_changes(unstable_new)
    assert not unstable_old.allow_breaking_changes(unstable_bad)

    assert stable_old.allow_breaking_changes(stable_new)
    assert not stable_old.allow_breaking_changes(stable_bad)
