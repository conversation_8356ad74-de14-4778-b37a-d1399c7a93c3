[tool.poetry]
name = "event-support"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
pyyaml = "^6.0.1"
jsonschema = "^4.20.0"
requests = "^2.32.2"
jinja2 = "^3.1.5"
casefy = "^0.1.7"
gitpython = "^3.1.41"
termcolor = "^2.4.0"
datamodel-code-generator = "^0.25.5"


[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
black = "^24.3.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
exclude = "generator/templates/python/*"