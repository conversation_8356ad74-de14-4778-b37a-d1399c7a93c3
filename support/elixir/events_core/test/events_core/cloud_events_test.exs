defmodule EventsCore.CloudEventsTest do
  use ExUnit.Case, async: true
  alias EventsCore.CloudEvents

  @source "/myservice"
  @event_type "multiverse.myservice.event.v1"
  @schema_uri "https://schema.multiverse.io/myservice/event/1.0.0"
  @payload %{"a" => 123}
  @subject "123"
  @time ~U[2024-01-01 10:10:00.000000Z]

  describe "encode!" do
    test "with valid params produces vaild cloudevent" do
      event = CloudEvents.encode!(@source, @event_type, @schema_uri, @payload)

      decoded = Jason.decode!(event)

      assert Uniq.UUID.valid?(decoded["id"])
      assert decoded["source"] == @source
      assert decoded["specversion"] == "1.0"
      assert decoded["type"] == @event_type
      assert decoded["datacontenttype"] == "application/json"
      assert decoded["dataschema"] == @schema_uri
      assert decoded["data"] == @payload

      refute decoded["subject"]

      timestamp = decoded["time"] |> DateTime.from_iso8601() |> elem(1)
      assert DateTime.diff(timestamp, DateTime.utc_now(), :millisecond) < :timer.seconds(1)
    end

    test "with optional keys" do
      event =
        CloudEvents.encode!(@source, @event_type, @schema_uri, @payload,
          subject: @subject,
          time: @time
        )

      decoded = Jason.decode!(event)

      assert decoded["subject"] == @subject
      assert decoded["time"] == "2024-01-01T10:10:00.000000Z"
    end
  end

  describe "decode!" do
    test "decoding valid cloudevent" do
      id = Uniq.UUID.uuid4()

      event = %{
        "id" => id,
        "source" => @source,
        "specversion" => "1.0",
        "type" => @event_type,
        "datacontenttype" => "application/json",
        "dataschema" => @schema_uri,
        "data" => @payload
      }

      json = Jason.encode!(event)

      decoded = CloudEvents.decode!(json)

      assert decoded.id == id
      assert decoded.source == @source
      assert decoded.specversion == "1.0"
      assert decoded.type == @event_type
      assert decoded.datacontenttype == "application/json"
      assert decoded.dataschema == @schema_uri
      assert decoded.data == @payload
    end
  end

  describe "is_cloud_event?/1" do
    test "returns true with a cloud event" do
      data = %{
        "id" => Uniq.UUID.uuid4(),
        "source" => @source,
        "specversion" => "1.0",
        "type" => @event_type,
        "datacontenttype" => "application/json",
        "dataschema" => @schema_uri,
        "data" => @payload
      }

      assert CloudEvents.is_cloud_event?(data)
    end

    test "returns false with something which isn't a cloud event" do
      refute CloudEvents.is_cloud_event?(@payload)
    end
  end
end
