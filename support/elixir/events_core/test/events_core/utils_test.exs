defmodule EventsCore.UtilTest do
  use ExUnit.Case, async: true
  alias EventsCore.Util

  describe "atom_keys_to_string/1" do
    test "with atom keyed map" do
      map = %{
        a: 1,
        b: %{c: 2},
        d: [3],
        e: [%{f: 4}]
      }

      expected = %{
        "a" => 1,
        "b" => %{"c" => 2},
        "d" => [3],
        "e" => [%{"f" => 4}]
      }

      assert Util.atom_keys_to_string(map) == expected
    end

    test "passes through string keyed map" do
      map = %{
        "a" => 1,
        "b" => %{"c" => 2},
        "d" => [3],
        "e" => [%{"f" => 4}]
      }

      assert Util.atom_keys_to_string(map) == map
    end
  end

  describe "string_keys_to_atom/1" do
    test "with string keyed map" do
      map = %{
        "a" => 1,
        "b" => %{"c" => 2},
        "d" => [3],
        "e" => [%{"f" => 4}]
      }

      expected = %{
        a: 1,
        b: %{c: 2},
        d: [3],
        e: [%{f: 4}]
      }

      assert Util.string_keys_to_atom(map) == expected
    end

    test "passes through atom keyed map" do
      map = %{
        a: 1,
        b: %{c: 2},
        d: [3],
        e: [%{f: 4}]
      }

      assert Util.string_keys_to_atom(map) == map
    end
  end
end
