defmodule EventsCore.ValidateTest do
  use ExUnit.Case, async: true
  alias EventsCore.Validate

  @schema %{
    "type" => "object",
    "properties" => %{
      "a" => %{
        "type" => "integer"
      },
      "b" => %{
        "type" => "object",
        "properties" => %{
          "c" => %{
            "type" => "integer"
          }
        },
        "required" => [
          "c"
        ]
      },
      "d" => %{
        "type" => "integer"
      },
      "e" => %{
        "type" => "array",
        "items" => %{
          "type" => "number"
        }
      },
      "f" => %{
        "type" => "array",
        "items" => %{
          "type" => "object",
          "properties" => %{
            "g" => %{
              "type" => "integer"
            }
          },
          "required" => [
            "g"
          ]
        }
      },
      "h" => %{
        "type" => "object"
      }
    },
    "required" => [
      "a"
    ]
  }

  test "with required field" do
    data = %{"a" => 123}

    assert :ok == Validate.strict_validate_keys(data, @schema)
  end

  test "with optional property" do
    data = %{"a" => 123, "d" => 123}

    assert :ok == Validate.strict_validate_keys(data, @schema)
  end

  test "missing required property" do
    data = %{"d" => 123}

    assert {:error, "missing required key .a"} == Validate.strict_validate_keys(data, @schema)
  end

  test "with optional sub object" do
    data = %{"a" => 123, "b" => %{"c" => 123}}

    assert :ok == Validate.strict_validate_keys(data, @schema)
  end

  test "with optional sub object without required property" do
    data = %{"a" => 123, "b" => %{}}

    assert {:error, "missing required key .b.c"} == Validate.strict_validate_keys(data, @schema)
  end

  test "with unknown property" do
    data = %{"a" => 123, "blah" => 123}

    assert {:error, "unknown key .blah"} == Validate.strict_validate_keys(data, @schema)
  end

  test "with unknown property in sub object" do
    data = %{"a" => 123, "b" => %{"c" => 123, "blah" => 123}}

    assert {:error, "unknown key .b.blah"} == Validate.strict_validate_keys(data, @schema)
  end

  test "with basic array property" do
    data = %{"a" => 123, "e" => [1, 2, 3]}

    assert :ok == Validate.strict_validate_keys(data, @schema)
  end

  test "with object array property" do
    data = %{"a" => 123, "f" => [%{"g" => 123}]}

    assert :ok == Validate.strict_validate_keys(data, @schema)
  end

  test "with object array property with unknown property" do
    data = %{"a" => 123, "f" => [%{"g" => 123, "blah" => 123}]}

    assert {:error, "unknown key .f[0].blah"} == Validate.strict_validate_keys(data, @schema)
  end

  test "with object array property with missing required property" do
    data = %{"a" => 123, "f" => [%{}]}

    assert {:error, "missing required key .f[0].g"} == Validate.strict_validate_keys(data, @schema)
  end

  test "with empty object for schema where properties are not specified" do
    data = %{"a" => 123, "h" => %{}}

    assert :ok == Validate.strict_validate_keys(data, @schema)
  end

  test "with non-empty object where properties are not specified" do
    data = %{"a" => 123, "h" => %{"foo" => "bar"}}

    assert :ok == Validate.strict_validate_keys(data, @schema)
  end

  test "type object but actually isn't object" do
    data = %{"a" => 123, "b" => 123}

    assert {:error, "expected object at .b"} == Validate.strict_validate_keys(data, @schema)
  end

  test "type not object but actually is an object" do
    data = %{"a" => %{"bleh" => 123}}

    assert {:error, "expected integer at .a"} == Validate.strict_validate_keys(data, @schema)
  end
end
