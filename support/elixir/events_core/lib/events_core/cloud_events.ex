defmodule EventsCore.CloudEvents do
  @doc """
  Encode a map into a valid CloudEvent.

  Options:
  - `subject` - The cloudevent subject. Will be omitted if not provided
  - `time` - The cloudevent time. Will default to the current time if not provided
  """
  def encode!(source, type, schema_uri, payload, opts \\ []) do
    envelope =
      %{
        id: Uniq.UUID.uuid4(),
        source: source,
        specversion: "1.0",
        type: type,
        datacontenttype: "application/json",
        dataschema: schema_uri,
        data: payload
      }
      |> add_optional_subject(opts)
      |> add_optional_time(opts)

    Jason.encode!(envelope)
  end

  defp add_optional_subject(envelope, opts) do
    opt = Keyword.get(opts, :subject, nil)

    if opt do
      Map.put(envelope, :subject, opt)
    else
      envelope
    end
  end

  defp add_optional_time(envelope, opts) do
    opt = Keyword.get(opts, :time, DateTime.utc_now())

    rfc3339 =
      cond do
        is_binary(opt) ->
          opt

        match?(%DateTime{}, opt) ->
          # The ISO format this generates is also rfc3339 compliant
          DateTime.to_iso8601(opt)

        true ->
          raise "invalid time"
      end

    Map.put(envelope, :time, rfc3339)
  end

  @doc """
  Decodes a JSON encoded cloud event.

  Also converts the top level attributes to atoms but leaves the payload as string keys
  """
  def decode!(data) do
    data
    |> Jason.decode!()
    |> Enum.map(fn {k, v} ->
      {String.to_existing_atom(k), v}
    end)
    |> Enum.into(%{})
  end

  @doc """
  Returns true if the given data looks like a cloud event.

  Only looks at keys and doesn't validate values.
  """
  def is_cloud_event?(data) do
    expected_keys = [
      "id",
      "source",
      "specversion",
      "type",
      "datacontenttype",
      "dataschema",
      "data"
    ]

    Enum.all?(expected_keys, &Map.has_key?(data, &1))
  end
end
