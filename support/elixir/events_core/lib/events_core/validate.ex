defmodule EventsCore.Validate do
  @doc """
  Validates that all required keys are present and no unknown keys are present

  Does not validate the format of values.

  Intended to be used in producers in addition to the validation performed by the consumer
  """
  def strict_validate_keys(data, schema, path \\ []) do
    cond do
      schema["type"] == "object" ->
        if is_map(data) do
          do_strict_validate_keys(data, schema, path)
        else
          {:error, "expected object at #{path_to_string(path)}"}
        end

      is_map(data) ->
        {:error, "expected #{schema["type"]} at #{path_to_string(path)}"}

      true ->
        :ok
    end
  end

  defp do_strict_validate_keys(data, schema, path) do
    required_keys = Map.get(schema, "required", [])

    {required, optional} =
      Map.get(schema, "properties", [])
      |> Enum.split_with(fn {key, _value} ->
        key in required_keys
      end)

    required_validations =
      required
      |> Enum.map(fn {key, value} ->
        if Map.has_key?(data, key) do
          validate_property(data, key, value, path)
        else
          {:error, "missing required key #{path_to_string(path, key)}"}
        end
      end)

    optional_validations =
      optional
      |> Enum.map(fn {key, value} ->
        if Map.has_key?(data, key) do
          validate_property(data, key, value, path)
        else
          :ok
        end
      end)

    all = Enum.map(required ++ optional, &elem(&1, 0))

    unknown_validations =
      data
      |> Enum.map(fn {key, _value} ->
        if key in all or Enum.empty?(all) do
          :ok
        else
          {:error, "unknown key #{path_to_string(path, key)}"}
        end
      end)

    validations = required_validations ++ optional_validations ++ unknown_validations

    errors = Enum.filter(validations, &match?({:error, _}, &1))

    if errors == [] do
      :ok
    else
      List.first(errors)
    end
  end

  defp validate_property(data, key, value, path) do
    case value["type"] do
      "array" ->
        errors =
          data[key]
          |> Enum.with_index()
          |> Enum.map(fn {item, index} ->
            path = [path, ?., key, ?[, to_string(index), ?]]
            strict_validate_keys(item, value["items"], path)
          end)
          |> Enum.filter(&match?({:error, _}, &1))

        if errors == [] do
          :ok
        else
          List.first(errors)
        end

      _ ->
        strict_validate_keys(data[key], value, [path, ?., key])
    end
  end

  def path_to_string(path) do
    IO.iodata_to_binary(path)
  end

  def path_to_string(path, key) do
    IO.iodata_to_binary([path, ?., key])
  end
end
