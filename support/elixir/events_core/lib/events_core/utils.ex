defmodule EventsCore.Util do
  def atom_keys_to_string(map) when is_map(map) do
    Map.new(map, &do_atom_keys_to_string/1)
  end

  def atom_keys_to_string(value) do
    value
  end

  defp do_atom_keys_to_string({key, val}) when is_map(val) do
    {convert_key_to_string(key), atom_keys_to_string(val)}
  end

  defp do_atom_keys_to_string({key, val}) when is_list(val) do
    {convert_key_to_string(key), Enum.map(val, &atom_keys_to_string(&1))}
  end

  defp do_atom_keys_to_string({key, val}) do
    {convert_key_to_string(key), val}
  end

  defp convert_key_to_string(key) when is_atom(key), do: Atom.to_string(key)
  defp convert_key_to_string(key), do: key

  def string_keys_to_atom(map) when is_map(map) do
    Map.new(map, &do_string_keys_to_atom/1)
  end

  def string_keys_to_atom(value) do
    value
  end

  defp do_string_keys_to_atom({key, val}) when is_map(val) do
    {convert_key_to_atom(key), string_keys_to_atom(val)}
  end

  defp do_string_keys_to_atom({key, val}) when is_list(val) do
    {convert_key_to_atom(key), Enum.map(val, &string_keys_to_atom(&1))}
  end

  defp do_string_keys_to_atom({key, val}) do
    {convert_key_to_atom(key), val}
  end

  defp convert_key_to_atom(key) when is_binary(key), do: String.to_atom(key)
  defp convert_key_to_atom(key), do: key
end
