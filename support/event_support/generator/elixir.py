from jinja2 import Environment, PackageLoader, select_autoescape, StrictUndefined
from datetime import datetime, timezone
from pathlib import Path
import casefy
import json
import glob
import git
import event_support.generator.utils as utils
from event_support.schema import Schema

env = Environment(
    loader=PackageLoader("event_support.generator"),
    autoescape=select_autoescape(),
    undefined=StrictUndefined,
)


def generate(schema_dir, outdir):
    utils.empty_dir(outdir)

    schema_files = glob.glob(f"{schema_dir}/**/*.yaml", recursive=True)

    for file in schema_files:
        generate_event(Schema(path=file), outdir)

    generate_mix(outdir)


def generate_event(schema, outdir):
    template = env.get_template("elixir/event.ex")

    context = dict()
    context["module_name"] = module_name(schema.id)
    context["schema_module_name"] = schema_module_name(schema.id)
    context["definition"] = dump_json(schema)
    context["schema_uri"] = schema.id.value
    context["schema_id"] = schema.id.value
    context["type"] = utils.event_type(schema.id)

    rendered = template.render(context)

    utils.write_file(Path(outdir, file_name(schema.id)), rendered)


def generate_mix(outdir):
    template = env.get_template("elixir/mix.exs")

    repo = git.Repo(search_parent_directories=True)
    rev = repo.git.rev_parse(repo.rev_parse("origin/main"), short=8)

    context = dict()
    context["version"] = mix_version("1.0.0", rev)
    context["git_ref"] = rev

    rendered = template.render(context)
    utils.write_file(Path(outdir, "mix.exs"), rendered)


def module_name(id):
    id_parts = [casefy.pascalcase(p) for p in [id.namespace, id.event_name]]

    parts = ["EventSchemas"] + id_parts + [f"V{id.major}"]

    return ".".join(parts)


def schema_module_name(id):
    return ".".join([module_name(id), "Schema"])


def file_name(id):
    parts = ["lib", "event_schemas", id.namespace, id.event_name, f"v{id.major}.ex"]

    return "/".join(parts)


def mix_version(base, ref):
    return base + "+" + ref


def dump_json(schema):
    return json.dumps(schema.definition, indent=2).replace("\\", "\\\\")
