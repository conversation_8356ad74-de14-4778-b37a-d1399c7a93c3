apiVersion: multiverse.io/v1alpha1
kind: Event
metadata:
  name: {{ type }}
  annotations:
    backstage.io/source-location: url:https://github.com/Multiverse-io/event-schemas/blob/main/schemas/{{ namespace }}/{{ event_name }}/v{{ major }}.yaml
  {% if description != None %}
  description: |
    {{ description | indent(4) }}
  {% endif %}
spec:
  type: jsonschema
  lifecycle: {{ lifecycle }}
  definition: |
    {{ definition | indent(4) }}
