# This file was generated from {{ type }}

defmodule {{schema_module_name}} do
  defmacro json do
    quote do
      """
      {{ definition | indent(6)}}
      """
    end
  end
end

defmodule {{module_name}} do
  require Exonerate
  require {{schema_module_name}}

  @schema Jason.decode!({{schema_module_name}}.json())

  def type do
    "{{ type }}"
  end

  Exonerate.function_from_string(:def, :validate, {{schema_module_name}}.json(), format: :default)

  @doc """
  Expects a map containing a valid `{{ type }}` event.

  Options:
  - `source` - override the cloudevent source field. Defaults to global config value
  - `subject` - the cloudevent subject field
  - `time` - the cloudevent time field
  """
  def encode!(payload, opts \\ []) do
    payload = EventsCore.Util.atom_keys_to_string(payload)

    with :ok <- EventsCore.Validate.strict_validate_keys(payload, @schema),
         :ok <- validate(payload) do
    else
      {:error, error} ->
        raise "Payload invalid: #{inspect(error)}"
    end

    source = Keyword.get(opts, :source) || EventsCore.get_source!()

    EventsCore.CloudEvents.encode!(
      source,
      "{{ type }}",
      "{{ schema_uri }}",
      payload,
      Keyword.take(opts, [:subject, :time])
    )
  end

  @doc """
  Expects a map containing a `{{ type }}` event.
  Should be called with the result of `EventsCore.CloudEvents.decode!/1`.

  Options:
  - `envelope` - whether to unwrap the data from the envelope. Defaults to true
  """
  def decode!(data, opts \\ []) when is_map(data) do
    payload =
      if Keyword.get(opts, :envelope, true) do
        envelope = data

        if envelope.type != "{{ type }}" do
          raise """
          Attempted to decode an event as {{ type }} but found event of type #{envelope.type}
          id: #{envelope.type}
          source: #{envelope.source}
          type: #{envelope.type}
          schema: #{envelope.dataschema}
          """
        end

        envelope.data
      else
        data
      end

    case validate(payload) do
      :ok ->
        :ok

      {:error, error} ->
        # Error value could contain PII
        cleaned_error = Keyword.delete(error, :error_value)

        raise """
        Failed to validate event of type {{ type }}
        Details: #{inspect(cleaned_error)}
        """
    end

    # TODO remove unknown keys before returning
    EventsCore.Util.string_keys_to_atom(payload)
  end
end
