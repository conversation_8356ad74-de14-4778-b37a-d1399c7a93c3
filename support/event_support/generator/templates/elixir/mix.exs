defmodule EventSchemas.MixProject do
  use Mix.Project

  def project do
    [
      app: :event_schemas,
      version: "{{ version }}",
      elixir: "~> 1.14",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  def application do
    [
      extra_applications: [:logger]
    ]
  end

  defp deps do
    [
      {:exonerate, "~> 1.1"},
      {:pegasus, "~> 0.2.2"},
      {:events_core,
       git: "**************:Multiverse-io/event-schemas",
       ref: "{{ git_ref }}",
       sparse: "support/elixir/events_core"}
    ]
  end
end
