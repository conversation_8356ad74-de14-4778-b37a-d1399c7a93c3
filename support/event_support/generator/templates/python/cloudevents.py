from typing import Optional, Any
from datetime import datetime, timezone
import typing
import uuid

from pydantic import BaseModel, Field, field_validator


class CloudEventSchema(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    source: str
    specversion: str = '1.0'
    type: str
    datacontenttype: str = 'application/json'
    dataschema: str
    data: Any
    subject: Optional[str] = None
    time: Optional[datetime] = Field(default_factory=lambda: datetime.now(timezone.utc))

    @field_validator('time')
    def set_time_now(cls, v: datetime | None) -> datetime:
        return v or datetime.now(timezone.utc)


class EventBase(typing.Protocol):

    class Meta(typing.Protocol):
        schema_type: str
        schema_uri: str
        non_nullable_optionals: dict
        raw_json_schema: dict


def encode_cloudevent(
    event: EventBase,
    source: str,
    time: datetime=None,
    subject: str=None,
) -> CloudEventSchema:
    return CloudEventSchema(
        type=event.Meta.schema_type,
        dataschema=event.Meta.schema_uri,
        source=source,
        data=event,
        subject=subject,
        time=time,
    )
