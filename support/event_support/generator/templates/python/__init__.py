from datetime import datetime

from {{ package_name }} import CloudEventSchema
from .{{ output_file }} import {{ event_type }}

def encode_cloudevent(
    event: {{ event_type }},
    source: str,
    time: datetime=None,
    subject: str=None,
) -> CloudEventSchema:
    return CloudEventSchema(
        type={{ schema_type }},
        dataschema={{ schema_uri }},
        source=source,
        data=event,
        subject=subject,
        time=time,
    )
