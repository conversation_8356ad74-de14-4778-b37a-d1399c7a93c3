import { z } from "zod";

{% for event_name, versioned_attrs in event_map.items() %}{% for version, attrs in versioned_attrs.items() %}import {{attrs["schema_name"]}} from "{{attrs["schema_import_path"]}}";
{% endfor %}{% endfor %}

/*
A utility type listing all the available event names as a union of string literals types.
*/
type EventVersions = { {% for event_name, versioned_attrs in event_map.items() %}
  "{{ event_name }}": "{{ versioned_attrs.keys()|join('" | "') }}",{% endfor %}};

/**
 * A utility type listing all the available event schemas as a map of full event name to schema type.
 */
// The type here is explicitly unrolled via code generators so as to prevent type checker needing to do this a type check time
// This allows explicitly typing EVENT_SCHEMAS_MAP, which is done to prevent type serialisation errors, since the type checker
// no longer needs to do this at runtime and the depth of the type serialisation is greatly reduced to just EventSchemasMap (this is what you'll see on hover)
type EventSchemasMap = {
  {% for event_name, versioned_attrs in event_map.items() %}{% for version, attrs in versioned_attrs.items() %}"{{attrs["full_event_name"]}}": typeof {{attrs["schema_name"]}},
{% endfor %}{% endfor %}};

// The type here is explicitly unrolled via code generators so as to prevent type checker needing to do this a type check time
// This greatly reduces the type inference complexity, speeding up the type checker at runtime and preventing excessive complexity errors
type EventPayloadsMap = {
  {% for event_name, versioned_attrs in event_map.items() %}{% for version, attrs in versioned_attrs.items() %}"{{attrs["full_event_name"]}}": z.infer<typeof {{attrs["schema_name"]}}>,
{% endfor %}{% endfor %}};

/**
 * A constant map of full event name to Zod schema.
 * You probably don't want to use this directly, but instead use the `eventSchema` utility function to get a specific schema.
 */
const EVENT_SCHEMAS_MAP: EventSchemasMap = {
  {% for event_name, versioned_attrs in event_map.items() %}{% for version, attrs in versioned_attrs.items() %}"{{attrs["full_event_name"]}}": {{attrs["schema_name"]}},
{% endfor %}{% endfor %}} as const;

/**
 * A constant map of full event name to routing key.
 * You probably don't want to use this directly, but instead use the `routingKey` utility function to get a specific routing key.
 */
const ROUTING_KEYS_MAP = {
  {% for event_name, versioned_attrs in event_map.items() %}{% for version, attrs in versioned_attrs.items() %}"{{attrs["full_event_name"]}}": "{{attrs["routing_key"]}}",
{% endfor %}{% endfor %}} as const;

/**
 * A constant map of full event name to schema URI.
 * You probably don't want to use this directly, but instead use the `schemaURI` utility function to get a specific schema URI.
 */
const SCHEMA_URIS_MAP = {
  {% for event_name, versioned_attrs in event_map.items() %}{% for version, attrs in versioned_attrs.items() %}"{{attrs["full_event_name"]}}": "{{attrs["schema_uri"]}}",
{% endfor %}{% endfor %}} as const;

/**
 * A constant map of full event name to full version string (e.g. "1.2.9").
 * You probably don't want to use this directly, but instead use the `fullVersion` utility function to get a specific full version string.
 */
const FULL_VERSIONS_MAP = {
  {% for event_name, versioned_attrs in event_map.items() %}{% for version, attrs in versioned_attrs.items() %}"{{attrs["full_event_name"]}}": "{{attrs["full_version"]}}",
{% endfor %}{% endfor %}} as const;

/**
 * A utility type listing all the available event names as a union of string literals types.
 */
export type EventName = keyof EventVersions;

/**
 * A utility type listing all the available full event names as a union of string literals types.
 */
export type FullEventNames = keyof EventSchemasMap
  // Ensure the full event name is in the expected format
  & `multiverse.${string}.${string}.${string}`;

type SplitFullEventName<FullEventName extends FullEventNames> =
  // Ensure full name matches expected format and extract the namespace, name and version
  FullEventName extends `multiverse.${infer Namespace}.${infer Name}.${infer Version}`
    // Ensure the namespace and name together form a valid event name
    ? `${Namespace}.${Name}` extends EventName
      // Ensure the version given is valid for the event
      ? Version extends EventVersion<`${Namespace}.${Name}`>
        // Return the namespace, name and version as an array
        ? [Namespace, Name, Version]
        : never
      : never
    : never;

type GetEventNameVersion<FullEventName extends FullEventNames> =
  // Ensure full name matches expected format and extract the namespace, name and version
  FullEventName extends `multiverse.${infer Namespace}.${infer Name}.${infer Version}`
    // Ensure the namespace and name together form a valid event name
    ? `${Namespace}.${Name}` extends EventName
      // Return the namespace, name and version as an array
      ? [`${Namespace}.${Name}`, Version]
      : never
    : never;
/**
 * A utility type listing all the available versions of a specific event as a union of string literals types.
 */
export type EventVersion<Event extends EventName> = EventVersions[Event];

/**
 * A utility type listing only event names that represent changelog events (no business events).
 *
 * These are defined as any event whose `EventCategory` is `"changelog"`.
 * The `ChangelogEventName` type represents a subset of the `EventName` type.
 */
export type ChangelogEventName = {
  [E in FullEventNames]: SplitFullEventName<E> extends [string, "changelog", string] ? GetEventNameVersion<E>[0] : never;
}[FullEventNames];

/**
 * A utility type listing only event names that represent business events (no changelog events).
 *
 * These are defined as any event whose `EventCategory` is `"event"`.
 * The `BusinessEventName` type represents a subset of the `EventName` type.
 */
export type BusinessEventName = {
  [E in FullEventNames]: SplitFullEventName<E> extends [string, "changelog", string] ? never : GetEventNameVersion<E>[0];
}[FullEventNames];

/**
 * A utility type to get the full event name of a specific version of a specific event.
 *
 * @example
 * type AccountChangelogFullEventName = FullEventName<"account.changelog">;
 */
export type FullEventName<
  Event extends EventName,
  Version extends EventVersion<Event>
> = Event extends `${infer Namespace}.${infer Name}`
  ? `multiverse.${Namespace}.${Name}.${Version}` extends FullEventNames
    ? `multiverse.${Namespace}.${Name}.${Version}`
    : never
  : never;

/**
 * A utility type for extracting the schema type of a specific version of a specific event.
 *
 * @example
 * type AccountChangelogV1Schema = EventSchema<"account.changelog", "v1">;
 */
export type EventSchema<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = typeof EVENT_SCHEMAS_MAP[FullEventName<Event, Version>];

/**
 * A utility type for extracting the payload type of a specific version of a specific event.
 *
 * @example
 * type AccountChangelogV1Payload = EventPayload<"account.changelog", "v1">;
 */
export type EventPayload<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = EventPayloadsMap[FullEventName<Event, Version>];
/**
 * A utility type to get the routing key of a specific version of a specific event.
 */
export type RoutingKey<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = typeof ROUTING_KEYS_MAP[FullEventName<Event, Version>];

/**
 * A utility type to get the full version string of a specific version of a specific event.
 *
 * @example
 * type AccountChangelogFullEventName = FullVersion<"account.changelog", "v1">;
 */
export type FullVersion<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = typeof FULL_VERSIONS_MAP[FullEventName<Event, Version>];

/**
 * A utility type to get the namespace of a specific version of a specific event.
 *
 * @example
 * type AccountChangelogNamespace = Namespace<"account.changelog", "v1">;
 */
export type Namespace<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = SplitFullEventName<FullEventName<Event, Version>>[0];

/**
 * A utility type to get the SchemaURI of a specific version of a specific event.
 *
 * @example
 * type AccountChangelogUri = SchemaURI<"account.changelog", "v1">;
 */
export type SchemaURI<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = typeof SCHEMA_URIS_MAP[FullEventName<Event, Version>];

/**
 * A utility type to get the EventCategory of a specific version of a specific event.
 * Either "changelog" or "event"
 *
 * @example
 * // "changelog"
 * type ChangelogEvent = EventCategory<"account.changelog", "v1">;
 */
export type EventCategory<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = FullEventName<Event, Version> extends ChangelogEventName ? "changelog" : "event";

/**
 * Get the full event name for a specific version of a specific event.
 *
 * @example
 * const fullEventName = fullEventName("account.changelog", "v1");
 */
export function fullEventName<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(
  eventName: Event,
  version: Version
): FullEventName<Event, Version> {
  return `multiverse.${eventName}.${version}` as FullEventName<Event, Version>;
}

/**
 * Type guard to check if a string is a valid full event name.
 *
 * @example
 * const isFullEventName = isFullEventName("multiverse.account.changelog.v1");
 */
export function isFullEventName(name: string): name is FullEventNames {
  return EVENT_SCHEMAS_MAP[name as FullEventNames] !== undefined;
}

/**
 * Get the schema for a specific version of a specific event, given the full event name.
 *
 * @example
 * const schema = schema("account.changelog", "v1");
 */
export function eventSchemaFromFullName<FullName extends FullEventNames>(fullEventName: FullName): EventSchemasMap[FullName] {
  return EVENT_SCHEMAS_MAP[fullEventName];
}

/**
 * Represents metadata about an event, including its name, version, routing information, and schema.
 * This type ensures internal consistency using existing utility types.
 */
export type EventMeta = {
  [K in FullEventNames]: {
    eventName: GetEventNameVersion<K>[0];
    version: GetEventNameVersion<K>[1];
    fullEventName: K;
    routingKey: typeof ROUTING_KEYS_MAP[K];
    schemaURI: typeof SCHEMA_URIS_MAP[K];
    fullVersion: typeof FULL_VERSIONS_MAP[K];
    schema: EventSchemasMap[K];
  };
}[FullEventNames];

/**
 * A generator function to iterate over all available events, giving their name, version, full event name, routing key, schema URI, full version string, and schema.
 * This is is largely useful only for testing purposes.
 *
 * @example
 * for (const {eventName, version, fullEventName, routingKey, schemaURI, fullVersion, schema} of allEvents()) {
 *   console.log(eventName, version, fullEventName, routingKey, schemaURI, fullVersion, schema);
 * }
 */
export function* allEvents(): Generator<EventMeta> {
  for (const fullEventName of Object.keys(EVENT_SCHEMAS_MAP) as FullEventNames[]) {
    const [eventName, version] = toEventNameAndVersion(fullEventName);
    yield {
      eventName,
      version,
      fullEventName,
      routingKey: ROUTING_KEYS_MAP[fullEventName],
      schemaURI: SCHEMA_URIS_MAP[fullEventName],
      fullVersion: FULL_VERSIONS_MAP[fullEventName],
      schema: EVENT_SCHEMAS_MAP[fullEventName]
    } as EventMeta;
  }
}

/**
 * Get the schema for a specific version of a specific event.
 *
 * @example
 * const schema = schema("account.changelog", "v1");
 */
export function eventSchema<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(
  eventName: Event,
  version: Version
): EventSchema<Event, Version> {
  return EVENT_SCHEMAS_MAP[fullEventName(eventName, version)];
}

/**
 * Get the routing key for a specific version of a specific event.
 *
 * @example
 * const key = routingKey("account.changelog", "v1");
 */
export function routingKey<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(
  eventName: Event,
  version: Version
): RoutingKey<Event, Version> {
  return ROUTING_KEYS_MAP[fullEventName(eventName, version)];
}

/**
 * Get the full version string for a specific version of a specific event.
 *
 * @example
 * const fullVersion = fullVersion("account.changelog", "v1");
 */
export function fullVersion<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(
  eventName: Event,
  version: Version
): FullVersion<Event, Version> {
  return FULL_VERSIONS_MAP[fullEventName(eventName, version)];
}

function getFullNameParts<FEN extends FullEventNames>(fullEventName: FEN): SplitFullEventName<FEN> {
  const parts = fullEventName.split(".");
  return [parts[1], parts[2], parts[3]] as SplitFullEventName<FEN>;
}

/**
 * Convert a full event name to an event name and version in a type safe manner.
 *
 * @example
 * const [eventName, version] = toEventNameAndVersion("multiverse.account.changelog.v1");
 * // ["account.changelog", "v1"]
 */
export function toEventNameAndVersion<FullName extends FullEventNames>(fullEventName: FullName): GetEventNameVersion<FullName> {
  const parts = getFullNameParts(fullEventName);
  return [`${parts[0]}.${parts[1]}`, parts[2]] as GetEventNameVersion<FullName>;
}


/**
 * Get the namespace for a specific version of a specific event.
 *
 * @example
 * const namespace = namespace("account.changelog", "v1");
 */
export function namespace<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(
  eventName: Event,
  version: Version
): Namespace<Event, Version> {
  return getFullNameParts(fullEventName(eventName, version))[0];
}

/**
 * Get the schema URI for a specific version of a specific event.
 *
 * @example
 * const schemaUri = schemaURI("account.changelog", "v1");
 */
export function schemaURI<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(
  eventName: Event,
  version: Version
): SchemaURI<Event, Version> {
  return SCHEMA_URIS_MAP[fullEventName(eventName, version)];
}

/**
 * Get the event type for a specific version of a specific event.
 * "changelog" or "event"
 *
 * @example
 * const changelog = eventCategory("account.changelog");
 */
export function eventCategory<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(
  eventName: Event,
  version: Version
): EventCategory<Event, Version> {
  return getFullNameParts(fullEventName(eventName, version))[1] as EventCategory<Event, Version>;
}

/**
 * A utility function to generate a CloudEvent schema for a specific version of a specific event.
 *
 * @example
 * const accountChangelogV1Schema = cloudEventSchema("account.changelog", "v1");
 */
export function cloudEventSchema<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(eventName: Event, version: Version) {
  return z.object({
    type: z
      .literal(fullEventName(eventName, version))
      .describe(
        "Describes the type of event related to the originating occurrence."
      ),
    dataschema: z
      .string()
      .describe("Identifies the schema that data adheres to."),
    data: eventSchema(eventName, version),
    datacontenttype: z
      .literal("application/json")
      .describe("The content type of the data."),
    id: z
      .string()
      .uuid()
      .describe(
        "Identifies the event. `source` + `id` must be unique for each event."
      ),
    source: z
      .string()
      .min(1)
      .describe("Identifies the context in which an event happened via a URI."),
    specversion: z
      .literal("1.0")
      .describe("CloudEvents specification version."),
    subject: z
      .string()
      .min(1)
      .nullish()
      .describe("Additional metadata about the event."),
    time: z
      .string()
      .datetime()
      .nullish()
      .describe("Timestamp of when the event happened."),
  });
}

/**
 * A schema for decoding any generic CloudEvent - use cloudEventSchema for a more detailed check and better type safety if you have the name and version.
 */
export const genericCloudEventSchema = z.object({
  type: z
    .string()
    .describe(
      "Describes the type of event related to the originating occurrence."
    ),
  dataschema: z
    .string()
    .describe("Identifies the schema that data adheres to."),
  // Allow any object, and ensure it is not stripped on parse
  data: z.record(z.any()),
  datacontenttype: z
    .literal("application/json")
    .describe("The content type of the data."),
  id: z
    .string()
    .uuid()
    .describe(
      "Identifies the event. `source` + `id` must be unique for each event."
    ),
  source: z
    .string()
    .min(1)
    .describe("Identifies the context in which an event happened via a URI."),
  specversion: z
    .literal("1.0")
    .describe("CloudEvents specification version."),
  subject: z
    .string()
    .min(1)
    .nullish()
    .describe("Additional metadata about the event."),
  time: z
    .string()
    .datetime()
    .nullish()
    .describe("Timestamp of when the event happened."),
});

/**
 * A utility type for extracting the CloudEvent schema type of a specific version of a specific event.
 * The CloudEvent schema is a wrapper around the event payload schema.
 *
 * @example
 * type AccountChangelogV1CloudEvent = CloudEventSchema<"account.changelog", "v1">;
 */
export type CloudEventSchema<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = ReturnType<typeof cloudEventSchema<Event, Version>>;

/**
 * This type represents the shape of any valid CloudEvent message.
 */
export type CloudEventMessage = z.infer<typeof genericCloudEventSchema>;

/**
 * A utility function to generate an EventBridge schema for a specific version of a specific event.
 * The EventBridge schema is a wrapper around the CloudEvent schema, which is in turn a wrapper around the event payload schema.
 *
 * @example
 * const accountChangelogV1EventBridge = eventBridgeSchema("account.changelog", "v1");
 */
export function eventBridgeSchema<
  Event extends EventName,
  Version extends EventVersion<Event>,
>(eventName: Event, version: Version) {
  return z.object({
    "detail-type": z.string().describe("The routing key that the event was published to"),
    detail: z.object({
      body: cloudEventSchema(eventName, version),
    }),
  });
}

/**
 * A schema for decoding any generic EventBridge message - use eventBridgeSchema for a more detailed check and better type safety if you have the name and version.
 */
export const genericEventBridgeSchema = z.object({
  "detail-type": z.string().describe("The routing key that the event was published to"),
  detail: z.object({
    body: genericCloudEventSchema,
  }),
});

/**
 * A utility type for extracting the EventBridge schema type of a specific version of a specific event.
 * The EventBridge schema is a wrapper around the CloudEvent schema, which is in turn a wrapper around the event payload schema.
 *
 * @example
 * type AccountChangelogV1EventBridge = EventBridgeSchema<"account.changelog", "v1">;
 */
export type EventBridgeSchema<
  Event extends EventName,
  Version extends EventVersion<Event>,
> = ReturnType<typeof eventBridgeSchema<Event, Version>>

/**
 * This type represents the shape of any valid EventBridge message.
 */
export type EventBridgeMessage = z.infer<typeof genericEventBridgeSchema>;
