{"name": "@multiverse-io/event-schemas-ts", "version": "{{ version }}", "publishConfig": {"@multiverse-io:registry": "https://npm.pkg.github.com"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "author": "Multiverse-io", "license": "ISC", "packageManager": "yarn@4.1.1", "scripts": {"build": "rm -rf dist/ && tsc --build", "clean": "tsc --build --clean"}, "dependencies": {"@types/uuid": "9.0.8", "uuid": "9.0.1", "zod": "3.22.4"}, "devDependencies": {"typescript": "5.4.3"}}