from jinja2 import Environment, PackageLoader, select_autoescape, StrictUndefined
from datetime import datetime, timezone
from pathlib import Path
import casefy
import json
import glob
import git
import event_support.generator.utils as utils
from event_support.schema import Schema
import yaml

env = Environment(
    loader=PackageLoader("event_support.generator"),
    autoescape=select_autoescape(),
    undefined=StrictUndefined,
)


def generate(schema_dir, outdir):
    utils.empty_dir(outdir)

    schema_files = glob.glob(f"{schema_dir}/**/*.yaml", recursive=True)

    for file in schema_files:
        generate_event(Schema(path=file), outdir)


def generate_event(schema, outdir):
    template = env.get_template("backstage/catalog-info.yaml")

    context = dict()
    context["definition"] = schema.raw_definition
    context["type"] = utils.event_type(schema.id)
    context["description"] = schema.definition.get("description")
    context["namespace"] = schema.id.namespace
    context["event_name"] = schema.id.event_name
    context["major"] = schema.id.major
    context["lifecycle"] = lifecycle(schema)

    rendered = template.render(context)

    utils.write_file(Path(outdir, file_name(schema.id)), rendered)


def lifecycle(schema):
    current_version = schema.id.major

    schema_dir = Path(schema.path).parent
    other_schema_files = glob.glob(f"{schema_dir}/*.yaml")

    all_versions = sorted([int(Path(x).stem[1:]) for x in other_schema_files])

    if all_versions[-1] == current_version:
        if int(schema.id.major) == 0:
            return "experimental"
        else:
            return "latest"
    else:
        return "deprecated"


def file_name(id):
    parts = [id.namespace, id.event_name, f"{id.major}.yaml"]

    return "/".join(parts)
