from collections.abc import Callable
import glob
from pathlib import Path
from typing import TypeAlias

import casefy
import event_support.generator.utils as utils
import git
from datamodel_code_generator import DataModelType, InputFileType
from datamodel_code_generator import generate as generate_datamodel
from event_support.id import Id
from event_support.schema import Schema
from jinja2 import Environment, PackageLoader, StrictUndefined, select_autoescape
from functools import reduce

env = Environment(
    loader=PackageLoader("event_support.generator"),
    autoescape=select_autoescape(),
    undefined=StrictUndefined,
)

PACKAGE_NAME = {"schemas": "event_schemas", "test-schemas": "event_test_schemas"}
INIT_TEMPLATE = env.get_template("python/__init__.py")
CLOUDEVENTS_TEMPLATE = env.get_template("python/cloudevents.py")
PYPROJECT_TEMPLATE = env.get_template("python/pyproject.toml")

JSON_DATA_TYPES = set(["string", "number", "boolean", "object", "array", "null"])


def extract_types(schema: dict) -> set[str]:
    """
    Extracts type keywords from a JSON schema.
    """

    def parse(_type: str | list[str]) -> set[str]:
        match _type:
            case list():
                return set(_type)
            case str():
                return set([_type])
            case _:
                return set()

    def subschemas(types: set[str], subschema: dict):
        return types | extract_types(subschema)

    types = (
        parse(schema.get("type", []))
        | reduce(subschemas, schema.get("allOf", []), set())
        | reduce(subschemas, schema.get("oneOf", []), set())
        | reduce(subschemas, schema.get("anyOf", []), set())
    ) or JSON_DATA_TYPES

    return types - reduce(subschemas, schema.get("not", []), set())


def is_nullable(prop: dict) -> bool:
    """
    Checks if a JSON schema property allows null values.

    Args:
        prop (dict): A dictionary representing a JSON schema property.

    Returns:
        bool: True if the property is nullable, False otherwise.
              A property is considered nullable if its 'type' is explicitly 'null',
              or if 'type' is a list containing 'null', or if the 'type' key is missing
              (which defaults to allowing any type, including null).
    """
    return "null" in extract_types(prop)


def find_non_nullable_optionals(schema: dict, required: set | None = None) -> dict:
    """
    Recursively finds all non-nullable, optional fields within a JSON schema.

    Args:
        schema (dict): A dictionary representing a JSON schema.
        required (set, optional): A set of field names that are currently considered required
                                   in the current scope. Defaults to an empty set.

    Returns:
        dict: A dictionary where keys are the names of non-nullable optional fields,
              and values are either True (for simple properties) or a nested dictionary
              representing non-nullable optionals within complex properties (objects or arrays).
    """
    optional = {}

    if required is None:
        required = set()

    # Update the set of required fields with any 'required' fields defined in the current schema.
    required = required | set(schema.get("required", {}))

    # A TypeAlias for the output (required, optional) of processing subschemas.
    S: TypeAlias = tuple[set[str], dict]

    def process_subschema(subschema: dict) -> S:
        """
        Returns:
            tuple[set, dict]: A tuple containing a set of required fields and a dictionary
                             of non-nullable optional fields found in the subschema.
        """
        return (
            set(subschema.get("required", {})),
            find_non_nullable_optionals(subschema, required),
        )

    # Reducer function for 'allOf' keyword. It merges the required sets and optional dictionaries from all subschemas.
    def union(x: S, y: S) -> S:
        return (x[0] | y[0], x[1] | y[1])

    # Reducer function for 'anyOf' and 'oneOf' keywords. It intersects the required sets and merges
    # the optional dictionaries from all subschemas.
    def intersect(x: S, y: S) -> S:
        return (x[0] & y[0], x[1] | y[1])

    # A TypeAlias for functions reducing the outputs of subschema processing
    Reducer: TypeAlias = Callable[[S, S], S]

    def process_subschemas(reducer: Reducer, subschemas: list[dict]) -> S:
        sub_required, sub_optional = reduce(reducer, map(process_subschema, subschemas))
        return (required | sub_required, optional | sub_optional)

    # Handle 'allOf' keyword: all subschemas must be valid.
    match schema:
        case {"allOf": subschemas} if len(subschemas) > 0:
            required, optional = process_subschemas(union, subschemas)

    # Handle 'anyOf' keyword: at least one subschema must be valid.
    match schema:
        case {"anyOf": subschemas} if len(subschemas) > 0:
            required, optional = process_subschemas(intersect, subschemas)

    # Handle 'oneOf' keyword: exactly one subschema must be valid.
    match schema:
        case {"oneOf": subschemas} if len(subschemas) > 0:
            required, optional = process_subschemas(intersect, subschemas)

    # Helper function to handle complex properties (objects and arrays).
    def handle_complex(key: str, value: dict) -> dict:
        """
        Handles complex properties (objects or arrays) by recursively finding
        non-nullable optionals within their definitions.

        Args:
            k (str): The key (name) of the property.
            item (dict): The schema definition of the property's items (for arrays) or the property itself (for objects).

        Returns:
            dict: A dictionary containing the non-nullable optionals found within the complex property,
                  or a dictionary indicating the property itself is a non-nullable optional if no
                  nested non-nullable optionals are found and the property is not required.
        """
        nested = find_non_nullable_optionals(value)
        # If nested non-nullable optionals are found, return them under the current key.
        if nested != {}:
            return {key: nested}
        # If no nested optionals and the current property is not required, mark it as a non-nullable optional.
        elif key not in required:
            return {key: True}
        # Otherwise, the property is either required or has no nested non-nullable optionals.
        else:
            return {}

    # Iterate through the properties defined in the current schema.
    for k, prop in schema.get("properties", {}).items():
        match prop:
            # If the property is an array, process its items.
            case {"type": "array"}:
                optional.update(handle_complex(k, prop["items"]))
            # If the property is an object, process it recursively.
            case {"type": "object"}:
                optional.update(handle_complex(k, prop))
            # If the property is not required and is not nullable, add it to the optional dictionary.
            case _ if k not in required and not is_nullable(prop):
                optional.update({k: True})

    return optional


def pydantic_model_name(id: Id) -> str:
    return f"{casefy.pascalcase(id.event_name)}Schema"


def schema_output_dir(id: Id) -> Path:
    return Path(
        *[
            id.namespace,
            id.event_name,
            f"v{id.major}",
        ]
    )


def generate_cloudevent_schema(outdir: Path, package_name: str) -> None:
    utils.write_file(
        outdir / package_name / "__init__.py",
        CLOUDEVENTS_TEMPLATE.render(),
    )


def get_version() -> str:
    repo = git.Repo(search_parent_directories=True)
    rev = repo.git.rev_parse(repo.rev_parse("origin/main"), short=8)
    return f'"1.0.1+{rev}"'


def generate_pyproject_toml(outdir: Path, package_name: str) -> None:
    utils.write_file(
        outdir / "pyproject.toml",
        PYPROJECT_TEMPLATE.render(
            {
                "version": get_version(),
                "package_name": package_name,
            }
        ),
    )


def generate_py_typed(outdir: Path, package_name: str) -> None:
    """Generate a py.typed file to indicate that the package contains type hints."""
    return (outdir / package_name / "py.typed").touch()


def generate_meta_class_content(schema: Schema) -> str:
    """Generate the Meta class content for a schema."""
    non_nullable_optionals = find_non_nullable_optionals(schema=schema.definition)
    return f"""
    class Meta:
        schema_type = {utils.event_type(schema.id)!r}
        schema_uri = {schema.id.value!r}
        non_nullable_optionals = {non_nullable_optionals!r}
        raw_json_schema = {schema.definition}
"""


def generate_pydantic_models(schema: Schema, outdir: Path, package_name: str) -> Path:
    """Generate a Pydantic model from a JSON schema."""
    output = outdir / package_name / schema_output_dir(schema.id)
    output.mkdir(parents=True, exist_ok=True)
    output_file = output / f"{schema.id.event_name}.py"

    generate_datamodel(
        Path(schema.path),
        input_file_type=InputFileType.JsonSchema,
        output=output_file,
        output_model_type=DataModelType.PydanticV2BaseModel,
        class_name=pydantic_model_name(schema.id),
        use_schema_description=True,
    )

    # We want to add the 'Meta' class below to generated model. It would be
    # better to use generate_datamodel, but despite its plethora of arguments it
    # doesn't appear to be possible to add anything to the body of the generated
    # model, or even to add a base class to it.
    #
    # So instead we just append the class to the end of the file with the
    # correct indentation so that it is embedded in the generated class.  This
    # relies on the schema model being the last class in the file and the
    # indentation being 4 spaces.

    with output_file.open("a") as f:
        f.write(generate_meta_class_content(schema))

    return output_file


def generate_schema(file: str, outdir: Path, package_name: str) -> None:
    schema = Schema(path=file)
    output_file = generate_pydantic_models(schema, outdir, package_name)
    utils.write_file(
        output_file.parent / "__init__.py",
        INIT_TEMPLATE.render(
            {
                "output_file": f"{schema.id.event_name}",
                "event_type": pydantic_model_name(schema.id),
                "schema_type": f'"{utils.event_type(schema.id)}"',
                "schema_uri": f'"{schema.id.value}"',
                "package_name": package_name,
            }
        ),
    )


def generate(
    schema_dir: str,
    outdir: str,
) -> None:
    """Iterate over all json schema files and generate Pydantic models,
    CloudEvent model and pyproject.toml."""
    utils.empty_dir(outdir)
    outdir = Path(outdir)

    schemas_name = Path(schema_dir).name
    package_name = PACKAGE_NAME[schemas_name]
    schema_files = glob.glob(f"{schema_dir}/**/*.yaml", recursive=True)
    for file in schema_files:
        generate_schema(file, outdir, package_name)

    generate_cloudevent_schema(outdir, package_name)
    generate_pyproject_toml(outdir, package_name)
    generate_py_typed(outdir, package_name)
