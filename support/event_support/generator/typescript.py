from jinja2 import Environment, PackageLoader, select_autoescape, StrictUndefined
from datetime import datetime, timezone
from pathlib import Path
import casefy
import json
import glob
import git
import shutil
import subprocess
import event_support.generator.utils as utils
import asyncio
import sys
import re
from event_support.schema import Schema, collect_versioned_schemas

env = Environment(
    loader=PackageLoader("event_support.generator"),
    autoescape=select_autoescape(),
    undefined=StrictUndefined,
)


# Use this to specify the minimum version of the package that should be generated.
# This is useful when you want to not just have the patch version automatically bumped, but
#   manually specify a new minor or major version.
min_new_package_version = "4.0.0"

# The way a new package version is determined is as follows:
# 1. The current version is determined by looking at the package.json file in the latest pushed typescript branch.
# 2. The patch version is bumped by 1.
# 3. Check this against the min_new_package_version above, and use the latest of the two.


def latest_git_package_version(repo):
    try:
        # Ensure we have the latest typescript branch from the remote
        repo.git.fetch("origin", "typescript")

        # Get the latest version of the package.json file from the typescript branch
        package_json_content = repo.git.show("origin/typescript:package.json")
        match = re.search(
            # Regex to extract the version from the package.json file, allowing for optional `+<rev>` etc at the end
            r'"version":\s*"(\d+\.\d+\.\d+).*"',
            package_json_content,
        )
        if match:
            return match.group(1)
        else:
            raise ValueError("Version not found in package.json")
    except git.exc.GitCommandError as e:
        raise RuntimeError(
            f"Error: Unable to retrieve the latest git package version: {str(e)}"
        )


def get_next_version(repo):
    try:
        current_version = latest_git_package_version(repo)

        # Split the current version string into 3 parts
        major, minor, patch = map(int, current_version.split("."))

        # Bump the patch version
        bumped_version = f"{major}.{minor}.{patch + 1}"

        # Compare bumped_version with min_new_package_version, taking the latest of the two
        def version_tuple(v):
            return tuple(map(int, v.split(".")))

        if version_tuple(bumped_version) > version_tuple(min_new_package_version):
            return bumped_version
        else:
            return min_new_package_version
    except Exception as e:
        print(f"Error: Failed to determine the next version: {str(e)}")
        sys.exit(1)


def version_context():
    try:
        repo = git.Repo(search_parent_directories=True)
        return {
            "version": get_next_version(repo),
        }
    except Exception as e:
        print(f"Error: Failed to create version context: {str(e)}")
        sys.exit(1)


def schema_context(schema):
    return {
        "zod_schema_name": zod_schema_name(schema.id),
        "type_name": typescript_type_name(schema.id),
        "schema_type": utils.event_type(schema.id),
        "schema_uri": schema.id.value,
    }


def generate(schema_dir, outdir):
    utils.empty_dir(outdir)

    schema_files = glob.glob(f"{schema_dir}/**/*.yaml", recursive=True)

    schemas = utils.parallel_run(
        [generate_schema(file, outdir) for file in schema_files]
    )

    generate_package_json(outdir, version_context())
    copy_static_config_files(outdir)

    generate_package_index_file(schemas)


def generate_package_index_file(schemas: list[Schema]):
    def event_name(schema: Schema, include_version, include_org):
        return ".".join(
            (["multiverse"] if include_org else [])
            + [schema.id.namespace, schema.id.event_name]
            + ([f"v{schema.id.major}"] if include_version else [])
        )

    def schema_attrs(schema: Schema):
        return {
            "schema_import_path": zod_schema_import_path(schema.id),
            "event_name": event_name(schema, include_version=False, include_org=False),
            "full_event_name": event_name(
                schema, include_version=True, include_org=True
            ),
            "routing_key": event_name(schema, include_version=False, include_org=False),
            "schema_uri": schema.id.value,
            "namespace": schema.id.namespace,
            "version": f"v{schema.id.major}",
            "full_version": f"{schema.id.major}.{schema.id.minor}.{schema.id.patch}",
            "schema_name": f"{casefy.camelcase(schema.id.namespace)}{casefy.pascalcase(schema.id.event_name)}V{schema.id.major}Schema",
            "event_category": (
                "changelog" if schema.id.event_name.lower() == "changelog" else "event"
            ),
        }

    event_map = {
        event_name: {
            version: schema_attrs(schema)
            for version, schema in versioned_schemas.items()
        }
        for event_name, versioned_schemas in collect_versioned_schemas(schemas).items()
    }

    template_context = {
        "event_map": event_map,
    }

    template = env.get_template("typescript/packageIndex.ts")
    rendered = template.render(template_context)

    utils.write_file(Path("output/typescript", "lib", "index.ts"), rendered)


@utils.background
def generate_schema(file, outdir):
    schema = Schema(path=file)
    generate_zod_schema(schema, outdir)
    generate_index_file(schema, outdir, schema_context(schema))
    return schema


def generate_index_file(schema, outdir, context):
    template = env.get_template("typescript/index.ts")
    rendered = template.render(context)
    utils.write_file(Path(outdir, index_file_name(schema.id)), rendered)


def generate_zod_schema(schema, outdir):
    schema_json = json.dumps(schema.definition, indent=2)

    try:
        formatted_output = subprocess.run(
            ["node", "zodgen.js"],
            input=schema_json,
            text=True,
            capture_output=True,
            check=True,
        ).stdout
    except subprocess.CalledProcessError as e:
        sys.exit(f"Error calling json-schema-to-zod or prettier: {e}")

    utils.write_file(Path(outdir, zod_schema_file_name(schema.id)), formatted_output)


def generate_package_json(outdir, context):
    template = env.get_template("typescript/package.json")
    utils.write_file(Path(outdir, "package.json"), template.render(context))


def copy_static_config_files(outdir):
    source_dir = Path(__file__).parent / "templates" / "typescript"
    outdir = Path(outdir)
    shutil.copy(source_dir / "tsconfig.json", outdir / "tsconfig.json")


def typescript_type_name(id):
    return f"{casefy.pascalcase(id.event_name)}"


def zod_schema_name(id):
    return f"{casefy.camelcase(id.event_name)}Schema"


def schema_output_dir(id):
    parts = ["lib", "events", id.namespace, id.event_name, f"v{id.major}"]
    return Path(*parts)


def zod_schema_file_name(id):
    return schema_output_dir(id) / f"{zod_schema_name(id)}.ts"


def zod_schema_import_path(id):
    return f"./{Path("events", id.namespace, id.event_name,
                f"v{id.major}", zod_schema_name(id)).as_posix()}"


def index_file_name(id):
    return schema_output_dir(id) / "index.ts"
