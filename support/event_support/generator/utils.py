import shutil
import os
import asyncio
from pathlib import Path


def empty_dir(dirname):
    """Empties a directory without deleting the directory itself."""
    out_dir = Path(dirname)

    # mkdir -p
    out_dir.mkdir(parents=True, exist_ok=True)

    for path in Path(dirname).iterdir():
        if path.is_dir():
            shutil.rmtree(path)
        elif path.is_file():
            path.unlink()


def write_file(filename, contents):
    dirname = os.path.dirname(filename)

    # mkdir -p
    Path(dirname).mkdir(parents=True, exist_ok=True)

    with open(filename, "w") as file:
        file.write(contents)


def event_type(id):
    parts = ["multiverse", id.namespace, id.event_name, f"v{id.major}"]

    return ".".join(parts)


def parallel_run(jobs):
    loop = asyncio.get_event_loop()
    looper = asyncio.gather(*jobs)
    return loop.run_until_complete(looper)


def background(f):
    def wrapped(*args, **kwargs):
        return asyncio.get_event_loop().run_in_executor(None, f, *args, **kwargs)

    return wrapped
