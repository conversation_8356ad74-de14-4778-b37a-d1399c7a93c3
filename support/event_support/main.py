import argparse
import sys
from pathlib import Path

import git
from termcolor import colored

import event_support.compatible as compatible
import event_support.generator.elixir as elixir
import event_support.generator.python as python
import event_support.generator.typescript as typescript
import event_support.generator.backstage as backstage
from event_support.schema import Schema


# DANGER: Please only update this list if you are certain the conditions below are met.
#         Allowing breaking changes to schemas runs the risk of breaking event consumers in hard to detect ways!

# This is only to be used in rare circumstances where:
#   - We are certain that no events have been (or will ever be) published under a given schema version (no publishers exist)
#   - We need to make breaking changes to a recently added schema, but don't want a dead schema major version
#   - We are certain that no consumers have been created using the old schema version - these would fail to consume the new events

ALLOW_BREAKING_CHANGES_TO_SCHEMAS = [
    # accidentally published a v1.0.0 of this schema whilst still in development. will remove in next PR
    "apprenticeship.training_plan_completed.v1.0.0",
]


def main():
    parser = argparse.ArgumentParser(description="Events support tool")
    subparsers = parser.add_subparsers(dest="mode", required=True)

    parser_generate = subparsers.add_parser(
        "generate", help="Generate event code for a given language"
    )
    parser_generate.add_argument(
        "lang", choices=["elixir", "python", "python-test", "typescript", "backstage"]
    )
    parser_generate.add_argument("schema_dir", help="Path to the schema directory")

    parser_validate = subparsers.add_parser("validate", help="Validate event schemas")
    parser_validate.add_argument("schemas", help="Path to a schema file", nargs="+")
    parser_validate.add_argument(
        "--diff", dest="git_ref", help="The git ref to compare to (eg main)"
    )

    args = parser.parse_args()

    match args.mode:
        case "generate":
            do_generate(args)
        case "validate":
            do_validate(args)


def do_generate(args):
    match args.lang:
        case "elixir":
            elixir.generate(args.schema_dir, "./output/elixir")

        case "python":
            python.generate(args.schema_dir, "./output/python")

        case "python-test":
            python.generate(args.schema_dir, "./output/python-test")

        case "typescript":
            typescript.generate(args.schema_dir, "./output/typescript")

        case "backstage":
            backstage.generate(args.schema_dir, "./output/backstage")


def breaking_changes_allowed(old_schema, new_schema):
    return (
        old_schema.id.allow_breaking_changes(new_schema.id)
        or old_schema.id.full_id() in ALLOW_BREAKING_CHANGES_TO_SCHEMAS
    )


def do_validate(args):
    if args.git_ref:
        for schema in args.schemas:
            print(f"validating changes to {schema}... ", end="")

            repo = git.Repo(search_parent_directories=True)
            schema_path = Path(schema).relative_to(repo.working_tree_dir)
            old_schema_contents = repo.git.show(f"{args.git_ref}:{schema_path}")

            schema_current = Schema(path=schema)
            schema_old = Schema(path=schema, code=old_schema_contents)

            if breaking_changes_allowed(schema_old, schema_current):
                schema_id = schema_old.id.full_id()
                print(f"Warning: Allowing breaking changes to {schema_id}")
                return

            try:
                compatible.check(schema_old.definition, schema_current.definition)

                if not schema_old.id.is_minor_bump(schema_current.id):
                    raise RuntimeError("expected minor version bump")

                print(colored("valid!", "green"))

            except RuntimeError as inst:
                print(colored("error!", "red"))
                sys.exit(colored(f"Failed to validate schema changes: {inst}", "red"))

    else:
        try:
            for schema in args.schemas:
                print(f"validating {schema}... ", end="")
                Schema(path=schema)
                print(colored("valid!", "green"))
        except RuntimeError as inst:
            print(colored("error!", "red"))
            sys.exit(colored(f"Failed to validate schema: {inst}", "red"))


if __name__ == "__main__":
    main()
