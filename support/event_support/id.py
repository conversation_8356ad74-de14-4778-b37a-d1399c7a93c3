import re
from pathlib import Path

id_regex = re.compile(
    r"^https://schema\.multiverse\.io/(?P<namespace>[a-z_]+)/(?P<event_name>[a-z_]+)/(?P<major>0|[1-9]\d*)\.(?P<minor>0|[1-9]\d*)\.(?P<patch>0|[1-9]\d*)$"
)


class Id:
    def __init__(self, value, path=None):
        matches = id_regex.match(value)

        if not matches:
            raise RuntimeError(
                f"Schema id invalid. Found '{value}' but was expecting something which looks like 'https://schema.multiverse.io/namespace/event_name/1.2.3'"
            )

        self.value = value
        self.namespace = matches["namespace"]
        self.event_name = matches["event_name"]
        self.major = int(matches["major"])
        self.minor = int(matches["minor"])
        self.patch = int(matches["patch"])

        if path:
            self.validate_matches_path(path)

    def __repr__(self):
        return f"<Id value:{self.value}>"

    def full_id(self):
        return f"{self.namespace}.{self.event_name}.v{self.major}.{self.minor}.{self.patch}"

    def validate_matches_path(self, path):
        p = Path(path)

        version = p.stem[1:]
        event = p.parent.name
        namespace = p.parent.parent.name

        if (
            self.namespace != namespace
            or self.event_name != event
            or self.major != int(version)
        ):
            raise RuntimeError("Id does not match filename")

    def is_minor_bump(self, other):
        return self.major == other.major and (
            self.minor < other.minor or self.patch < other.patch
        )

    def allow_breaking_changes(self, new_version):
        return (self.major == 0 and self.minor < new_version.minor) or (
            self.major < new_version.major
        )
