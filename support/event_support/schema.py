import yaml
import requests
import jsonschema
from pathlib import Path
import re
from event_support.id import Id
from typing import List

meta_schema_uri = "https://json-schema.org/draft/2020-12/schema"


meta_schema = requests.get(meta_schema_uri).json()


# Don't add to this list – it's only for schemas added before the check was introduced.
ALLOW_EXTRA_REQUIRED_PROPERTIES_SCHEMAS = [
    "competency_domain.changelog.v1.0.0",
    "salesforce_company.created.v0.1.0",
    "customer_manager.learner_unfollowed.v1.0.0",
    "project.changelog.v1.3.0",
    "apprenticeship.progress_review_submitted.v1.0.0",
]


def validate_additional_properties(object):
    if "additionalProperties" in object:
        raise RuntimeError(
            "additionalProperties should not be set as it limits backwards compatibility"
        )

    for key, value in object["properties"].items():
        if value["type"] == "object":
            validate_additional_properties(value)

    return True


def validate_required_properties(object):
    """
    Validate that required properties are listed in the properties section.
    """
    for required_property in object.get("required", []):
        if required_property not in object["properties"]:
            raise RuntimeError(
                f"required property {required_property} is not listed in the properties section"
            )

    for key, value in object["properties"].items():
        if value["type"] == "object":
            validate_required_properties(value)

    return True


class UniqueKeyValidatingLoader(yaml.SafeLoader):
    """
    Implementation of yaml.SafeLoader that disallows duplicate keys in mapping,
    as prescribed by the YAML spec ([1])

    [1] https://yaml.org/spec/1.2.2/#3211-nodes
    """

    def construct_mapping(self, node, deep=False):
        mapping = super().construct_mapping(node, deep)
        if len(mapping) < len(node.value):
            keys = set()
            for key_node, _ in node.value:
                if ":merge" in key_node.tag:
                    continue
                key = self.construct_object(key_node, deep=deep)
                if key in keys:
                    raise RuntimeError(f"Duplicate {key!r} key found in YAML.")
                keys.add(key)
        return mapping


class Schema:
    def __init__(self, path, code=None):
        self.raw_definition = ""
        if code:
            self.raw_definition = code
        else:
            self.raw_definition = Path(path).read_text()

        self.path = path
        self.definition = yaml.load(self.raw_definition, UniqueKeyValidatingLoader)

        # Check definition is a valid json schema
        jsonschema.validators.Draft202012Validator(meta_schema).validate(
            self.definition
        )

        self.id = Id(self.definition["$id"], self.path)

        self.validate_schema_version()
        validate_additional_properties(self.definition)
        if self.id.full_id() not in ALLOW_EXTRA_REQUIRED_PROPERTIES_SCHEMAS:
            validate_required_properties(self.definition)

    def __lt__(self, other):
        """
        Compare two schemas based on their version numbers.
        A schema is considered less than another if it has a lower major.minor.patch version number.
        """
        if not isinstance(other, Schema):
            return NotImplemented

        if self.id.major != other.id.major:
            return self.id.major < other.id.major
        elif self.id.minor != other.id.minor:
            return self.id.minor < other.id.minor
        else:
            return self.id.patch < other.id.patch

    def validate_schema_version(self):
        schema_version = self.definition["$schema"]

        if schema_version != meta_schema_uri:
            raise RuntimeError(
                f"$schema must be set to {
                    meta_schema_uri}. Found {schema_version}"
            )


def collect_versioned_schemas(schemas: List[Schema]) -> dict[str, dict[str, Schema]]:
    """
    Collects versioned schemas from a list of schemas.
    """

    schema_map = {}
    for schema in schemas:
        key = f"{schema.id.namespace}.{schema.id.event_name}"
        version_key = f"v{schema.id.major}"

        if key not in schema_map:
            schema_map[key] = {}

        schema_map[key][version_key] = schema

    return schema_map
