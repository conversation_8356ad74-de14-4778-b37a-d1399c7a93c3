def build_error(message):
    return RuntimeError(f"Detected backwards incompatible change\n{message}")


def check(old, new):
    if old["type"] != new["type"]:
        new_type = new["type"]
        old_type = old["type"]
        raise build_error(f"new type '{new_type}' doesn't match old type '{old_type}'")

    if old["type"] == "object":
        return check_object(old, new)
    elif old["type"] == "array":
        if old["items"]["type"] == "object":
            return check_object(old["items"], new["items"])
    elif "format" in old or "format" in new:
        if old["format"] != new["format"]:
            new_format = new["format"]
            old_format = old["format"]
            raise build_error(
                f"new format '{new_format}' doesn't match old format '{old_format}'"
            )

    return True


def check_object(old, new):
    old_required, old_optional = split_props(old)
    new_required, new_optional = split_props(new)

    if propkeys(old_required) != propkeys(new_required):
        raise build_error("required fields have been changed")

    if not propkeys(new_optional).issuperset(propkeys(old_optional)):
        raise build_error("optional fields have been removed")

    if "properties" in new:
        for prop in new["properties"].items():
            if "properties" in old and prop[0] in old["properties"]:
                check(old["properties"][prop[0]], prop[1])

    return True


def split_props(schema):
    required = []
    optional = []

    if "properties" in schema:
        for prop in schema["properties"].items():
            if prop[0] in schema.get("required", []):
                required.append(prop)
            else:
                optional.append(prop)

        return required, optional
    else:
        return [], []


def propkeys(props):
    return set(p[0] for p in props)
