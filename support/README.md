# event_support

This is a python app to generate code to help with encoding, decoding and validating event payloads.

The intention is for this to output code in:
- elixir
- python
- typescript

## Usage

First poetry and python need to be installed. This can be done with asdf.

``` bash
asdf plugin-add poetry
asdf plugin-add python
asdf install
```

Then install the python deps with:

``` bash
poetry install
```

Then enter the poetry shell with:

``` bash
poetry shell
```

The following commands will now be available in your shell:

- `./run` - run the tool. Use `./run --help` to see available options
- `make test` - run the tests
- `make format` - format the code using black
