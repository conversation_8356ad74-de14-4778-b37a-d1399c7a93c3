[tool.ruff]
target-version = "py312"

# Exclude a variety of commonly ignored directories.
exclude = [
  ".ruff_cache",
  ".mypy_cache",
  ".git",
  "__pycache__",
  "*/snap_*.py",
  ".venv*",
  "*.pyi",
]

[tool.ruff.lint]
select = [
  "E",   # pycodestyle
  "F",   # Pyflakes
  "I",   # isort
  "ISC", # implicit-string-concat
  "T20", # flake8-print
  "UP",  # pyupgrade
]

ignore = [
  "E501",   # Disable warnings about lines longer than 80 chars
  "ISC001", # Don't warn about single-line implicit string concatenation
  "ISC003", # Don't warn about explicit string concatenation
]

# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = [
  "F",   # Pyflakes
  "I",   # isort
  "ISC", # implicit-string-concat
  "UP",  # pyupgrade
]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.per-file-ignores]
"*/__init__.py" = ["F401"]
"*/conftest.py" = ["F401"]

# Allow print statement in requirement tests
"events_tooling_py/requirement_tests/*.py" = ["T201"]

[tool.ruff.lint.isort]
# known-pytest = ["pytest", "py"]
known-first-party = ["events_tooling_py"]

combine-as-imports = true
# include-trailing-comma = true
force-sort-within-sections = true
# line-length = 88
# multi-line-output = 3
order-by-type = false

[tool.ruff.lint.flake8-implicit-str-concat]
allow-multiline = false

[tool.ruff.format]
quote-style = "preserve"
preview = true

[tool.poetry]
name = "events-tooling-py"
version = "0.7.2"
description = "Events Tooling Python Library"
authors = ["Multiverse"]

[tool.poetry.dependencies]
python = ">=3.12,<4"
pika = "^1.3.2"
pydantic = ">=2.0,<3.0.0"
jsonschema = "4.22.0"

[tool.poetry.group.dev.dependencies]
ruff = "~0.4.2"
pytest = "~8.2.0"
pytest-asyncio = "^0.24.0"
pytest-xdist = "^3.6.1"
mypy = "~1.13.0"
bandit = "^1.7.7"
pytest-rerunfailures = "^14.0"
types-pika = "^1.2.0b1"
syrupy = "^4.6.1"
# Use this when developing tests if you want to tweak the requirements
# event-tooling-test-framework = { path = "../event-tooling-test-framework", develop = true }
event-tooling-test-framework = { git = "**************:Multiverse-io/event-tooling-test-framework.git", rev = "v0.2.4" }
event-test-schemas = { git = "**************:Multiverse-io/event-schemas.git", rev = "python-test" }

[tool.mypy]
python_version = "3.12"
mypy_path = "events_tooling/type_stubs"
check_untyped_defs = true
disallow_any_unimported = true
disallow_untyped_defs = true
follow_imports = "normal"
ignore_missing_imports = true
no_implicit_optional = true
show_error_codes = true
strict_optional = true
warn_redundant_casts = true
warn_return_any = true
warn_unused_configs = true

# Strictly typed files
# `mypy .` runs all files in project
files = ["events_tooling_py"]
# Note: `mypy` w/ no args runs `files` (above)
# Ignore the following files:
exclude = ["events_tooling_py/(tests/|tests.py|conftest.py)"]

[[tool.mypy.overrides]]
module = "*.__snapshots__.*"
ignore_errors = true
follow_imports = "skip"

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
