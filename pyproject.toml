[tool.poetry]
name = "event-tooling-test-framework"
version = "0.2.4"
description = "Test framework for RabbitMQ event tooling"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "event_tooling_test_engine", from = "src" },
    { include = "event_tooling_tests", from = "src" },
]

[tool.poetry.dependencies]
python = "^3.12"
aiohttp = "^3.10.8"

[tool.poetry.group.dev.dependencies]
mypy = "^1.11.2"
pytest = "^8.3.3"
pytest-rerunfailures = "^14.0"
ruff = "^0.6.8"
pytest-asyncio = "^0.24.0"
pika = "^1.3.2"
aio-pika = "^9.4.3"
pytest-xdist = "^3.6.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_unimported = true
disallow_untyped_defs = true
follow_imports = "normal"
ignore_missing_imports = true
no_implicit_optional = true
show_error_codes = true
strict_optional = true
warn_redundant_casts = true
warn_return_any = true
warn_unused_configs = true
# Strictly typed files

files = ["src"]

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
testpaths = ["**/tests"]
