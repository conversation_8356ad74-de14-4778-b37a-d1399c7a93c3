$id: https://schema.multiverse.io/skillset/changelog/1.0.1
$schema: https://json-schema.org/draft/2020-12/schema
description: The schema for the skillset changelog event
type: object
required:
  - id
  - title
  - description
  - learning_uid
  - internal_extended_description
  - competency_modules
  - created_at
  - updated_at
properties:
  id:
    description: The id of the skillset
    type: string
    format: uuid
  title:
    description: The user facing title of the skillset
    type: string
  description:
    description: The user facing description of the skillset
    type: string
  learning_uid:
    description: |
      A manually created internal facing unique identifier for the skillset.
      Learning uses these to cross-reference and identify skillsets.
      Example value - "stem-AILiteracy".
    type: string
  internal_extended_description:
    description: |
      An internal facing detailed description of the skillset which may be used, for instance with LLMs
    type: string
  competency_modules:
    description: The list of competency modules that are part of the skillset. Order is not significant.
    type: array
    items:
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency module
          type: string
          format: uuid
  created_at:
    description: The time the skillset was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the skillset was updated in ISO8601 format
    type: string
    format: date-time
  deleted_at:
    description: The time the skillset was deleted in ISO8601 format
    type: string
    format: date-time
