$id: https://schema.multiverse.io/calendar_invite/invite_response/2.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Event dispatched when a calendar invite response is received.
required:
  - id
  - received_at
  - event_id
  - guest_email
  - response_status
properties:
  id:
    type: string
    description: The id of the calendar invite response event.
    format: uuid
  received_at:
    type: string
    description: The ISO 8601 timestamp of the event.
    format: date-time
  event_id:
    type: string
    description: The smart invite id of the event.
  event_type:
    type: string
    description: The type of event the invite is for, e.g. "prospect_info_session"
  guest_email:
    type: string
    description: The email address of the guest.
    format: email
  response_status:
    type: string
    description: The status of the response.
    enum: ["accepted", "tentative", "declined", "pending"]
  response_comment:
    type: string
    description: The comment, if any, of the response.
