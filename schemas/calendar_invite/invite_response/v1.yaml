$id: https://schema.multiverse.io/calendar_invite/invite_response/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Event dispatched when a calendar invite response is received.
required:
  - id
  - timestamp
  - event_id
  - guest_email
  - response_status
  - response_comment
properties:
  id:
    type: string
    description: The id of the calendar invite response event.
    format: uuid
  timestamp:
    type: integer
    description: The unix timestamp of the event.
  event_id:
    type: string
    description: The smart invite id of the event.
  guest_email:
    type: string
    description: The email address of the guest.
    format: email
  response_status:
    type: string
    description: The status of the response.
    enum: ["accepted", "tentative", "declined", "pending"]
  response_comment:
    type: string
    description: The comment, if any, of the response.
