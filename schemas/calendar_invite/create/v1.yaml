$id: https://schema.multiverse.io/calendar_invite/create/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Event dispatched to create Calendar Invites.
required:
  - id
  - requesting_application
  - organizer
  - timestamp
  - event_id
  - event_type
  - event_start
  - event_end
  - timezone
  - summary
  - description
  - location
  - guest_email
properties:
  id:
    type: string
    description: The id of the calendar invite create event.
    format: uuid
  requesting_application:
    type: string
    description: Name of the application publishing the event.
  organizer:
    type: object
    properties:
      email:
        type: string
        description: The email address of the event organizer.
        format: email
      name:
        type: string
        description: The name of the event organizer.
    required:
      - email
      - name
  timestamp:
    type: integer
    description: The unix timestamp of the event creation.
  event_id:
    type: string
    description: The id of the event.
  event_type:
    type: string
    description: The type of event.
  event_start:
    type: string
    description: The start date & time of the event.
    format: date-time
  event_end:
    type: string
    description: The end date & time of the event.
    format: date-time
  timezone:
    type: string
    description: The timezone of the event.
  summary:
    type: string
    description: The summary of the event, used as invitation email subject line.
  description:
    type: string
    description: The description of the event, used as invitation email body.
  location:
    type: object
    properties:
      description:
        type: string
        description: The description of the event location.
      lat:
        type: number
        description: The latitude of the event location.
      long:
        type: number
        description: The longitude of the event location.
    required:
      - description
  guest_email:
    type: string
    description: The email address of the guest.
    format: email
  guest_id:
    type: string
    description: The accounts service id of the guest.
    format: uuid
  force_send:
    type: boolean
    description: Whether to force send the calendar invite.
  callback_url:
    type: string
    description: The callback URL for sending RSVP notifications.
