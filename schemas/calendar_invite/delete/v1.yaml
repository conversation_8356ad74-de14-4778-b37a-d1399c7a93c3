$id: https://schema.multiverse.io/calendar_invite/delete/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Event dispatched to delete Calendar Invites.
required:
  - id
  - requesting_application
  - timestamp
  - event_id
  - event_type
  - guest_email
properties:
  id:
    type: string
    description: The id of the calendar invite create event.
    format: uuid
  requesting_application:
    type: string
    description: Name of the application publishing the event.
  timestamp:
    type: integer
    description: The unix timestamp of the event creation.
  event_id:
    type: string
    description: The id of the event.
  event_type:
    type: string
    description: The type of event.
  guest_email:
    type: string
    description: The email address of the guest.
    format: email
  guest_id:
    type: string
    description: The accounts service id of the guest.
    format: uuid
  reason:
    type: string
    description: The reason for deleting the calendar invite.
