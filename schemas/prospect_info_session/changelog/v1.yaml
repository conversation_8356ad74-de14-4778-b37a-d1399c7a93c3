$id: https://schema.multiverse.io/prospect_info_session/changelog/1.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a prospect info session is modified
required:
  - id
  - prospect_interest_form_id
  - name
  - company_uuid
  - start_time
  - duration
  - video_call_url
  - additional_invited_emails
  - host_emails
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the info session
    format: uuid
  prospect_interest_form_id:
    type: string
    description: The id of the prospect interest form that the info session is associated with
    format: uuid
  name:
    type: string
    description: The name of the info session
  description:
    type: string
    description: The description of the info session
  company_uuid:
    type: string
    description: The uuid of the company
    format: uuid
  start_time:
    type: string
    description: The start time of the info session
    format: date-time
  duration:
    type: number
    description: The duration of the info session
  video_call_url:
    type: string
    description: The video call url of the info session
  additional_invited_emails:
    type: array
    description: The additional invited emails of the info session
    items:
      type: string
      description: The email of the additional invited email
  host_emails:
    type: array
    description: The host emails of the info session
    items:
      type: string
      description: The email of the host
  external_meeting:
    type: object
    description: The external meeting details if the info session is hosted on a 3rd party platform, e.g. Teams
    properties:
      external_meeting_id:
        type: string
        description: The id of the external meeting from the hosting provider
      external_hosting_provider:
        type: string
        enum: ["teams", "zoom", "google_meet"]
        description: The hosting provider of the external meeting, e.g. teams
  inserted_at:
    type: string
    description: The date and time the info session was created
    format: date-time
  updated_at:
    type: string
    description: The date and time the info session was updated
    format: date-time
  removed_at:
    type: string
    description: The date and time the info session was removed if appropriate
