$id: https://schema.multiverse.io/prospect_info_session/hosts_added/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever hosts are added to a prospect info session
required:
  - id
  - host_emails
  - added_at
properties:
  id:
    type: string
    description: The id of the prospect info session
    format: uuid
  host_emails:
    type: array
    description: The emails of the hosts added to the prospect info session
    items:
      type: string
      description: The email of the host
  added_at:
    type: string
    description: The date and time the hosts were added to the prospect info session
    format: date-time
