$id: https://schema.multiverse.io/prospect_info_session/prospect_registered/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a prospect registers for a prospect info session
required:
  - prospect_id
  - info_session_id
  - registered_at
properties:
  prospect_id:
    type: string
    description: The id of the prospect registering for the prospect info session
    format: uuid
  info_session_id:
    type: string
    description: The id of the prospect info session
    format: uuid
  registered_at:
    type: string
    description: The date and time the prospect registered for the prospect info session
    format: date-time
