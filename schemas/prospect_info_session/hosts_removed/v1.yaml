$id: https://schema.multiverse.io/prospect_info_session/hosts_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever hosts are removed from a prospect info session
required:
  - id
  - host_emails
  - removed_at
properties:
  id:
    type: string
    description: The id of the prospect info session
    format: uuid
  host_emails:
    type: array
    description: The emails of the hosts removed from the prospect info session
    items:
      type: string
      description: The email of the host
  removed_at:
    type: string
    description: The date and time the hosts were removed from the prospect info session
    format: date-time
