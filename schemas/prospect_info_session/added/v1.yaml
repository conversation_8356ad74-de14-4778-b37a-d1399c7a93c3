$id: https://schema.multiverse.io/prospect_info_session/added/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a prospect info session is added
required:
  - id
  - prospect_interest_form_id
  - name
  - company_uuid
  - start_time
  - duration
  - inserted_at
properties:
  id:
    type: string
    description: The id of the info session
    format: uuid
  prospect_interest_form_id:
    type: string
    description: The id of the prospect interest form that the info session is associated with
  name:
    type: string
    description: The name of the info session
  description:
    type: string
    description: The description of the info session
  company_uuid:
    type: string
    description: The uuid of the company
    format: uuid
  start_time:
    type: string
    description: The start time of the info session
    format: date-time
  duration:
    type: number
    description: The duration of the info session
  video_call_url:
    type: string
    description: The video call url of the info session
  additional_invited_emails:
    type: array
    description: The additional invited emails of the info session
    items:
      type: string
      description: The email of the additional invited email
  host_emails:
    type: array
    description: The host emails of the info session
    items:
      type: string
      description: The email of the host email
  inserted_at:
    type: string
    description: The date and time the info session was created
    format: date-time
