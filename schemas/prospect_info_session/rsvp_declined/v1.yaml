$id: https://schema.multiverse.io/prospect_info_session/rsvp_declined/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a prospect declines a prospect info session RSVP
required:
  - info_session_id
  - prospect_id
  - rsvp_status
  - responded_at
properties:
  info_session_id:
    type: string
    description: The id of the info session
    format: uuid
  prospect_id:
    type: string
    description: The id of the prospect created from the prospect interest form
    format: uuid
  email:
    type: string
    description: The email address of the prospect
  rsvp_status:
    type: string
    const: "not_attending"
  responded_at:
    type: string
    description: The date and time the prospect responded to the info session RSVP
    format: date-time
