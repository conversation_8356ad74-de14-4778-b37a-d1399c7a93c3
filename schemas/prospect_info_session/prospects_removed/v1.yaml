$id: https://schema.multiverse.io/prospect_info_session/prospects_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever prospects are removed from a prospect info session
required:
  - id
  - prospect_emails
  - removed_at
properties:
  id:
    type: string
    description: The id of the prospect info session
    format: uuid
  prospect_emails:
    type: array
    description: The emails of the prospects removed from the prospect info session
    items:
      type: string
      description: The email of the prospect
  removed_at:
    type: string
    description: The date and time the prospects were removed from the prospect info session
    format: date-time
