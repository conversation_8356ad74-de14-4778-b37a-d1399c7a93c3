$id: https://schema.multiverse.io/enabler_unavailability_period/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: Represents a period of time that the enabler will be unavailable for.
type: object
required:
  - id
  - enabler_id
  - period
  - created_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the enabler unavailability period
    format: uuid
  enabler_id:
    type: string
    description: The id of the enabler who created the unavailability period
    format: uuid
  period:
    type: object
    description: The period of time the enabler is unavailable for
    required:
      - start_date
      - end_date
      - timezone
    properties:
      start_date:
        type: string
        description: The start date of the unavailability period
        format: date
      end_date:
        type: string
        description: The last date (inclusive) of the unavailability period
        format: date
      timezone:
        type: string
        description: The timezone of the unavailability period
  created_at:
    type: string
    description: The time this unavailability period was created in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time this unavailability period was updated in ISO8601 format
    format: date-time
