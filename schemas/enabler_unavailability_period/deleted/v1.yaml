$id: https://schema.multiverse.io/enabler_unavailability_period/deleted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: Event is published whenever a new enabler unavailability period is deleted by an enabler.
type: object
required:
  - id
  - enabler_id
  - deleted_at
properties:
  id:
    type: string
    description: The id of the enabler unavailability period
    format: uuid
  enabler_id:
    type: string
    description: The id of the enabler who has deleted the unavailability period
    format: uuid
  deleted_at:
    type: string
    description: The time this enabler unavailability period was deleted in ISO8601 format
    format: date-time
