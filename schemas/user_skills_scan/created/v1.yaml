$id: https://schema.multiverse.io/user_skills_scan/created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A skills scan has been created for a user.
required:
  - id
  - account_id
  - url
  - created_at
  - competencies
properties:
  id:
    description: The id of the skills scan
    type: string
    format: uuid
  account_id:
    description: The account id of the user assigned to the skills scan
    type: string
    format: uuid
  url:
    description: The unique url of the skills scan for the user
    type: string
    format: uri
  created_at:
    description: The time when the skills scan was created in ISO8601 format
    type: string
    format: date-time
  competencies:
    description: The competencies this skills scan will assess
    type: array
    items:
      minItems: 1
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
