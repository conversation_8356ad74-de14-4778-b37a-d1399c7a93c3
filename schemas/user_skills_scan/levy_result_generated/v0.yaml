$id: https://schema.multiverse.io/user_skills_scan/levy_result_generated/0.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Triggered whenever a result is generated for a completed Levy survey. This event indicates that the user's responses have been evaluated and the final outcome is now available.
required:
  - user_skills_scan_id
  - account_id
  - programmes
  - has_basic_eligibility
  - has_right_to_work
  - created_at
  - updated_at
properties:
  user_skills_scan_id:
    description: The id of the skills scan (also known as the survey instance id).
    type: string
    format: uuid
  account_id:
    type: string
    description: The account id of the user that completed the skills scan.
    format: uuid
  programmes:
    type: array
    description: The list of programmes the user has been scanned against and whether they meet the required eligibility criteria.
    items:
      type: object
      required:
        - apprenticeship_qualification_id
        - meets_required_criteria
      properties:
        apprenticeship_qualification_id:
          type: string
          format: uuid
          description: The id of the programme (this matches the Platform's apprenticeship qualification uuid).
        meets_required_criteria:
          type: boolean
          description: Returns true or false depending on whether a set of conditions specific to this programme are met. The criteria involved vary between programmes and are evaluated as a whole to determine the final result.
        apply_skills_decision:
          type: boolean
          description: Returns true if the user is able to apply all skills relevant for this programme.
        pla:
          type: number
          description: The amount of Prior Learning Adjustment the user needs.
        pla_decision:
          type: string
          description: Returns true if the user's Prior Learning Adjustment is below the programme's threshold.
        contract_length_decision:
          type: boolean
          description: Returns true if the user's contract end date is later than the programme's length threshold, or if the user does not have a fixed-term contract.
        minimum_hours_working_with_data_decision:
          type: boolean
          description: Returns true if the user meets the programme’s minimum required weekly hours of working with data.
        data_access_decision:
          type: boolean
          description: Returns true if the user has access to relevant datasets for this programme.
        data_role_decision:
          type: boolean
          description: Returns true if the user's core role is to work with data (e.g. data analyst, data scientist, data engineer).
        business_process_decision:
          type: boolean
          description: Returns true if the user's ability to improve business processes matches this programme's requirements.
  has_basic_eligibility:
    type: boolean
    description: Returns true if the user has basic eligibility - this is programme agnostic (e.g. sufficient working hours, no higher education, works in England).
  has_right_to_work:
    type: boolean
    description: Returns true if the user has the right to work in the UK.
  created_at:
    description: The time when the skills scan result was generated in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time when the skills scan result was updated in ISO8601 format
    type: string
    format: date-time
