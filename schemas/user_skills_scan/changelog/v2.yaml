$id: https://schema.multiverse.io/user_skills_scan/changelog/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A skills scan has been changed for a user.
required:
  - id
  - account_id
  - url
  - created_at
  - started_at
  - completed_at
  - competencies
  - user_goal_id
  - modules
properties:
  id:
    description: The id of the skills scan
    type: string
    format: uuid
  account_id:
    description: The account id of the user assigned to the skills scan
    type: string
    format: uuid
  user_goal_id:
    description: The id of the user goal the skills scan is associated with
    type: string
    format: uuid
  url:
    description: The unique url of the skills scan for the user
    type: string
    format: uri
  created_at:
    description: The time when the skills scan was created in ISO8601 format
    type: string
    format: date-time
  started_at:
    description: The time when the skills scan was started in ISO8601 format
    type: ["string", "null"]
    format: date-time
  completed_at:
    description: The time when the skills scan was completed in ISO8601 format
    type: ["string", "null"]
    format: date-time
  competencies:
    type: array
    description: The competencies this skills scan will assess with mastery level if identified competencies of the user.
    items:
      minItems: 1
      uniqueItems: true
      type: object
      required:
        - competency_id
        - mastery_percent
        - self_declared
      properties:
        self_declared:
          type:
            - boolean
            - "null"
          description: Whether the competency was self-declared by the user.
        competency_id:
          type: string
          format: uuid
          description: The competency id of the competency.
        mastery_percent:
          type:
            - number
            - "null"
          description: The users identified mastery of the competency, between 0 (none) and 100 (mastered).
          minimum: 0
          maximum: 100
  modules:
    type: array
    description: The competency modules this skills scan will assess with mastery level based on identified competencies of the user.
    items:
      minItems: 1
      uniqueItems: true
      type: object
      required:
        - competency_module_id
        - mastery_percent
        - self_declared
      properties:
        self_declared:
          type:
            - boolean
            - "null"
          description: Whether the competency module was self-declared by the user.
        competency_module_id:
          type: string
          format: uuid
          description: The competency module id of the competency module.
        mastery_percent:
          type:
            - number
            - "null"
          description: The users identified mastery/proficiency of the competency module, between 0 (no proficiency/mastery) and 100 (proficient/mastered).
          minimum: 0
          maximum: 100
