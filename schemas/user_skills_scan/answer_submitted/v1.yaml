$id: https://schema.multiverse.io/user_skills_scan/answer_submitted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  This event is sent when a user submits an answer to a question in a skills scan.
  The event does not contain information about the ordering of a question within a survey, so the
  answered_at timestamp should be used to determine the order of answers.
  If the skills_scan_id, and question_id match a previous event, it can be assumed that this
  is a user resubmitting an answer to the same question. In this case you'll also see that the
  answered_at timestamp is more recent than the previous event.
required:
  - skills_scan_id
  - account_id
  - question_id
  - question_text
  - answer_text
  - answered_at

properties:
  skills_scan_id:
    description: The ID of the skills scan.
    type: string
    format: uuid
  account_id:
    description: The ID of the account.
    type: string
    format: uuid
  question_id:
    description: The ID of the question that the user submitted an answer to.
    type: string
    format: uuid
  question_text:
    description: The text of the question.
    type: string
  answer_text:
    description: The text of the answer submitted by the user.
    type: string
  answered_at:
    description: The date and time the answer was submitted.
    type: string
    format: date-time
  related_competency_id:
    description: The ID of the competency that the question is related to.
    type: string
    format: uuid
