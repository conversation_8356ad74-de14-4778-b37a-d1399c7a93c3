$id: https://schema.multiverse.io/user_skills_scan/completed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A user has completed a skills scan.
required:
  - id
  - account_id
  - completed_at
  - user_competencies
properties:
  id:
    description: The id of the skills scan
    type: string
    format: uuid
  account_id:
    type: string
    description: The account id of the user that completed the skills scan.
    format: uuid
  completed_at:
    type: string
    description: The time when the user completed the skills scan in ISO8601 format.
    format: date-time
  user_competencies:
    type: array
    description: The identified competencies of the user.
    items:
      type: object
      required:
        - competency_id
        - mastery_percent
        - self_declared
      properties:
        self_declared:
          type: boolean
          description: Whether the competency was self-declared by the user.
        competency_id:
          type: string
          description: The competency id of the competency.
        mastery_percent:
          type: number
          description: The users identified mastery of the competency, between 0 (none) and 100 (mastered).
          minimum: 0
          maximum: 100
