$id: https://schema.multiverse.io/customer_diagnosis/completed/0.3.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a customer diagnosis process is completed
required:
  - id
  - status
  - completed_at
  - result
  - type
  - company_id
  - account_id
properties:
  id:
    description: The id of the customer diagnosis
    type: string
    format: uuid
  status:
    description: The status of the diagnosis. Will always be 'complete' when the diagnosis is complete
    type: string
    const: complete
  completed_at:
    description: The time when the diagnosis was completed in ISO8601 format
    type: string
    format: date-time
  type:
    description: The type of customer goal, can be either 'company' or 'department'
    type: string
    enum: ["company", "department"]
  company_id:
    description: The id of the customer/company which owns the diagnosis
    type: string
    format: uuid
  account_id:
    description: The account id of the user who submitted the needs used as input for the diagnosis
    type: string
    format: uuid
  result:
    description: The result of the diagnosis
    type: object
    required:
      - needs_summary
      - competencies
    properties:
      needs_summary:
        description: A text summary of the diagnosis result to render in the UI
        type: string
      competencies:
        description: List of competencies returned in the diagnosis result
        type: array
        items:
          type: object
          required:
            - id
          properties:
            id:
              description: The id of a competency returned in the diagnosis result
              type: string
              format: uuid
