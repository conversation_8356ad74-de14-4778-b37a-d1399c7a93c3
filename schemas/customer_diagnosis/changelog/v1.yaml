$id: https://schema.multiverse.io/customer_diagnosis/changelog/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event representing the current state of a customer diagnosis
required:
  - id
  - type
  - company_id
  - account_id
  - status
  - parent_id
  - children
  - needs
  - result
  - created_at
  - updated_at
properties:
  id:
    description: The id of the customer diagnosis
    type: string
    format: uuid
  type:
    description: The type of the diagnosis, can be either 'company' or 'department'
    type: string
    enum: ["company", "department"]
  company_id:
    description: The id of the customer/company which owns the diagnosis
    type: string
    format: uuid
  account_id:
    description: The account id of the user who submitted the needs used as input for the diagnosis
    type: string
    format: uuid
  status:
    description: The status of the diagnosis
    type: string
    enum:
      ["pending", "in_progress", "retrying", "complete", "accepted", "error"]
  needs:
    description: The customer needs used as an input for the diagnosis process
    type: string
  children:
    description: List of ids of the diagnosis that are children of this one
    type: array
    items:
      type: string
      format: uuid
  parent_id:
    description: The id of the parent diagnosis
    type: ["string", "null"]
    format: uuid
  result:
    description: The result of the diagnosis
    type: object
    required:
      - competencies
    properties:
      needs_summary:
        description: A text summary of the diagnosis result to render in the UI
        type: string
      competencies:
        description: List of competencies returned in the diagnosis result (an empty array if the diagnosis is not completed)
        type: array
        items:
          type: object
          required:
            - id
          properties:
            id:
              description: The id of a competency returned in the diagnosis result
              type: string
              format: uuid
  created_at:
    description: The time the diagnosis was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the diagnosis was update in ISO8601 format
    type: string
    format: date-time
