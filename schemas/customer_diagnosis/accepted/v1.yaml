$id: https://schema.multiverse.io/customer_diagnosis/accepted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a customer diagnosis has been finalised by the customer
required:
  - id
  - status
  - accepted_at
properties:
  id:
    description: The id of the customer diagnosis
    type: string
    format: uuid
  status:
    description: The status of the diagnosis. Will always be 'accepted' when the diagnosis is accepted
    type: string
    const: "accepted"
  accepted_at:
    description: The time when the diagnosis was accepted in ISO8601 format
    type: string
    format: date-time
