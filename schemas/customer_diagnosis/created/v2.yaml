$id: https://schema.multiverse.io/customer_diagnosis/created/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a diagnosis is created with a status of pending
required:
  - id
  - type
  - company_id
  - account_id
  - status
  - needs_q_and_as
  - parent_id
  - created_at
properties:
  id:
    description: The id of the customer diagnosis
    type: string
    format: uuid
  type:
    description: The type of the diagnosis, can be either 'company' or 'department'
    type: string
    enum: ["company", "department"]
  company_id:
    description: The id of the customer/company which owns the diagnosis
    type: string
    format: uuid
  account_id:
    description: The account id of the user who submitted the needs used as input for the diagnosis
    type: string
    format: uuid
  status:
    description: The status of the diagnosis, should always be 'pending' when the diagnosis is created
    type: string
    const: "pending"
  needs_q_and_as:
    description: The customer needs used as an input for the diagnosis process
    type: array
    items:
      type: object
      properties:
        question:
          description: The question asked related to the customer needs
          type: string
        answer:
          description: The answer provided for the question
          type: string
  parent_id:
    description: The id of the parent diagnosis
    type: ["string", "null"]
    format: uuid
  created_at:
    description: The time the diagnosis was created in ISO8601 format
    type: string
    format: date-time
