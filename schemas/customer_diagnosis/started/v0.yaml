$id: https://schema.multiverse.io/customer_diagnosis/started/0.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a customer diagnosis process is started
required:
  - id
  - status
  - started_at
properties:
  id:
    description: The id of the customer diagnosis
    type: string
    format: uuid
  status:
    description: The status of the diagnosis. Will always be 'in_progress' when the diagnosis is started
    type: string
    const: in_progress
  started_at:
    description: The time when the diagnosis was started in ISO8601 format
    type: string
    format: date-time
