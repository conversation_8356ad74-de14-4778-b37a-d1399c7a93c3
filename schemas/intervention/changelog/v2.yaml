$id: https://schema.multiverse.io/intervention/changelog/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The intervention changelog message from Learner Risk service.
required:
  - intervention
properties:
  intervention:
    type: object
    properties:
      id:
        type: string
        description: The unique ID of this intervention
        format: uuid
      author_account_id:
        type: string
        description: The ID of the account which authored this intervention
        format: uuid
      apprenticeship_id:
        type: string
        description: The ID of the apprenticieship this intervention is associated with
        format: uuid
      body:
        type: string
        description: The content of the intervention
        format: text
      type:
        type: object
        properties:
          id:
            type: string
            description: The id of the type of intervention
            format: uuid
          name:
            type: string
            description: The name of the type of intervention
            format: text
          severity_level:
            type: integer
            description: The severity level of the intervention type
            format: integer
          sensitive:
            type: boolean
            description: Whether the intervention type is sensitive or not
          action:
            type: object
            properties:
              id:
                type: string
                description: The id of the action of the intervention type
                format: uuid
              name:
                type: string
                description: The name of the action associated with the intervention type
                format: text
      end_at:
        type: string
        description: The end of the intervention
        format: date-time
      extensions:
        type: array
        description: List of Extensions associated with the Intervention
        items:
          type: object
          properties:
            id:
              type: string
              description: The id of the extension
              format: uuid
            author_account_id:
              type: string
              description: The id of the account that authored the extension
              format: uuid
            previous_end_at:
              type: string
              description: The previous end date of the associated intervention
              format: date-time
            end_at:
              type: string
              description: The updated end date of the associated intervention
              format: date-time
            body:
              type: string
              description: The content of the extension
              format: text
            inserted_at:
              type: string
              description: The time at which this extension was created
              format: date-time
            updated_at:
              type: string
              description: The time at which this extension was updated
              format: date-time
      inserted_at:
        type: string
        description: The time at which this intervention was created
        format: date-time
      updated_at:
        type: string
        description: The time at which this intervention was updated
        format: date-time
      deleted_at:
        type: ["string", "null"]
        description: The time at which this intervention was deleted, or null if it is not deleted
        format: date-time
    required:
      - id
      - author_account_id
      - apprenticeship_id
      - body
      - type
      - end_at
      - extensions
      - inserted_at
      - updated_at
      - deleted_at
