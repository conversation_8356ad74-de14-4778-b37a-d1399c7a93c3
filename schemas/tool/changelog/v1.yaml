$id: https://schema.multiverse.io/tool/changelog/1.0.1
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a tool is updated
required:
  - id
  - name
  - created_at
  - updated_at
properties:
  id:
    description: The id of the tool
    type: string
    format: uuid
  name:
    description: The name of the tool
    type: string
  created_at:
    description: The time the tool was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the tool was last updated in ISO8601 format
    type: string
    format: date-time
  deleted_at:
    description: The time the tool was soft deleted in ISO8601 format
    type: string
    format: date-time
    