$id: https://schema.multiverse.io/support_factor/resolved/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The support factor resolved message from Learner Risk service.
required:
  - resolution_id
  - author_account_id
  - apprenticeship_id
  - body
  - support_factors
  - resolved_at
properties:
  resolution_id:
    type: string
    description: The unique ID of this support factor resolution
    format: uuid
  author_account_id:
    type: string
    description: The ID of the account which authored this support factor resolution
    format: uuid
  apprenticeship_id:
    type: string
    description: The ID of the apprenticieship this support factor resolution is associated with
    format: uuid
  body:
    type: string
    description: The content of the support factor resolution
    format: text
  support_factors:
    type: array
    description: List of support factors associated with the resolution
    items:
      type: object
      properties:
        id:
          type: string
          description: The id of the support factor
          format: uuid
        name:
          type: string
          description: The name of the support factor
          format: text
        assignment_id:
          type: string
          description: The id of the corresponding support factor assignment
          format: uuid
  resolved_at:
    type: string
    description: The time at which these support factors were resolved
    format: date-time