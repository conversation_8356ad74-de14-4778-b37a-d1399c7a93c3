$id: https://schema.multiverse.io/support_factor/assigned/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The support factor assigned message from Learner Risk service.
required:
  - assignment_id
  - author_account_id
  - apprenticeship_id
  - body
  - support_factors
  - assigned_at
properties:
  assignment_id:
    type: string
    description: The unique ID of this support factor assignment
    format: uuid
  author_account_id:
    type: string
    description: The ID of the account which authored this support factor assignment
    format: uuid
  apprenticeship_id:
    type: string
    description: The ID of the apprenticieship this support factor assignment is associated with
    format: uuid
  body:
    type: string
    description: The content of the support factor assignment
    format: text
  support_factors:
    type: array
    description: List of support factors associated with the assignment
    items:
      type: object
      properties:
        id:
          type: string
          description: The id of the support factor
          format: uuid
        name:
          type: string
          description: The name of the support factor
          format: text
  assigned_at:
    type: string
    description: The time at which these support factors were assigned
    format: date-time