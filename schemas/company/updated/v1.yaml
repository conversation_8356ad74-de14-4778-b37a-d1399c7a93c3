$id: https://schema.multiverse.io/company/updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The company updated message from Platform.
required:
  - id
  - name
  - automated_comms
  - timestamp
  - region
properties:
  id:
    type: string
    format: uuid
    description: The id of the company record.
  name:
    type: string
  automated_comms:
    type: 
      - boolean
      - "null"
  timestamp:
    type: integer
  region:
    type: string
    enum:
      - GB
      - US
