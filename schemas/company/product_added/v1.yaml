$id: https://schema.multiverse.io/company/product_added/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  Published whenever a product is added to a company for the first time or when
  a previously removed/deactivated product is added back/reactivated.
required:
  - id
  - products
  - added_at
properties:
  id:
    type: string
    description: The id of the company record.
    format: uuid
  products:
    description: >
      All the products that the company is using or has used in the past, including their configuration.
    type: array
    items:
      type: object
      required:
        - type
        - active
        - options
      properties:
        type:
          type: string
          enum: ["levy", "saas"]
        active:
          type: boolean
        options:
          type: object
  added_at:
    description: The time the product was added to the company in ISO8601 format.
    type: string
    format: date-time
