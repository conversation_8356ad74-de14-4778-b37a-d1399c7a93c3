$id: https://schema.multiverse.io/company/account_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever an account is removed from a company. (WARNING!!! This is a temporary event that will be emitted from the client-xp app but will be emitted from the company or the accounts service in the future and probably will be replaced, it is just for consumption from DW atm)
required:
  - id
  - account_id
  - removed_at
properties:
  id:
    description: The company service company id.
    type: string
    format: uuid
  account_id:
    description: The accounts service account id.
    type: string
    format: uuid
  removed_at:
    type: string
    description: Timestamp of the account's removal from the company.
    format: date-time
