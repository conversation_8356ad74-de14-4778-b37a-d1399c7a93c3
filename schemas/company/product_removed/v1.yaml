$id: https://schema.multiverse.io/company/product_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  Published whenever a product is removed from a company
required:
  - id
  - products
  - removed_at
properties:
  id:
    type: string
    description: The id of the company record.
    format: uuid
  products:
    description: >
      All the products that the company is using or has used in the past, including their configuration.
    type: array
    items:
      type: object
      required:
        - type
        - active
        - options
      properties:
        type:
          type: string
          enum: ["levy", "saas"]
        active:
          type: boolean
        options:
          type: object
  removed_at:
    description: The time the product was removed from the company in ISO8601 format.
    type: string
    format: date-time
