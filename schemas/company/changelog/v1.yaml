$id: https://schema.multiverse.io/company/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The company changelog message from companies service.
required:
  - id
  - company
  - preferences
properties:
  id:
    type: string
    description: The id of the company record.
    format: uuid
  company:
    type: object
    properties:
      id:
        type: string
        format: uuid
      parent_company_id:
        type:
          - string
          - "null"
      name:
        type: string
      description:
        type: string
      url:
        type: string
        description: The URL for the company website
      address_line_1:
        type: string
      postcode:
        type: string
      region:
        type: string
        enum:
          - uk
          - us
          - global
    required:
      - id
      - parent_company_id
      - name
      - description
      - url
      - address_line_1
      - postcode
      - region
  preferences:
    type: object
    properties:
      ai_opt_out:
        type: boolean
    required:
      - ai_opt_out
