$id: https://schema.multiverse.io/company/changelog/5.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The company changelog message from companies service.
required:
  - id
  - company
  - preferences
properties:
  id:
    type: string
    description: The id of the company record.
    format: uuid
  company:
    type: object
    properties:
      id:
        type: string
        format: uuid
      parent_company_id:
        type:
          - string
          - "null"
      name:
        type: string
      status:
        description: Active companies are the ones we provide services directly to. Prospectives can be either non-finished deals or parents of active companies.
        type: string
        enum:
          - active
          - prospective
      url:
        type: string
        description: The URL for the company website
      address_line_1:
        type: string
      postcode:
        type: string
      region:
        description: The region "other" is only used for non-active parent companies
        type: string
        enum:
          - uk
          - us
          - other
      deleted_at:
        description:  The date-time when the company was deleted on Salesforce
        type: 
          - string
          - "null"
        format: date-time
    required:
      - id
      - parent_company_id
      - name
      - status
      - url
      - address_line_1
      - postcode
      - region
      - deleted_at
  preferences:
    type: object
    properties:
      ai_opt_out:
        type: boolean
    required:
      - ai_opt_out
