$id: https://schema.multiverse.io/company/changelog/8.6.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The company changelog message from companies service.
required:
  - id
  - company
  - preferences
properties:
  id:
    type: string
    description: The id of the company record.
    format: uuid
  company:
    type: object
    properties:
      id:
        type: string
        format: uuid
      parent_company_id:
        type:
          - string
          - "null"
      name:
        type: string
      status:
        description: Active companies are the ones we provide services directly to. Prospectives can be either non-finished deals or parents of active companies.
        type: string
        enum:
          - active
          - prospective
      paying_saas_customer:
        description: >
          This is a flag to indicate if the company is a paying customer of Multiverse.
        type: boolean
        default: false
      url:
        type:
          - string
          - "null"
        description: The URL for the company website
      address_line_1:
        type:
          - string
          - "null"
      postcode:
        type:
          - string
          - "null"
      region:
        description: The region "other" is only used for non-active parent companies
        type: string
        enum:
          - uk
          - us
          - other
      salesforce_id:
        type: string
      internal:
        description: >
          Indicates whether a company is an internal company. Internal companies are used for testing,
          internal demos, etc. Internal companies are not real paying customers.
          If this property is not present in an event payload, its value can be assumed as false.
        type: boolean
        default: false
      products:
        description: >
          All the products that the company is using or has used in the past, including their configuration.
        type: array
        items:
          type: object
          required:
            - type
            - active
            - options
          properties:
            type:
              type: string
              enum: ["levy", "saas"]
            active:
              type: boolean
            options:
              type: object
      code:
        description: >
          A unique identifier code assigned to each company that allows users to be asscoiated with company during signup by entering this code in the registration form.
        type: string
      email_domains:
        description: >
          A list of email domains that are associated with the company.
        type: array
        items:
          type: string
          format: hostname
      updated_at:
        type: string
        format: date-time
        description: The last time the company was updated
    required:
      - id
      - parent_company_id
      - name
      - status
      - url
      - address_line_1
      - postcode
      - region
  preferences:
    type: object
    properties:
      ai_opt_out:
        type: boolean
    required:
      - ai_opt_out
