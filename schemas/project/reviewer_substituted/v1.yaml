$id: https://schema.multiverse.io/project/reviewer_substituted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a Project Reviewer is replaced with another reviewer.
required:
  - project_review_id
  - project_id
  - project_submission_id
  - previous_reviewer_id 
  - substitute_reviewer_id
  - reviewer_substituted_at
properties:
  project_review_id:
    description: The id of the ProjectReview
    type: string
    format: uuid
  project_id:
    description: The Project against which a reviewer was substituted.
    type: string
    format: uuid
  project_submission_id:
    description: The ProjectSubmission ID of the review.
    type: string
    format: uuid
  previous_reviewer_id:
    description: The account id of the previous reviewer.
    type: string
    format: uuid
  substitute_reviewer_id:
    description: The account id of the current reviewer.
    type: string
    format: uuid
  reviewer_substituted_at:
    description: The time when the reviewer was substituted.
    type: string
    format: date-time
