$id: https://schema.multiverse.io/project/reflection_submitted/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a learner submits their reflection after a review on <PERSON>.
required:
  - project_id
  - project_review_id
  - project_review_reflection_id
  - learner_account_id
  - project_review_reflection_created_at
properties:
  project_id:
    description: The project id against which a review reflection was submitted
    type: string
    format: uuid
  project_review_id:
    description: The project review id against which the the reflection was submitted
    type: string
    format: uuid
  learner_account_id:
    description: The account ID of the learner attached to the reflection.
    type: string
    format: uuid
  project_review_reflection_id:
    description: The ID of the submitted reflection
    type: string
    format: uuid
  project_review_reflection_created_at:
    description: The time the reflection was created in ISO8601 format.
    type: string
    format: date-time
  apprenticeship_id:
    description: The ID of the apprenticeship associated with the project submission. Only included for Levy projects.
    type: string
    format: uuid