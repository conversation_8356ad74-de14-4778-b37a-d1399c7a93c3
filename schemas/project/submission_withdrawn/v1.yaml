$id: https://schema.multiverse.io/project/submission_withdrawn/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: <PERSON><PERSON> can withdraw a submission before a coach can review it.
required:
  - project_id
  - project_submission_id
  - learner_account_id
  - project_submission_state
  - project_submission_withdrawn_at
properties:
  project_id:
    description: The Project against which a submission withdrawal was made.
    type: string
    format: uuid
  project_submission_id:
    description: The ProjectSubmission ID of the entry.
    type: string
    format: uuid
  learner_account_id:
    description: The account ID of the learner who attached to the submission.
    type: string
    format: uuid
  project_submission_state:
    description: The state of the submission as it is seen by the learner at project submission withdrawal. This value is always going to be `in_progress` when a submission is withdrawn.
    type: string
    const: in_progress
  project_submission_withdrawn_at:
    description: The time the ProjectSubmission was created in ISO8601 format.
    type: string
    format: date-time
  apprenticeship_id:
    description: The ID of the apprenticeship associated with the project submission. Only included for Levy projects.
    type: string
    format: uuid