$id: https://schema.multiverse.io/project/submitted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a ProjectSubmission is made. A Project can have multiple submissions but only one can be active at a time.
required:
  - project_id
  - learner_account_id
  - project_submission_id
  - project_submission_state
  - project_submitted_at
properties:
  project_id:
    description: The Project against which a submission was made.
    type: string
    format: uuid
  learner_account_id:
    description: The account ID of the learner who made the submission.
    type: string
    format: uuid
  project_submission_id:
    description: The ProjectSubmission ID of the entry.
    type: string
    format: uuid
  project_submitted_at:
    description: The time the ProjectSubmission was created in ISO8601 format.
    type: string
    format: date-time
  project_submission_state:
    description: The state of the submission as it is seen by the learner after they submit their project for review. This will always be set to 'awaiting_review'.
    type: string
    const: awaiting_review
