$id: https://schema.multiverse.io/project/submitted/3.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a project is submitted. A project can have multiple submissions but only one can be active at a time.
required:
  - project_id
  - learner_account_id
  - project_submission_id
  - project_submission_state
  - project_submitted_at
  - project_task_submissions
properties:
  project_id:
    description: The Project against which a submission was made.
    type: string
    format: uuid
  learner_account_id:
    description: The account ID of the learner who made the submission.
    type: string
    format: uuid
  project_submission_id:
    description: The id of the project submission.
    type: string
    format: uuid
  project_submitted_at:
    description: The time the ProjectSubmission was created in ISO8601 format.
    type: string
    format: date-time
  project_submission_state:
    description: The state of the submission as it is seen by the learner after they submit their project for review. This will always be set to 'awaiting_review'.
    type: string
    const: awaiting_review
  project_submission_title:
    description: The title of the project submission
    type: string
  project_task_submissions:
    type: array
    description: A collection of tasks submitted as part of the project submission
    items:
      type: object
      required:
        - submission_content
        - task_description
        - task_order
      properties:
        submission_content:
          type: string
          description: The content of the submission containing HTML markup
          contentMediaType: text/html
        task_description:
          type: string
          description: The description of the task
          contentMediaType: text/html
        task_order:
          type: number
          description: 0 indexed order in which this task appears
        ksbs:
          type: array
          description: List of KSBs associated with this task
          items:
            type: object
            required:
              - id
              - type
            properties:
              id:
                type: string
                format: uuid
                description: The id of the KSB
              description:
                type: string
                description: The description of the KSB
              tag:
                type: string
                description: The tag of the KSB
              type:
                type: string
                description: The type of the KSB
                enum:
                  - KNOWLEDGE
                  - BEHAVIOUR
                  - SKILL
              grading_primer:
                type: string
                description: The grading primer for the KSB, it's optional as some projects still use the old grading rubrics
              grading_criteria:
                type: array
                description: The list of grading criterion for the KSB, it's optional as some projects still use the old grading rubrics
                items:
                  type: object
                  required: ["criterion_id", "criterion_text", "criterion_target_grade", "criterion_order"]
                  properties:
                    criterion_id:
                      type: string
                      format: uuid
                      description: The uuid of the grading criterion
                    criterion_text:
                      type: string
                      description: The grading criterion text for the KSB
                    criterion_target_grade:
                      type: string
                      description: The target grade for the KSB
                      enum: ["MEETS_EXPECTATIONS", "EXCEEDS_EXPECTATIONS"]
                    criterion_order:
                      type: number
                      description: 0 indexed order of the grading criterion
        learning_objectives:
          type: array
          description: List of Learning Objectives associated with this task
          items:
            type: object
            required:
              - id
            properties:
              id:
                type: string
                description: The id of the Learning Objective
                format: uuid
              description:
                type: string
                description: The description of the Learning Objective
              grading_primer:
                type: string
                description: The grading primer for the Learning Objective, it's optional as some projects still use the old grading rubrics
              grading_criteria:
                type: array
                description: The list of grading criterion for the Learning Objective, it's optional as some projects still use the old grading rubrics
                items:
                  type: object
                  required: ["criterion_id", "criterion_text", "criterion_target_grade", "criterion_order"]
                  properties:
                    criterion_id:
                      type: string
                      format: uuid
                      description: The uuid of the grading criterion
                    criterion_text:
                      type: string
                      description: The grading criterion text for the Learning Objective
                    criterion_target_grade:
                      type: string
                      description: The criterion target grade for the Learning Objective
                      enum: ["MEETS_EXPECTATIONS", "EXCEEDS_EXPECTATIONS"]
                    criterion_order:
                      type: number
                      description: 0 indexed order of the grading criterion
      oneOf:
        - required: [ksbs]
        - required: [learning_objectives]

