$id: https://schema.multiverse.io/project/reviewer_withdrawn/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a coach withdraws their draft review before its submission on Ariel
required:
  - project_review_id
  - project_id
  - project_submission_id
  - reviewer_account_id
  - reviewer_withdrawn_at
properties:
  project_review_id:
    description: The id of the withdrawn ProjectReview
    type: string
    format: uuid
  project_id:
    description: The Project attached to the withdrawn review.
    type: string
    format: uuid
  project_submission_id:
    description: The ProjectSubmission ID attached to the withdrawn review.
    type: string
    format: uuid
  reviewer_account_id:
    description: The account id of the reviewer.
    type: string
    format: uuid
  reviewer_withdrawn_at:
    description: The time when the reviewer was withdrawn.
    type: string
    format: date-time
