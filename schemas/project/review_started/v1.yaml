$id: https://schema.multiverse.io/project/review_started/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Message published when a coach starts reviewing a project submission from Ariel.
required:
  - project_review_id
  - project_id
  - project_submission_id
  - reviewer_account_id 
  - review_state
  - review_started_at
properties:
  project_review_id:
    description: The id of the ProjectReview
    type: string
    format: uuid
  project_id:
    description: The Project attached to the review.
    type: string
    format: uuid
  project_submission_id:
    description: The ProjectSubmission ID of the review.
    type: string
    format: uuid
  reviewer_account_id:
    description: The account id of the reviewer.
    type: string
    format: uuid
  review_state:
    description: The state in which the project review is in at the moment. This value is always going to be `draft`
    type: string
    const: draft
  review_started_at:
    description: The time when the review was started.
    type: string
    format: date-time
