$id: https://schema.multiverse.io/project/changelog/1.4.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a Project is updated.
required:
  - id
  - title
  - thumbnail
  - modified_at
  - tasks
properties:
  id:
    description: The id of the Project.
    type: string
    format: uuid
  project_type:
    description: The type of project. Either LEVY or SAAS.
    type: string
    enum: ["levy", "saas"]
  title:
    description: The title of the Project.
    type: string
    maxLength: 255
  html_description:
    description: The html description of the Project that can include images, links, bullet points, etc.
    type: string
  short_description:
    description: The shortened description of the Project.
    type: string
    maxLength: 1023
  thumbnail:
    description: The thumbnail image URL of the Project.
    type: string
    maxLength: 512
  competency_module_id:
    description: The competency module this project is associated with, if any.
    type: string
    format: uuid
  created_at:
    description: The time the Project was created in ISO8601 format.
    type: string
    format: date-time
  modified_at:
    description: The time the Project was modified in ISO8601 format.
    type: string
    format: date-time
  deleted_at:
    description: The time the Project was soft deleted in ISO8601 format.
    type: ["string", "null"]
    format: date-time
  tasks:
    description: The list of tasks associated with the project.
    type: array
    items:
      type: object
      required:
        - id
        - description
      properties:
        id:
          description: The id of the task
          type: string
          format: uuid
        description:
          description: The description of the task
          type: string
        ksb_ids:
          description: The list of KSB ids associated with the task. Only for Levy projects
          type: array
          items:
            type: string
            format: uuid
            uniqueItems: true
        learning_objective_ids:
          description: The list of learning objective ids associated with the task. This is applicable for SaaS projects only.
          type: array
          items:
            type: string
            format: uuid
            uniqueItems: true
