$id: https://schema.multiverse.io/project/started/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a learner starts working on a project submission. Since learners can submit projects more than once, there shall be one project started event per submission.
required:
  - project_id
  - learner_account_id
  - project_submission_id
  - project_submission_state
  - project_started_at
properties:
  project_id:
    description: The Project against which a submission was made.
    type: string
    format: uuid
  learner_account_id:
    description: The account ID of the learner who made the submission.
    type: string
    format: uuid
  project_submission_id:
    description: The ProjectSubmission ID of the entry.
    type: string
    format: uuid
  project_started_at:
    description: The time the ProjectSubmission was created in ISO8601 format.
    type: string
    format: date-time
  project_submission_state:
    description: The state of the submission as it is seen by the learner when they start working on their project.  This will always be set to 'in_progress'. 
    type: string
    const: in_progress
  apprenticeship_id:
    description: The ID of the apprenticeship associated with the project submission. Only included for Levy projects.
    type: string
    format: uuid
