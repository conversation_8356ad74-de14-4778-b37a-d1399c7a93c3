$id: https://schema.multiverse.io/project/completed/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a Project is completed.
required:
  - project_id
  - project_submission_id
  - project_submission_state
  - project_completed_at
  - learner_account_id
properties:
  project_id:
    description: The Project against which a submission was made.
    type: string
    format: uuid
  project_submission_id:
    description: The ProjectSubmission ID of the entry.
    type: string
    format: uuid
  project_completed_at:
    description: The time the ProjectSubmission was created in ISO8601 format.
    type: string
    format: date-time
  project_submission_state:
    description: The state of the submission as it is seen by learner at project completion. This value is always going to be `completed`.
    type: string
    const: completed 
  learner_account_id:
    description: The account ID of the learner who made the submission.
    type: string
    format: uuid
  apprenticeship_id:
    description: The ID of the apprenticeship associated with the project submission. Only included for Levy projects.
    type: string
    format: uuid
