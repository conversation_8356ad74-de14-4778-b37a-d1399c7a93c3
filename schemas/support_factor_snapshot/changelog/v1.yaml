$id: https://schema.multiverse.io/support_factor_snapshot/changelog/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The support factor snapshot changelog message from Learner Risk service.
required:
  - snapshot_id
  - author_account_id
  - apprenticeship_id
  - body
  - support_factors
  - inserted_at
  - updated_at
properties:
  snapshot_id:
    type: string
    description: The unique ID of this support factor snapshot
    format: uuid
  author_account_id:
    type: string
    description: The ID of the account which authored this support factor snapshot
    format: uuid
  apprenticeship_id:
    type: string
    description: The ID of the apprenticieship this support factor snapshot is associated with
    format: uuid
  body:
    type: string
    description: The content associated with these support factors
    format: text
  support_factors:
    type: array
    description: List of support factors associated in this snapshot
    items:
      type: object
      properties:
        id:
          type: string
          description: The id of the support factor
          format: uuid
        name:
          type: string
          description: The name of the support factor
          format: text
        sensitive:
          type: boolean
          description: Whether the support factor is sensitive or not
  inserted_at:
    type: string
    description: The time at which these support factors snapshot was inserted
    format: date-time
  updated_at:
    type: string
    description: The time at which these support factors snapshot was updated
    format: date-time