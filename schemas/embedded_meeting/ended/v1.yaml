$id: https://schema.multiverse.io/embedded_meeting/ended/1.0.1
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The meeting ended event for the embedded meeting service (Hello).
required:
  - meetingId
  - webhookTimestamp
  - actualDurationSeconds
  - reason
  - type
properties:
  meetingId:
    type: string
    description: The id of the corresponding meeting record.
  webhookTimestamp:
    type: string
    description: The timestamp of the meeting started webhook event.
    format: date-time
  actualDurationSeconds:
    type: integer
    description: The actual duration of the meeting in seconds.
  reason:
    type: string
    description: The reason for the meeting starting.
  type:
    type: string
    description: The type of the meeting.
    enum: ["PUBLIC", "PRIVATE", "INSTANT", "STUB"]
