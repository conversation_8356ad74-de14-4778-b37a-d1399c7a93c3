$id: https://schema.multiverse.io/embedded_meeting/participant_left/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The meeting participant left event for the embedded meeting service (Hello).
required:
  - meetingId
  - accountsServiceId
  - helloUserId
  - leftAt
  - isHost
properties:
  meetingId:
    type: string
    description: The id of the corresponding meeting record.
  accountsServiceId:
    type: string
    description: The id of the accounts service record for the participant.
    format: uuid
  helloUserId:
    type: string
    description: The id of the participant user record in the Hello service.
  leftAt:
    type: string
    description: The timestamp of the participant left webhook event.
    format: date-time
  isHost:
    type: boolean
    description: Whether the participant is a host of the meeting.
