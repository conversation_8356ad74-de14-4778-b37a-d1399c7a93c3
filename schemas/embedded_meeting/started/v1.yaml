$id: https://schema.multiverse.io/embedded_meeting/started/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The meeting started event for the embedded meeting service (Hello).
required:
  - meetingId
  - webhookTimestamp
  - scheduledDurationMinutes
  - reason
  - type
properties:
  meetingId:
    type: string
    description: The id of the corresponding meeting record.
  liveSessionId:
    type: string
    description: The id of the corresponding live session.
  webhookTimestamp:
    type: string
    description: The timestamp of the meeting started webhook event.
    format: date-time
  scheduledDurationMinutes:
    type: integer
    description: The scheduled duration of the meeting in minutes.
  reason:
    type: string
    description: The reason for the meeting starting.
  type:
    type: string
    description: The type of the meeting.
    enum: ["PUBLIC", "PRIVATE", "INSTANT", "STUB"]
