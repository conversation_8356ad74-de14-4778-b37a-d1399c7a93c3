$id: https://schema.multiverse.io/embedded_meeting/scheduled/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The meeting scheduled event for the embedded meeting service (Hello).
required:
  - meetingId
  - createdAt
  - startDate
  - durationMinutes
  - title
  - agenda
  - reason
  - type
  - createdBy
  - isCancelled
properties:
  meetingId:
    type: string
    description: The id of the corresponding meeting record.
  createdAt:
    type: string
    description: The timestamp when the meeting was created.
    format: date-time
  startDate:
    type: string
    description: The timestamp when the meeting is scheduled to start.
    format: date-time
  durationMinutes:
    type: integer
    description: The scheduled duration of the meeting in minutes.
  title:
    type: string
    description: The name of the scheduled meeting.
  agenda:
    type: string
    description: The agenda of the scheduled meeting.
  reason:
    type: string
    description: The reason for the meeting starting.
  type:
    type: string
    description: The type of the meeting.
    enum: ["PUBLIC", "PRIVATE", "INSTANT", "STUB"]
  createdBy:
    type: string
    description: Information about how the meeting was created, whether it was a calling service or UI feature.
  isCancelled:
    type: boolean
    description: Whether the meeting is cancelled.
  createdByAccountId:
    type: string
    description: The accounts service id of the user who created this meeting.
    format: uuid
