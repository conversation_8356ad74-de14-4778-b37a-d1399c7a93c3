$id: https://schema.multiverse.io/embedded_meeting/summary_created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The meeting summary event for an embedded meeting.
required:
  - id
  - meetingId
  - liveSessionId
  - text
  - retentionStatus
properties:
  id:
    type: string
    description: The id of the summary.
  liveSessionId:
    type: string
    description: The id of the corresponding liveSession record in the hello_app database containing information about the Whereby room assigned to a given meeting
  meetingId:
    type: string
    description: The id of the corresponding meeting record in the hello_app database containing information about the meeting to which the Whereby room is assigned.
  text:
    type: string
    description: The AI generated summary text of the meeting, not including chapters or section breakdowns.
  retentionStatus:
    type: string
    description: Indicates the summary's data retention state.
    enum: ["VIEWABLE", "RECOVERABLE", "DELETED"]
