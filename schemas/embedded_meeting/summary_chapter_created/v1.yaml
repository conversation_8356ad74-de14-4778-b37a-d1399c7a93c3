$id: https://schema.multiverse.io/embedded_meeting/summary_chapter_created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An AI-generated topical segment of a meeting summary for an embedded meeting. A chapter represents a distinct topic discussed during the meeting.
required:
  - id
  - title
  - text
  - sequence_index
  - summary_id
properties:
  id:
    type: string
    description: The id of the summary chapter.
  title:
    type: string
    description: An AI-generated title that captures what this topical segment is about.
  text:
    type: string
    description: An AI-generated brief summary of the chapter content.
  sequence_index:
    type: integer
    description: The position of this chapter in the sequence of chapters, used for ordering.
  summary_id:
    type: string
    description: The id of the parent summary to which this chapter belongs. 
