$id: https://schema.multiverse.io/embedded_meeting/recorded/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The meeting recorded event for the embedded meeting service (Hello).
required:
  - meetingId
  - startedAt
  - webhookSentAt
properties:
  meetingId:
    type: string
    description: The id of the corresponding meeting record.
  startedAt:
    type: string
    description: The timestamp when the recording started.
    format: date-time
  webhookSentAt:
    type: string
    description: The timestamp when the recordingEnded webhook was dispatched from Whereby.
    format: date-time
