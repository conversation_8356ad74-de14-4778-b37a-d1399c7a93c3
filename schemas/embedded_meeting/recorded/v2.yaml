$id: https://schema.multiverse.io/embedded_meeting/recorded/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The meeting recorded event for the embedded meeting service (Hello).
required:
  - liveSessionId
  - rawFilename
  - startedAt
  - webhookTimestamp
properties:
  liveSessionId:
    type: string
    description: The id of the corresponding live session.
  rawFilename:
    type: string
    description: The name of the raw recording file.
  startedAt:
    type: string
    description: The timestamp when the recording started.
    format: date-time
  webhookTimestamp:
    type: string
    description: The timestamp when we received the call.
    format: date-time
