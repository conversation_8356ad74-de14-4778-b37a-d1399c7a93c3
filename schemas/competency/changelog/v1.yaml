$id: https://schema.multiverse.io/competency/changelog/1.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a competency is updated
required:
  - id
  - name
  - met_threshold
  - exceeded_threshold
  - version
  - previous_versions
  - ksbs
  - created_at
  - updated_at
properties:
  id:
    description: The id of the competency
    type: string
    format: uuid
  name:
    description: The name of the competency
    type: string
  description:
    description: The description of the competency
    type: string
  met_threshold:
    description: The score needed to meet the competency's expectations
    type: number
    minimum: 0
    maximum: 100
  exceeded_threshold:
    description: The score needed to exceed the competency's expectations
    type: number
    minimum: 0
    maximum: 100
  version:
    description: The version number of the competency
    type: number
  previous_versions:
    description: The list of previous versions of the competency
    type: array
    items:
      minItems: 0
      uniqueItems: true
      type: object
      required:
        - id
        - version
        - created_at
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
        version:
          description: The version number of the competency
          type: number
        created_at:
          description: The time the competency was created in ISO8601 format
          type: string
          format: date-time
  ksbs:
    description: The list of KSBs associated with the competency
    type: array
    items:
      minItems: 0
      uniqueItems: true
      type: object
      required:
        - id
        - weight
      properties:
        id:
          description: The id of the KSB
          type: string
          format: uuid
        weight:
          description: The weight that the KSB contributes to the competency
          type: number
        description:
          description: The description of the KSB - optional for schema backward compatibility
          type: string
        exceeded_explanation:
          description: Optional explanation of what is required to exceed the KSB's expectations
          type: string
  created_at:
    description: The time the competency was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the competency was updated in ISO8601 format
    type: string
    format: date-time
