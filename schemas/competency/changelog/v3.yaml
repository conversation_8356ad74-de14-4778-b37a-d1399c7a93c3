$id: https://schema.multiverse.io/competency/changelog/3.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a competency is updated
required:
  - id
  - name
  - status
  - target
  - created_at
  - updated_at
  - internal_extended_description
properties:
  id:
    description: The id of the competency
    type: string
    format: uuid
  name:
    description: The name of the competency
    type: string
  description:
    description: A user facing description of the competency
    type: string
  internal_extended_description:
    description: |
      An internal facing detailed description of the competency.
      For competencies where `status` is "deprecated" this field
      is likely to be null.
    type: ["string", "null"]
  learning_uid:
    description: |
      A manually created internal facing unique identifier for the competency.
      Learning uses these to cross-reference and identify competencies.
      Example values would be "DO-2-Sheets" or "DS-52".
    type: string
  status:
    description: |
      The status of the competency: 
      active = competency is linked to learning content
      pending = competency is not linked to learning content
      deprecated = competency may be linked to learning content but will not be used in future diagnosis or goals
      deleted = competency has been soft deleted
    type: string
    enum: ["active", "pending", "deprecated", "deleted"]
  target:
    description: The target context the competency will be used in
    type: string
    enum: ["saas", "levy", "saas_and_levy"]
  created_at:
    description: The time the competency was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the competency was updated in ISO8601 format
    type: string
    format: date-time
