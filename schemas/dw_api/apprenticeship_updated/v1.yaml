$id: https://schema.multiverse.io/dw_api/apprenticeship_updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Events are raised whenever data related to a Data Warehouse API Apprenticeship record is updated
required:
  - id
  - fields
  - timestamp
properties:
    id:
      type: string
      description: The apprenticeship id
      format: uuid
    fields:
      type: array
      description: List of changed field names. Keys are generated deterministically from the schema like path.to.field.name
      items:
        type: string
    timestamp:
      type: string
      description: The time that the data was updated in the database represented as a string in ISO8601 format
      format: date-time   
