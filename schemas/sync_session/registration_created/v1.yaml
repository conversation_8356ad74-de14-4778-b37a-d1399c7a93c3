$id: https://schema.multiverse.io/sync_session/registration_created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A message detailing information about a learner that has registered for a live session.
required:
  - registration_id
  - attendee_account_id
  - session_id
  - registered_at
properties:
  registration_id:
    type: string
    description: The unique ID of this registration
    format: uuid
  attendee_account_id:
    type: string
    description: The unique ID of the attendee associated with this registration
    format: uuid
  session_id:
    type: string
    description: The unique ID of the session associated with this registration
    format: uuid
  registered_at:
    type: string
    description: The time the attendee registered for the session
    format: date-time
