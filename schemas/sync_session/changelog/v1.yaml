$id: https://schema.multiverse.io/sync_session/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The sync session changelog message from Sync Sessions service.
required:
  - id
  - title
  - scheduled_for
  - duration_in_minutes
  - status
  - meeting_link
  - number_of_seats
  - session_template
  - company
  - meeting_id
  - creator_account_id
  - inserted_at
  - updated_at
  - hosts
properties:
  id:
    type: string
    description: The unique ID of this sync session
    format: uuid
  title:
    type: string
    description: The title of the sync session
    format: text
  scheduled_for:
    type: string
    description: The time the sync session is scheduled for
    format: date-time
  duration_in_minutes:
    type: integer
    description: The duration of the sync session in minutes
    format: integer
  status:
    type: string
    description: The current status of the sync session
    enum: ["scheduled", "cancelled"]
  meeting_link:
    type: string
    description: The link to join the sync session
    format: text
  number_of_seats:
    type: integer
    description: The number of seats available for the sync session
    format: integer
  session_template:
    type: object
    description: The session template that the sync session belongs to
    properties:
      id:
        type: string
        description: The ID of the session template that the sync session belongs to
        format: uuid
      title:
        type: string
        description: The title of the session template
        format: text
      unit_templates:
        type: array
        description: The unit templates associated with the session template
        items:
          type: string
          description: The id of the unit template
          format: uuid
    required:
      - id
      - title
      - unit_templates
  company:
    type: object
    description: The company that the sync session belongs to
    properties:
      id:
        type: string
        description: The ID of the company that the sync session belongs to
        format: uuid
      name:
        type: string
        description: The name of the company
        format: text
      status:
        type: string
        description: The current status of the company
        enum: ["active", "prospective"]
    required:
      - id
      - name
      - status
  meeting_id:
    type: string
    description: The ID of the meeting for the sync session
    format: text
  creator_account_id:
    type: string
    description: The creator account ID of the sync session
    format: uuid
  inserted_at:
    type: string
    description: The time the sync session was created at
    format: date-time
  updated_at:
    type: string
    description: The time the sync session was updated at
    format: date-time
  hosts:
    type: array
    description: The current hosts for the sync session
    items:
      type: object
      required:
        - id
        - type
      properties:
        id:
          type: string
          description: The account id of the host
          format: uuid
        type:
          type: string
          description: The role type of the host
          enum: ["instructor", "assistant"]