$id: https://schema.multiverse.io/sync_session/registration_changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A changelog message detailing information about a learner's registration for a live session.
required:
  - registration_id
  - attendee_account_id
  - session_id
  - attendee_email
  - status
  - inserted_at
  - updated_at
properties:
  registration_id:
    type: string
    description: The unique ID of this registration
    format: uuid
  attendee_account_id:
    type: string
    description: The unique ID of the attendee associated with this registration
    format: uuid
  attendee_email:
    type: string
    description: The email address of the attendee associated with this registration
    format: email
  session_id:
    type: string
    description: The unique ID of the session associated with this registration
    format: uuid
  status:
    type: string
    description: The status of the registration
    enum: ["registered", "cancelled"]
  goal_categories:
    type: array
    description: A list of goal categories that the attendee is interested in
    items:
      type: string
      enum: ["understand_key_concepts", "deepen_knowledge", "get_back_on_track", "get_help_with_project", "get_feedback_on_project", "help_with_using_tool", "no_specific_goal", "something_else"]
  goal_details:
    type: string
    description: A detailed description of the attendee's goals
    format: text
  inserted_at:
    type: string
    description: The time the attendee registered for the session
    format: date-time
  updated_at:
    type: string
    description: The time the registration was last updated
    format: date-time
