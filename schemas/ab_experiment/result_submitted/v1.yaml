$id: https://schema.multiverse.io/ab_experiment/result_submitted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: result of an experiment
required:
  - result_id
  - experiment_id
  - submitted_at
  - variant_id
  - outcome_description
properties:
  result_id:
    type:   string
    description: The unique identifier for the experiment result
    format: uuid
  experiment_id:
    type: string
    description: The unique identifier for the experiment
    format: uuid
  submitted_at:
    type: string
    description: The datetime the result was submitted
    format: date-time
  account_id:
    type: string
    format: uuid
    description: Unique identifier for an account used in the experiment
  session_id:
    type: string
    format: uuid
    description: Unique identifier for the session used in the experiment
  anonymous_id:
    type: string
    description: anonymous id for some anonymous field used in the experiment
  anonymous_id_definition:
    type: string
    description: The definition of the anonymous id, if used in the experiment
  variant_id:
    type: string
    description: Unique identifier for the variant used in the experiment
    format: uuid
  outcome_description:
    type: string
    description: Description of the outcome of the experiment
  outcome_integer:
    type: integer
    description: Integer value of the outcome of the experiment
  outcome_boolean:
    type: boolean
    description: Boolean value of the outcome of the experiment
  outcome_float:
    type: number
    description: Float value of the outcome of the experiment
  outcome_string:
    type: string
    description: String value of the outcome of the experiment
