$id: https://schema.multiverse.io/ab_experiment/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: an experiment event raised when a new experiment is created
required:
  - id
  - name
  - start_date
  - end_date
  - owner_email
  - variants
properties:
  id:
    type: string
    description: The unique identifier for the experiment
    format: uuid
  name:
    type: string
    description: The unique name of the experiment
  start_date:
    type: string
    description: The datetime the experiment starts
    format: date-time
  end_date:
    type: string
    description: The datetime the experiment ends
    format: date-time
  description:
    type: string
    description: Description of the experiment
  owner_email:
    type: string
    description: The email of the owner of the experiment
    format: email
  tags:
    type: array
    description: Tags of the experiment
    items:
      type: string
  variants:
    type: array
    description: The variants of the experiment
    items:
      type: object
      properties:
        variant_id: 
          type: string
          description: The unique identifier for the variant
          format: uuid
        variant_description:
          type: string
          description: The description of the variant
      required:
        - variant_id
        - variant_description
