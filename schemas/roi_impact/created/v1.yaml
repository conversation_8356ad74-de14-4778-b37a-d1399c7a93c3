$id: https://schema.multiverse.io/roi_impact/created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Event published whenever a new ROI impact story is stored in Atlas. This schema
  replaces `impact.created`.
type: object
required:
  - id
  - user_id
  - problem_addressed_description
  - implemented_solution_description
  - type_of_impact
  - stage_of_project
  - type_of_frequency_of_task
  - actual_frequency_of_type
  - summary_of_impact
  - created_at
properties:
  id:
    type: string
    format: uuid
  project_id:
    type: ["string", "null"]
    format: uuid
  project_submission_id:
    type: ["string", "null"]
    format: uuid
  problem_addressed_description:
    type: string
  implemented_solution_description:
    type: string
  type_of_impact:
    type: string
  stage_of_project:
    type: string
  type_of_frequency_of_task:
    type: string
  actual_frequency_of_type:
    type: number
  number_of_participants:
    type: integer
  hours_taken_before_per_frequency:
    type: number
  hours_taken_after_per_frequency:
    type: number
  cost_before_per_frequency:
    type: number
  cost_after_per_frequency:
    type: number
  cost_saved_per_frequency:
    type: number
  total_hours_saved_per_frequency:
    type: number
  summary_of_impact:
    type: string
  user_id:
    type: string
    format: uuid
  created_at:
    type: string
    format: date-time
