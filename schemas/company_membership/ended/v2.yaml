$id: https://schema.multiverse.io/company_membership/ended/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  Published whenever a membership between an account and company is ended
required:
  - account_id
  - company_id
  - end_date
  - reason
properties:
  account_id:
    type: string
    description: The account's id
    format: uuid
  company_id:
    type: string
    description: The company's id
    format: uuid
  end_date:
    type: string
    description: The end date of the membership
    format: date
  reason:
    type: string
    description: The reason for the membership ending
