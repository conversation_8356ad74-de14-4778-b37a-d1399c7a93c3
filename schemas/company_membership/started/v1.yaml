$id: https://schema.multiverse.io/company_membership/started/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  Published whenever a membership between an account and company is started
required:
  - account_id
  - company_id
  - start_date
properties:
  account_id:
    type: string
    description: The account's id
    format: uuid
  company_id:
    type: string
    description: The company's id
    format: uuid
  start_date:
    type: string
    description: The start date of the membership
    format: date
