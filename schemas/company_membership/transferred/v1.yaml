$id: https://schema.multiverse.io/company_membership/transferred/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  Published whenever an account's membership is transferred from one company to another
required:
  - account_id
  - from_company_id
  - to_company_id
  - transfer_date
  - reason
properties:
  account_id:
    type: string
    description: The account's id
    format: uuid
  from_company_id:
    type: string
    description: The id of the company the account was transferred from
    format: uuid
  to_company_id:
    type: string
    description: The id of the company the account was transferred to
    format: uuid
  transfer_date:
    type: string
    description: The date of the membership transfer
    format: date
  reason:
    type: string
    description: The reason for the membership transfer
