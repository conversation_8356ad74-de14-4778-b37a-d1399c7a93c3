$id: https://schema.multiverse.io/competency_grouping/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a competency grouping is updated
required:
  - id
  - name
  - competencies
  - created_at
  - updated_at
properties:
  id:
    description: The id of the competency grouping
    type: string
    format: uuid
  name:
    description: The name of the competency grouping
    type: string
  description:
    description: The description of the competency grouping
    type: string
  competencies:
    description: The list of competencies associated with the competency grouping
    type: array
    items:
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
  created_at:
    description: The time the competency grouping was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the competency grouping was updated in ISO8601 format
    type: string
    format: date-time
