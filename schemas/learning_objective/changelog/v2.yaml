$id: https://schema.multiverse.io/learning_objective/changelog/2.0.1
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a learning objective is updated
required:
  - id
  - version
  - title
  - competency_id
  - status
  - grading_criteria
  - updated_at
  - created_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the learning objective
  parent_id:
    type: string
    format: uuid
    description: The id of the previous version of the learning objective
  version:
    description: The version number. The first version of a learning objective is 1 and each subsequent version increments by 1
    type: integer
  title:
    description: The name of the learning objective
    type: string
  description:
    description: Optional description of the learning objective
    type: string
  grading_primer:
    description: Optional information for coaches and or AI on how to grade the learning objective e.g. definitions or terms
    type: string
  competency_id:
    description: The id of the competency that the learning objective belongs to
    type: string
    format: uuid
  status:
    description: The status of the learning objective either 'active' or 'deprecated'
    type: string
    enum: ["active", "deprecated"]
  grading_criteria:
    description: The list of grading criteria for the learning objective
    type: array
    items:
      type: object
      required:
        - id
        - criterion_text
        - target_grade
      properties:
        id:
          description: The id of the grading criterion
          type: string
          format: uuid
        criterion_text:
          description: The criterion text explaining how to meet or exceed the grading criterion
          type: string
        target_grade:
          description: The target grade of the grading criterion
          type: string
          enum: ["meets_expectations", "exceeds_expectations"]
  created_at:
    description: The time the learning objective was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the learning objective was updated in ISO8601 format
    type: string
    format: date-time
