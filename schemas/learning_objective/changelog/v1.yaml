$id: https://schema.multiverse.io/learning_objective/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a learning objective is updated
required:
  - id
  - name
  - competency_id
  - met_threshold
  - met_explanation
  - exceeded_threshold
  - exceeded_explanation
  - updated_at
  - created_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the learning objective
  name:
    description: The name of the learning objective
    type: string
  competency_id:
    description: The id of the competency that the learning objective belongs to
    type: string
    format: uuid
  met_threshold:
    description: The threshold required to meet the Learning Objective expectations
    type: number
    minimum: 0
    maximum: 100
  met_explanation:
    description: <PERSON><PERSON> facing explanation of what is required to meet the Learning Objective expectations
    type: string
  exceeded_threshold:
    description: The threshold required to exceed the Learning Objective expectations
    type: number
    minimum: 0
    maximum: 100
  exceeded_explanation:
    description: <PERSON><PERSON> facing explanation of what is required to exceed the Learning Objective expectations
    type: string
  created_at:
    description: The time the learning objective was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the learning objective was updated in ISO8601 format
    type: string
    format: date-time
  deleted_at:
    description: The time the learning objective was soft deleted in ISO8601 format
    type: string
    format: date-time
