$id: https://schema.multiverse.io/levy_programme_recommendations/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a levy learner's programme recommendations have been created or updated.
required:
  - account_id
  - user_skills_scan_id
  - recommended_programmes
  - rejection_reasons
properties:
  account_id:
    description: The account id of the user.
    type: string
    format: uuid
  user_skills_scan_id:
    description: The corresponding skills scan survey instance id.
    type: string
    format: uuid
  rejection_reasons:
    type: array
    description: The list of reasons why the user is not eligible for the recommended programmes
    items:
      type: string
  recommended_programmes:
    type: array
    description: The list of programmes the user can be recommended
    items:
      type: object
      required:
        - apprenticeship_qualification_id
      properties:
        apprenticeship_qualification_id:
          type: string
          format: uuid
          description: The id of the programme
