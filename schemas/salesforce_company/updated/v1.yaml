$id: https://schema.multiverse.io/salesforce_company/updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The company changed event emitted when we receive an account change event from Salesforce
required:
  - id
  - company
properties:
  id:
    type: string
    description: The salesforce id of the company
  company:
    type: object
    properties:
      parent_salesforce_id:
        description: The ID of the parent company
        type:
          - string
          - "null"
      salesforce_id:
        type: string
      name:
        type: string
      url:
        type:
          - string
          - "null"
        description: The URL for the company website
      address_line_1:
        type:
          - string
          - "null"
      postcode:
        type:
          - string
          - "null"
      region:
        type: string
        enum:
          - uk
          - us
          - other
      number_of_deals:
        type: number
      ai_opt_out:
        type: boolean
    required:
      - salesforce_id
