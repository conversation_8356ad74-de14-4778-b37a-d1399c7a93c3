$id: https://schema.multiverse.io/salesforce_company/created/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The company created event emitted when we receive an account creation event from Salesforce
required:
  - company
properties:
  company:
    type: object
    properties:
      parent_salesforce_id:
        description: The ID of the parent company
        type:
          - string
          - "null"
      salesforce_id:
        type: string
      name:
        type: string
      url:
        type:
          - string
          - "null"
        description: The URL for the company website
      address_line_1:
        type:
          - string
          - "null"
      postcode:
        type:
          - string
          - "null"
      region:
        type: string
        enum:
          - uk
          - us
          - other
      ai_opt_out:
        type: boolean
    required:
      - parent_salesforce_id
      - salesforce_id
      - name
      - status
      - url
      - address_line_1
      - postcode
      - region
      - ai_opt_out
