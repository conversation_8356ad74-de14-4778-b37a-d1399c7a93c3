$id: https://schema.multiverse.io/salesforce_company/created/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The company created event emitted when we receive an account creation event from Salesforce
required:
  - id
  - company
properties:
  id:
    type: string
    description: The salesforce id of the company
  company:
    type: object
    properties:
      parent_salesforce_id:
        description: The ID of the parent company
        type:
          - string
          - "null"
      salesforce_id:
        type: string
      name:
        type: string
      url:
        type:
          - string
          - "null"
        description: The URL for the company website
      address_line_1:
        type:
          - string
          - "null"
      postcode:
        type:
          - string
          - "null"
      region:
        type: string
        enum:
          - uk
          - us
          - other
      number_of_deals:
        type: number
      ai_opt_out:
        type: boolean
      occurred_at:
        type: string
        format: date-time
        description: The time when this event happened in Salesforce
    required:
      - parent_salesforce_id
      - salesforce_id
      - name
      - url
      - address_line_1
      - postcode
      - region
      - number_of_deals
      - ai_opt_out
      - occurred_at
