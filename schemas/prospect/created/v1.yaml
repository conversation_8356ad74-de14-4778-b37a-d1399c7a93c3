$id: https://schema.multiverse.io/prospect/created/1.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a new prospect is created
required:
  - id
  - first_name
  - last_name
  - email
  - company_uuid
  - signup_source
  - created_at
properties:
  id:
    type: string
    description: The id of the prospect
    format: uuid
  first_name:
    type: string
    description: The first name of the prospect
  last_name:
    type: string
    description: The last name of the prospect
  email:
    type: string
    description: The email address of the prospect
  job_title:
    type: string
    description: The job title of the prospect
  org_level_1:
    type: string
    description: The first level of the prospect's organization
  org_level_2:
    type: string
    description: The second level of the prospect's organization
  org_level_3:
    type: string
    description: The third level of the prospect's organization
  line_manager_email:
    type: string
    description: The email address of the prospect's line manager
  company_uuid:
    type: string
    description: The uuid of the company the prospect belongs to
    format: uuid
  phone_number:
    type: string
    description: The phone number of the prospect
  country:
    type: string
    description: The country of the prospect
  signup_source:
    type: string
    description: If the prospect signed up through a skills survey, prospect interest form, or info session
  form_with_info_session_dates:
    type: boolean
    description: Whether the prospect signed up through a form with info session dates
  invite_to_apply:
    type: boolean
    description: Whether the prospect was invited to apply after signing up
  prospect_interest_form_id:
    type: string
    description: The uuid of the prospect interest form the prospect signed up through
    format: uuid
  created_at:
    type: string
    description: The date and time the prospect was created
    format: date-time
