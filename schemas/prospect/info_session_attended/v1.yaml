$id: https://schema.multiverse.io/prospect/info_session_attended/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a prospect has attended an info session
required:
  - prospect_id
  - info_session_id
  - attended_at
properties:
  prospect_id:
    type: string
    description: The id of the prospect
    format: uuid
  info_session_id:
    type: string
    description: The id of the info session
    format: uuid
  external_meeting:
    type: object
    description: The external meeting details if the info session is hosted on a 3rd party platform, e.g. Teams
    properties:
      external_meeting_id:
        type: string
        description: The id of the external meeting from the hosting provider
      external_hosting_provider:
        type: string
        enum: ["teams", "zoom", "google_meet"]
        description: The hosting provider of the external meeting, e.g. teams
  attended_at:
    type: string
    description: The date and time the prospect first joined the info session
    format: date-time
