$id: https://schema.multiverse.io/project_review/submitted/2.3.1
$schema: https://json-schema.org/draft/2020-12/schema
description: The project review message from Ariel
type: object
required:
  - id
  - project_id
  - project_type
  - project_submission_id
  - learner_account_id
  - reviewer_account_id
  - project_review_submitted_at
oneOf:
  - required: [ksbs]
  - required: [learning_objectives]
properties:
  id:
    type: string
    description: The project review id
    format: uuid
  project_id:
    type: string
    description: The project id
    format: uuid
  project_type:
    type: string
    description: The project type.  A project of type "levy" has KSBs reviewed, one of type "saas" has Learning Objectives reviewed.
    enum: ["levy", "saas"]
  project_submission_id:
    type: string
    description: The project submission id
    format: uuid
  learner_account_id:
    type: string
    description: The project submitter account id
    format: uuid
  reviewer_account_id:
    type: string
    description: The project reviewer account id
    format: uuid
  project_review_submitted_at:
    description: The time when the review was submitted in ISO8601 format
    type: string
    format: date-time
  additional_notes:
    type: string
    description: This is the project level feedback on the review
    format: text
  submission_result_id:
    type: ["string", "null"]
    description: The ID of the AI grading result shown to the reviewer
    format: uuid
  ksbs:
    type: array
    description: List of KSBs and their scores. There will be either ksbs or learning_objectives, but not both.
    items:
      type: object
      required:
        - id
        - score
        - assessment_summary
      properties:
        id:
          type: string
          description: The id of the KSB
          format: uuid
        assessment_summary:
          type: string
          description: The assessment of the KSB
          enum: ["not_met", "met", "exceeded"]
        score:
          type: number
          description: The score for the KSB
          minimum: 0
          maximum: 100
        grading_primer:
          type: string
          description: Text field containing the metric used to evaluate a grade.
          format: text
        feedback:
          type: string
          description: Feedback attached to project review KSB.
          format: text
        assessed_grading_criteria:
          type: array
          description: List of Grading Criteria assessed and their assessment status
          items:
            type: object
            required:
              - id
              - criterion_target_grade
              - assessment
              - criterion_text
            properties:
              id:
                type: string
                description: The id of the Grading Criterion
                format: uuid
              criterion_target_grade:
                type: string
                description: The category the Grading Criterion belongs to
                enum: ["MEETS_EXPECTATIONS", "EXCEEDS_EXPECTATIONS"]
              assessment:
                type: string
                description: The assessment made by the reviewer on the Grading Criterion
                enum: ["not_fulfilled", "fulfilled"]
              criterion_text:
                type: string
                description: The grading criterion text for the KSB
                format: text
  learning_objectives:
    type: array
    description: List of Learning Objectives with their assessment by the reviewer. There will be either ksbs or learning_objectives, but not both.
    items:
      type: object
      required:
        - id
        - assessment_summary
      properties:
        id:
          type: string
          description: The id of the Learning Objective
          format: uuid
        assessment_summary:
          type: string
          description: The assessment of the Learning Objective
          enum: ["not_met", "met", "exceeded"]
        grading_primer:
          type: string
          description: Text field containing the metric used to evaluate a grade.
          format: text
        feedback:
          type: string
          description: Feedback attached to project review Learning Objective.
          format: text
        assessed_grading_criteria:
          type: array
          description: List of Grading Criteria assessed and their assessment status
          items:
            type: object
            required:
              - id
              - criterion_target_grade
              - assessment
              - criterion_text
            properties:
              id:
                type: string
                description: The id of the Grading Criterion
                format: uuid
              criterion_target_grade:
                type: string
                description: The category the Grading Criterion belongs to
                enum: ["MEETS_EXPECTATIONS", "EXCEEDS_EXPECTATIONS"]
              assessment:
                type: string
                description: The assessment made by the reviewer on the Grading Criterion
                enum: ["not_fulfilled", "fulfilled"]
              criterion_text:
                type: string
                description: The grading criterion text for the Learning Objective
                format: text
