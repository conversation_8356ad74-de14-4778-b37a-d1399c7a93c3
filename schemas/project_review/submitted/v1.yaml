$id: https://schema.multiverse.io/project_review/submitted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: The project review message from Ariel
type: object
required:
  - id
  - project_id
  - project_submission_id
  - submitter_account_id
  - reviewer_account_id
  - submitted_at
  - ksbs
properties:
  id:
    type: string
    description: The project review id
    format: uuid
  project_id:
    type: string
    description: The project id
    format: uuid
  project_submission_id:
    type: string
    description: The project submission id
    format: uuid
  submitter_account_id:
    type: string
    description: The project submitter account id
    format: uuid
  reviewer_account_id:
    type: string
    description: The project reviewer account id
    format: uuid
  submitted_at:
    description: The time when the review was submitted in ISO8601 format
    type: string
    format: date-time
  ksbs:
    type: array
    description: List of KSBs and their scores
    items:
      type: object
      required:
        - id
        - score
      properties:
        id:
          type: string
          description: The id of the KSB
          format: uuid
        score:
          type: number
          description: The score for the KSB
          minimum: 0
          maximum: 100
