$id: https://schema.multiverse.io/prospect_interest/changelog/1.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a prospect interest form is modified
required:
  - id
  - company_uuid
  - title
  - info_sessions
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the prospect interest form
    format: uuid
  company_uuid:
    type: string
    description: The uuid of the company associated with the prospect interest form
    format: uuid
  title:
    type: string
    description: The title of the form
  closed_at:
    type: string
    description: The date and time the form was closed
    format: date-time
  deadline:
    type: string
    description: The deadline of the form
    format: date-time
  info_sessions:
    type: array
    description: The info sessions added to the prospect interest form
    items:
      type: object
      properties:
        id:
          type: string
          description: The id of the info session
          format: uuid
        name:
          type: string
          description: The name of the info session
        description:
          type: string
          description: The description of the info session
        company_uuid:
          type: string
          description: The uuid of the company
          format: uuid
        start_time:
          type: string
          description: The start time of the info session
          format: date-time
        duration:
          type: number
          description: The duration of the info session
        video_call_url:
          type: string
          description: The video call url of the info session
        additional_invited_emails:
          type: array
          description: The additional invited emails of the info session
          items:
            type: string
            description: The email of the additional invited email
        host_emails:
          type: array
          description: The host emails of the info session
          items:
            type: string
            description: The email of the host email
        inserted_at:
          type: string
          description: The date and time the info session was created
          format: date-time
        updated_at:
          type: string
          description: The date and time the info session was updated
          format: date-time
  skills_survey_id:
    type: string
    description: The id of the skills survey
  inserted_at:
    type: string
    description: The date and time the form was created
    format: date-time
  updated_at:
    type: string
    description: The date and time the form was updated
    format: date-time
