$id: https://schema.multiverse.io/prospect_interest/form_created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a prospect interest form is created
required:
  - id
  - company_uuid
  - title
  - created_at
properties:
  id:
    type: string
    description: The id of the prospect interest form
    format: uuid
  company_uuid:
    type: string
    description: The uuid of the company associated with the prospect interest form
    format: uuid
  title:
    type: string
    description: The title of the form
  created_at:
    type: string
    description: The date and time the form was created
    format: date-time
