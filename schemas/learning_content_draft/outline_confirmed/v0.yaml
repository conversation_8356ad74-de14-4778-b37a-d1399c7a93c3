$id: https://schema.multiverse.io/learning_content_draft/outline_confirmed/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Event published whenever a unit outline is confirmed in the unit drafting service and content has begun to be created
type: object
required:
  - id
  - state
  - updatedAt
properties:
  id:
    type: string
    format: uuid
  state:
    type: string
    enum: ["outlined", "content", "confirmed"]
  updatedAt:
    type: string
    format: date-time
