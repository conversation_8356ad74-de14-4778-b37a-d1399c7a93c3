$id: https://schema.multiverse.io/learning_content_draft/created/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Event published whenever a unit is initially drafted in the unit drafting service
type: object
required:
  - id
  - title
  - summary
  - createdById
  - state
  - createdAt
properties:
  id:
    type: string
    format: uuid
  title:
    type: string
  summary:
    type: string
  createdById:
    type: string
    format: uuid
  state:
    type: string
    enum: ["outlined", "content", "confirmed"]
  createdAt:
    type: string
    format: date-time
