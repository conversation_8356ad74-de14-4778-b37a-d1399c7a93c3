$id: https://schema.multiverse.io/learning_content_draft/content_exported/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Event published whenever a unit is exported from the unit drafting service
type: object
required:
  - id
  - content
  - exportedAt
properties:
  id:
    type: string
    format: uuid
  content:
    type: object
    required:
      - chapters
    properties:
      chapters:
        type: array
        items:
          type: object
          required:
            - title
            - sections
          properties:
            title:
              type: string
            sections:
              type: array
              items:
                type: object
                required:
                  - title
                  - sectionType
                  - body
                properties:
                  title:
                    type: string
                  sectionType:
                    type: string
                    enum: ["Introduction", "Instruction", "Conclusion"]
                  learningObjective:
                    type: string
                    description: The learning objective of the section if it is an Instruction section
                  body:
                    type: string
  exportedAt:
    type: string
    format: date-time
