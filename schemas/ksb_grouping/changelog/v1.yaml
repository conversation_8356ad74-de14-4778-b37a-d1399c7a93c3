$id: https://schema.multiverse.io/ksb_grouping/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a KSB grouping is updated
required:
  - id
  - name
  - ksbs
  - created_at
  - updated_at
properties:
  id:
    description: The id of the KSB grouping
    type: string
    format: uuid
  name:
    description: The name of the KSB grouping
    type: string
  description:
    description: The description of the KSB grouping
    type: string
  ksbs:
    description: The list of KSBs associated with the KSB grouping
    type: array
    items:
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the KSB
          type: string
          format: uuid
  created_at:
    description: The time the KSB grouping was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the KSB grouping was updated in ISO8601 format
    type: string
    format: date-time
