$id: https://schema.multiverse.io/authentication/session_updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - account_id
  - session_id
  - timestamp
properties:
  account_id:
    type: string
    description: The account id
    format: uuid
  session_id:
    type: string
    description: The session id
    format: uuid
  access_level:
    type: string
    description: The updated access level of the session. A session is restricted when a user hasn't completed certain onboarding steps (e.g. email verification, agreed to terms of service, etc.)
    enum:
      - default
  timestamp:
    type: string
    description: When the session was updated
    format: date-time
