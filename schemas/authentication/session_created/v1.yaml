$id: https://schema.multiverse.io/authentication/session_created/1.1.1
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - account_id
  - session_id
  - timestamp
properties:
  account_id:
    type: string
    description: The account id
    format: uuid
  session_id:
    type: string
    description: The session id
    format: uuid
  timestamp:
    type: string
    description: When the session was created
    format: date-time
  by_application:
    type: string
    description: Which application created the session, e.g. Ariel, Platform
    format: text
  access_level:
    type: ["string", "null"]
    description: The access level of the session. A session is restricted when a user hasn't completed certain onboarding steps (e.g. email verification, agreed to terms of service, etc.). When access_level is restricted, the session is only readable by the authentication and account_hub services.
    enum:
      - default
      - restricted