$id: https://schema.multiverse.io/learning_domain/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a learning domain is updated
required:
  - id
  - learning_uid
  - title
  - competency_modules
  - created_at
  - updated_at
properties:
  id:
    description: The id of the learning domain
    type: string
    format: uuid
  learning_uid:
    description: |
      A manually created internal facing unique identifier for the learning
      domain. Learning uses these to cross-reference and identify learning
      domains.
    type: string
  title:
    description: The title of the learning domain
    type: string
  competency_modules:
    description: |
      The competency modules making up this learning domain. The order is this
      list is significant as it reflects the order in which these competency
      modules should be learnt.  However ordering between modules with the same
      earning_uid is not significant as they are alternatives to each
      other.
    type: array
    items:
      uniqueItems: true
      type: object
      required:
        - id
        - learning_uid
      properties:
        id:
          description: The id of the competency module
          type: string
          format: uuid
        learning_uid:
          description: |
            the manually created learning id of the module (can be shared
            between modules that teach the same thing)
          type: string
  created_at:
    description: The time the learning domain was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the learning domain was updated in ISO8601 format
    type: string
    format: date-time
  deleted_at:
    description: The time the learning domain was soft deleted in ISO8601 format
    type: string
    format: date-time
