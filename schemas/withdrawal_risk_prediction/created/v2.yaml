$id: https://schema.multiverse.io/withdrawal_risk_prediction/created/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A prediction made by the risk model about an apprentice's likelihood of withdrawal.
required:
  - apprenticeship_uuid
  - model_version
  - days_on_programme_checkpoint
  - prediction
  - predicted_at
  - shapley_values
properties:
  apprenticeship_uuid:
    type: string
    description: The UUID of the apprenticeship that the prediction corresponds to.
    format: uuid
  model_version:
    type: integer
    description: The version of the model that made the prediction.
    const: 2
  days_on_programme_checkpoint:
    type: integer
    description: The number of days that the apprentice has been on programme.
  prediction:
    type: object
    description: The overall risk value as a percentage and its associated risk bucket.
    properties:
      value:
        type: integer
        minimum: 0
        maximum: 100
      bucket:
        type: object
        properties:
          version:
            type: integer
          value:
            type: string
        required:
          - version
          - value
    required:
      - value
      - bucket
  predicted_at:
    type: string
    description: When the prediction was made.
    format: date-time
  shapley_values:
    type: object
    description: The factors that have affected the withdrawal risk and their effect.
    properties:
      age:
        type: integer
      aln_level_high_plus:
        type: integer
      app_manager_support_score:
        type: integer
      bil_counts:
        type: integer
      cohort_type_open:
        type: integer
      delivery_session_attendance_frac:
        type: integer
      fs_english_decision_approve_but_exam_required:
        type: integer
      fs_english_decision_approve_but_provisional:
        type: integer
      fs_english_decision_approve_but_waiting_on_certs:
        type: integer
      fs_english_required:
        type: integer
      fs_maths_decision_approve_but_exam_required:
        type: integer
      fs_maths_decision_approve_but_provisional:
        type: integer
      fs_maths_decision_approve_but_waiting_on_certs:
        type: integer
      fs_maths_required:
        type: integer
      ilj_admissions_type_existing_employee:
        type: integer
      ilj_admissions_type_hired_by_mv:
        type: integer
      ilj_aln_social_emotional_social/emotional:
        type: integer
      ilj_ethnicity_group_asian:
        type: integer
      ilj_ethnicity_group_black:
        type: integer
      ilj_ethnicity_group_multi-racial:
        type: integer
      ilj_ethnicity_group_others:
        type: integer
      ilj_ethnicity_group_white:
        type: integer
      ilj_gender_name_man:
        type: integer
      ilj_gender_name_woman:
        type: integer
      monthly_fb_coach_expertise:
        type: integer
      monthly_fb_nps_wh:
        type: integer
      monthly_fb_wellbeing_feeling:
        type: integer
      otj_actual_vs_target:
        type: integer
      otj_log_count:
        type: integer
      otj_type_training_at_your_work:
        type: integer
      programme_group_business:
        type: integer
      programme_group_data_fellowship:
        type: integer
      programme_group_data_literacy:
        type: integer
      programme_group_leadership:
        type: integer
      programme_group_management:
        type: integer
      programme_group_marketing:
        type: integer
      programme_group_swe:
        type: integer
      total_bil_duration:
        type: integer
      total_events:
        type: integer
      total_non_events:
        type: integer
      total_visits:
        type: integer
