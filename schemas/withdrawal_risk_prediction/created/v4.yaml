$id: https://schema.multiverse.io/withdrawal_risk_prediction/created/4.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A prediction made by the risk model about an apprentice's likelihood of withdrawal.
required:
  - apprenticeship_uuid
  - model_version
  - days_on_programme_checkpoint
  - prediction
  - predicted_at
  - shapley_values
properties:
  apprenticeship_uuid:
    type: string
    description: The UUID of the apprenticeship that the prediction corresponds to.
    format: uuid
  model_version:
    type: integer
    description: The version of the model that made the prediction.
  days_on_programme_checkpoint:
    type: integer
    description: The number of days that the apprentice has been on programme.
  prediction:
    type: object
    description: The overall risk value as a percentage and its associated risk bucket ID.
    properties:
      value:
        type: integer
        minimum: 0
        maximum: 100
      bucket_id:
        type: integer
    required:
      - value
      - bucket_id
  predicted_at:
    type: string
    description: When the prediction was made.
    format: date-time
  shapley_values:
    type: object
    description: The factors that have affected the withdrawal risk and their effect.
    properties:
      aln_level_high_plus:
        type: integer
        name: Additional Learning Needs
        description: Learners with one or more additional learning needs may be at higher risk of withdrawal. Make sure the learner is support on programme and refer to the Mulitverse support team if appropriate.
      aln_social_emotional:
        type: integer
        name: Social/Emotional ALN
        description: Learners with social/emotional additional learning needs may be at higher risk of withdrawal. Make sure the learner is support on programme and refer to the Mulitverse support team if appropriate.
      apply_learning:
        type: integer
        name: OTJ Type - Applying Learning To Work
        description: Learners who log too much OTJ of this type may be at an increased risk of withdrawal. Check the learner has good role fit and they're engaging with the content in a variety of different ways.
      attendance_frac:
        type: integer
        name: Delivery Attendance %
        description: Learners with lower attendance may be at an increased risk of withdrawal. Check the learner is attending all sessions and understands the requirements of the programme.
      cohort_type_closed:
        type: integer
        name: Cohort Type - Closed
        description: Learners in closed cohorts are more likely to withdraw. Check the learner has the right manager support, role fit, and understands the expectations of the programme.
      community_events:
        type: integer
        name: Total Community Events
        description: Learners who do not engage with community events are more likely to withdraw. Ensure the learner is aware of the full Multiverse offering.
      community_visits:
        type: integer
        name: Total Community Visits
        description: Learners who do not engage with community events are more likely to withdraw. Ensure the learner is aware of the full Multiverse offering.
      company_employee_count:
        type: integer
        name: Employer Size
        description: Learners from really small or really large companies are more likely to withdraw. Check the learner has the right support from their manager and role fit to succeed on programme.
      feeling:
        type: integer
        name: Wellbeing Score
        description: Learners with low wellbeing scores from the coaching survey are more likely to withdraw. Make sure the learner is support on programme and refer to the Mulitverse support team if appropriate.
      fs_english_required:
        type: integer
        name: Functional Skills - English Required
        description: Learners without English functional skills are more likely to withdraw. Support the learner to provide certificates, explain the functional skills process and direct them to reach out to support.
      fs_maths_required:
        type: integer
        name: Functional Skills - Maths Required (Admissions)
        description: Learners without Maths functional skills are more likely to withdraw. Support the learner to provide certificates, explain the functional skills process and direct them to reach out to support.
      ilj_admissions_type_existing_employee:
        type: integer
        name: Existing Employee (Career Builder)
        description: Learners who are hired by their employer may have an increased early withdrawal risk. Check that the learner has the right manager support and role fit to succeed on programme.
      ilj_admissions_type_hired_by_employer:
        type: integer
        name: Apprentice Hired By Employer (Career Builder)
        description: Learners who are hired by their employer may have an increased early withdrawal risk. Check that the learner has the right manager support and role fit to succeed on programme.
      ilj_admissions_type_hired_by_mv:
        type: integer
        name: Apprentice Hired By MV (Career Starter)
        description: Learners who are hired by Multiverse have a reduced withdrawal risk. Check that the learner has the right manager support and role fit to succeed on programme.
      nps:
        type: integer
        name: NPS Score
        description: Learners with low NPS scores from the coaching survey are more likely to withdraw. Make sure the learner is supported on programme.
      otj_actual_vs_target:
        type: integer
        name: OTJ %
        description: Learners who are not meeting their OTJ target of 100% are more likely to withdraw. Support the learner to find opportunities to apply their learning and understands the programme requirements.
      otj_log_count:
        type: integer
        name: OTJ Log Count
        description: Learners who log their OTJ infrequently or in big chunks are more likely to withdraw. Support the learner to find opportunities to apply their learning and understands the programme requirements.
      otj_type_applying_learning_to_work:
        type: integer
        name: OTJ Type - Applying Learning To Work
        description: Learners who log to much OTJ of 'applied learning' may be at an increased risk of withdrawal. Check the learner has good role fit and they're engaging with the content in a variety of different ways.
      otj_type_training_at_your_work:
        type: integer
        name: OTJ Type - Training At Work
        description: Learners who don't OTJ of 'training at work' may be at an increased risk of withdrawal. Check the learner has good role fit and are supported to apply their new skills at work.
      prior_learning_l4_plus:
        type: integer
        name: Prior L4 Qualification
        description: Learners with previous qualifications may be at higher risk of withdrawal. Check the programme is aligned with the learner's goals and can be stretched if they feel comfortable with the content.
      programme_group_business:
        type: integer
        name: Programme Group - Business
        description: The programme group the learner's programme belongs to. Certain programmes may increase/reduce early withdrawal risk. Including the programme allows for the model to contextualise the other data points better. No coach action required.
      programme_group_data_fellowship:
        type: integer
        name: Programme Group - Data Fellowship
        description: The programme group the learner's programme belongs to. Certain programmes may increase/reduce early withdrawal risk. Including the programme allows for the model to contextualise the other data points better. No coach action required.
      programme_group_data_literacy:
        type: integer
        name: Programme Group - Data Literacy
        description: The programme group the learner's programme belongs to. Certain programmes may increase/reduce early withdrawal risk. Including the programme allows for the model to contextualise the other data points better. No coach action required.
      programme_group_management:
        type: integer
        name: Programme Group - Management
        description: The programme group the learner's programme belongs to. Certain programmes may increase/reduce early withdrawal risk. Including the programme allows for the model to contextualise the other data points better. No coach action required.
      programme_group_swe:
        type: integer
        name: Programme Group - Software Engineering
        description: The programme group the learner's programme belongs to. Certain programmes may increase/reduce early withdrawal risk. Including the programme allows for the model to contextualise the other data points better. No coach action required.
      total_bil_count:
        type: integer
        name: Total BILs
        description: Learners who have been on one or more break in learning's (BILs) in the past are more likely to withdraw. Check the learner is supported and has caught up since their BIL.
      total_bil_duration_days:
        type: integer
        name: Total BIL Days
        description: Learners who have been on break in learning's (BILs) for a longer period of time in the past are more likely to withdraw. Check the learner is supported and has caught up since their BIL.
