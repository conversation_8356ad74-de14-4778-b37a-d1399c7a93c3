$id: https://schema.multiverse.io/withdrawal_risk_prediction/created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A prediction made by the risk model about an apprentice's likelihood of withdrawal.
required:
  - apprenticeship_uuid
  - model_version
  - days_on_programme_checkpoint
  - prediction
  - predicted_at
  - shapley_values
properties:
  apprenticeship_uuid: 
    type: string
    description: The UUID of the apprenticeship that the prediction corresponds to.
    format: uuid
  model_version:
    type: integer
    description: The version of the model that made the prediction.
  days_on_programme_checkpoint:
    type: integer
    description: The number of days that the apprentice has been on programme.
  prediction: 
    type: object
    description: The overall risk value as a percentage and its associated risk bucket.
    properties:
      value:
        type: integer
        minimum: 0
        maximum: 100
      bucket:
        type: object
        properties:
          version:
            type: integer
          value:
            type: string
        required:
          - version
          - value
    required:
      - value
      - bucket
  predicted_at:
    type: string
    description: When the prediction was made.
    format: date-time
  shapley_values:
    type: object
    description: The factors that have affected the withdrawal risk and their effect.
    properties:
      fs_maths_action_required:
        type: integer
      fs_english_action_required:
        type: integer
      aln_level_high_plus:
        type: integer
      prior_qual_l4_plus:
        type: integer
      bil_boolean_pre_45:
        type: integer
      otj_above_target_pre_45_days:
        type: integer
      age:
        type: integer
      most_complete_company_uk_size:
        type: integer
      total_visits_pre_45_days:
        type: integer
      total_posts_pre_45_days:
        type: integer
      total_events_pre_45_days:
        type: integer
      otj_log_count_pre_45_days:
        type: integer
      otj_actual_vs_target_pre_45_days:
        type: integer
      otj_type_applying_apprenticeship_learning_to_work_pre_45_days:
        type: integer
      otj_type_training_at_your_work_pre_45_days:
        type: integer
      ilj_gender_name_man:
        type: integer
      ilj_gender_name_woman:
        type: integer
      ilj_ethnicity_group_asian:
        type: integer
      ilj_ethnicity_group_black:
        type: integer
      ilj_ethnicity_group_multi-racial:
        type: integer
      ilj_ethnicity_group_others:
        type: integer
      ilj_ethnicity_group_white:
        type: integer
      programme_group_business:
        type: integer
      programme_group_data_fellowship:
        type: integer
      programme_group_data_literacy:
        type: integer
      programme_group_leadership:
        type: integer
      programme_group_management:
        type: integer
      programme_group_marketing:
        type: integer
      programme_group_swe:
        type: integer
      ilj_aln_social_emotional_social/emotional:
        type: integer
      ilj_admissions_type_existing_employee:
        type: integer
      ilj_admissions_type_hired_by_mv:
        type: integer
      cohort_type_closed:
        type: integer
      cohort_type_open:
        type: integer
      fs_maths_decision_approve:
        type: integer
      fs_maths_decision_approve_but_waiting_on_certs:
        type: integer
      fs_english_decision_approve:
        type: integer
      fs_english_decision_approve_but_waiting_on_certs:
        type: integer
      fs_english_decision_reject:
        type: integer
      bil_boolean_pre_90:
        type: integer
      otj_above_target_pre_90_days:
        type: integer
      non_events_pre_90_days_flag:
        type: integer
      total_visits_pre_90_days:
        type: integer
      total_posts_pre_90_days:
        type: integer
      total_events_pre_90_days:
        type: integer
      otj_log_count_pre_90_days:
        type: integer
      otj_actual_vs_target_pre_90_days:
        type: integer
      otj_type_applying_apprenticeship_learning_to_work_pre_90_days:
        type: integer
      otj_type_training_at_your_work_pre_90_days:
        type: integer
      total_non_events_pre_90_days:
        type: integer
      monthly_fb_coach_connection_pre_90_days:
        type: integer
      monthly_fb_coach_expertise_pre_90_days:
        type: integer
      monthly_fb_nps_wh_pre_90_days:
        type: integer
      monthly_fb_wellbeing_feeling_pre_90_days:
        type: integer
      bil_boolean_pre_135:
        type: integer
      otj_above_target_pre_135_days:
        type: integer
      non_events_pre_135_days_flag:
        type: integer
      total_visits_pre_135_days:
        type: integer
      total_posts_pre_135_days:
        type: integer
      total_events_pre_135_days:
        type: integer
      otj_log_count_pre_135_days:
        type: integer
      otj_actual_vs_target_pre_135_days:
        type: integer
      otj_type_applying_apprenticeship_learning_to_work_pre_135_days:
        type: integer
      otj_type_training_at_your_work_pre_135_days:
        type: integer
      total_non_events_pre_135_days:
        type: integer
      monthly_fb_coach_connection_pre_135_days:
        type: integer
      monthly_fb_coach_expertise_pre_135_days:
        type: integer
      monthly_fb_nps_wh_pre_135_days:
        type: integer
      monthly_fb_wellbeing_feeling_pre_135_days:
        type: integer
      bil_boolean_pre_180:
        type: integer
      otj_above_target_pre_180_days:
        type: integer
      non_events_pre_180_days_flag:
        type: integer
      total_visits_pre_180_days:
        type: integer
      total_posts_pre_180_days:
        type: integer
      total_events_pre_180_days:
        type: integer
      otj_log_count_pre_180_days:
        type: integer
      otj_actual_vs_target_pre_180_days:
        type: integer
      otj_type_applying_apprenticeship_learning_to_work_pre_180_days:
        type: integer
      otj_type_training_at_your_work_pre_180_days:
        type: integer
      total_non_events_pre_180_days:
        type: integer
      monthly_fb_coach_connection_pre_180_days:
        type: integer
      monthly_fb_coach_expertise_pre_180_days:
        type: integer
      monthly_fb_nps_wh_pre_180_days:
        type: integer
      monthly_fb_wellbeing_feeling_pre_180_days:
        type: integer
      bil_boolean_pre_225:
        type: integer
      otj_above_target_pre_225_days:
        type: integer
      non_events_pre_225_days_flag:
        type: integer
      total_visits_pre_225_days:
        type: integer
      total_posts_pre_225_days:
        type: integer
      total_events_pre_225_days:
        type: integer
      otj_log_count_pre_225_days:
        type: integer
      otj_actual_vs_target_pre_225_days:
        type: integer
      otj_type_applying_apprenticeship_learning_to_work_pre_225_days:
        type: integer
      otj_type_training_at_your_work_pre_225_days:
        type: integer
      total_non_events_pre_225_days:
        type: integer
      monthly_fb_coach_connection_pre_225_days:
        type: integer
      monthly_fb_coach_expertise_pre_225_days:
        type: integer
      monthly_fb_nps_wh_pre_225_days:
        type: integer
      monthly_fb_wellbeing_feeling_pre_225_days:
        type: integer
      bil_boolean_pre_270:
        type: integer
      otj_above_target_pre_270_days:
        type: integer
      non_events_pre_270_days_flag:
        type: integer
      total_visits_pre_270_days:
        type: integer
      total_posts_pre_270_days:
        type: integer
      total_events_pre_270_days:
        type: integer
      otj_log_count_pre_270_days:
        type: integer
      otj_actual_vs_target_pre_270_days:
        type: integer
      otj_type_applying_apprenticeship_learning_to_work_pre_270_days:
        type: integer
      otj_type_training_at_your_work_pre_270_days:
        type: integer
      total_non_events_pre_270_days:
        type: integer
      monthly_fb_coach_connection_pre_270_days:
        type: integer
      monthly_fb_coach_expertise_pre_270_days:
        type: integer
      monthly_fb_nps_wh_pre_270_days:
        type: integer
      monthly_fb_wellbeing_feeling_pre_270_days:
        type: integer
