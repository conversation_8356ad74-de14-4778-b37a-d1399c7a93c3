$id: https://schema.multiverse.io/apprenticeship/training_plan_completed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Event triggered after the successful completion of a training plan.
required:
  - account_id
  - apprenticeship_id
  - completed_at
  - pandadoc_document_id
properties:
  account_id:
    type: string
    description: The account uuid of the apprentice
    format: uuid
  apprenticeship_id:
    type: string
    description: The uuid of the apprenticeship
    format: uuid
  completed_at:
    type: string
    description: The date and time the training plan was completed
    format: date-time
  pandadoc_document_id:
    type: string
    description: The id of the PandaDoc document
