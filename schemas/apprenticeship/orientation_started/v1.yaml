$id: https://schema.multiverse.io/apprenticeship/orientation_started/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: When a user enters the orientation period.
required:
  - account_id
  - started_at
  - apprenticeship_id
properties:
  account_id:
    type: string
    description: The account uuid of the user who entered the orientation period.
    format: uuid
  started_at:
    type: string
    description: The date-time the user enterted the orientation period.
    format: date-time
  apprenticeship_id:
    type: string
    description: The uuid of the apprenticeship record.
    format: uuid
