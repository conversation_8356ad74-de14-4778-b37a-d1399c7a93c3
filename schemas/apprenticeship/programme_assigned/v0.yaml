$id: https://schema.multiverse.io/apprenticeship/programme_assigned/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a programme (apprenticeship qualification) is assigned to an apprentice, similar to cohort assignments, but for both finalised and unfinalised learners cohorts.
required:
  - apprenticeship_id
  - apprentice_user_id
  - cohort_id
  - company
  - cohort
  - apprenticeship_qualification
  - followed_dpt_pipeline
properties:
  followed_dpt_pipeline:
    description: Indicates if the apprentice followed the new DPT pipeline.
    type: boolean
  apprenticeship_id:
    type: string
    description: The unique identifier for the apprenticeship.
    format: uuid
  apprentice_user_id:
    type: string
    description: The unique identifier for the apprentice's platform user id.
    format: uuid
  cohort_id:
    type: string
    description: The unique identifier for the cohort id.
    format: uuid
  company:
    description: Details of the company associated with the apprenticeship's cohort.
    type: object
    properties:
      name:
        type: string
    required:
      - name
  cohort:
    description: Details of the cohort the apprentice was assigned to.
    type: object
    properties:
      title:
        type: string
      region:
        type: string
        enum:
          - GB
          - US
      recruitment_finalised:
        description: Indicates if the cohort recruitment process is finalised.
        type: boolean
    required:
      - title
      - region
      - recruitment_finalised
  apprenticeship_qualification:
    description: Details of the apprenticeship qualification.
    type: object
    properties:
      id:
        description: The identifier for the apprenticeship qualification.
        type: string
        format: uuid
      display_name:
        description: The unique display name of the qualification.
        type: string
    required:
      - id
      - display_name
