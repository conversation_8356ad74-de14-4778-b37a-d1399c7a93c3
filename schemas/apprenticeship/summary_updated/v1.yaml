$id: https://schema.multiverse.io/apprenticeship/summary_updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Event containing the current state of an apprenticeship's performance summary
required:
  - activity
  - apprenticeship_details
  - attendance
  - functional_skills
  - otj
  - risk
  - satisfaction
  - status
  - updated_at
properties:
  activity:
    type: object
    description: Activity details for the apprenticeship
    properties:
      latest_day_of_learning:
        type: ["string", "null"]
        description: Latest of delivery session attendance (including optional) and coaching attendance
        format: date
      latest_evidence_of_learning_date:
        type: ["string", "null"]
        description: The most recent date that a learner has attended/caught up on a live session, submitted a coaching survey or logged the relevant type of OTJ
        format: date
  apprenticeship_details:
    type: object
    description: Details of the apprenticeship
    properties:
      id:
        type: ["string", "null"]
        description: the id of the apprenticeship
        format: uuid
      account_id:
        type: ["string", "null"]
        description: The id of the apprentice's account
        format: uuid
      apprentice_manager_account_id:
        type: ["string", "null"]
        description: The id of the apprentice manager's account
        format: uuid
      apprenticeship_start_date:
        type: ["string", "null"]
        description: Date the apprenticeship started (Date flying start attendance was logged)
        format: date
      booked_gateway_date:
        type: ["string", "null"]
        description: Date for which Gateway has been booked
        format: date
      cohort_code:
        type: ["string", "null"]
        description: Shorthand name for the cohort
      cohort_id:
        type: ["string", "null"]
        description: The id of the cohort
        format: uuid
      cohort_start_date:
        type: ["string", "null"]
        description: Cohort's start date
        format: date
      company_id:
        type: ["string", "null"]
        description: The id of the company
        format: uuid
      completion_date:
        type: ["string", "null"]
        description: Date the apprenticeship was completed
        format: date
      country:
        type: ["string", "null"]
        description: Country of the apprenticeship
      estimated_gateway_date:
        type: ["string", "null"]
        description: Estimated gateway date
        format: date
      job_title:
        type: ["string", "null"]
        description: Apprentice's job title
      latest_epa_date:
        type: ["string", "null"]
        description: The date of the latest End Point Assessment of an apprentice
        format: date
      latest_epa_grade:
        type: ["string", "null"]
        description: The grade for the latest End Point Assessment of an apprentice
        enum:
          - pass
          - merit
          - fail
          - distinction
          - completion
          - referral
          - resubmit
          - did_not_attend
      programme_expected_gateway_months:
        type: ["integer", "null"]
        description: The number of months until the learner's gateway date
      programme_id:
        type: ["string", "null"]
        description: The id of the programme
        format: uuid
      programme_name:
        type: ["string", "null"]
        description: The name of the programme
      time_on_programme_days:
        type: ["integer", "null"]
        description: Days the learner has been on programme
      timely_gateway_date:
        type: ["string", "null"]
        description: Timely gateway date
        format: date
      unique_learner_number:
        type: ["integer", "null"]
        description: Unique learner number
  attendance:
    type: object
    description: Details about the apprentice's attendance
    properties:
      coaching_sessions_attended:
        type: ["integer", "null"]
        description: Count of the coaching sessions the learner has attended
      coaching_sessions_remaining:
        type: ["integer", "null"]
        description: Count of the coaching sessions remaining
      delivery_attendance_ratio:
        type: ["number", "null"]
        description: Fraction of non-optional delivery sessions that were attended (attended) / (attended + missed)
      delivery_sessions_attended:
        type: ["integer", "null"]
        description: Count of delivery sessions attended, including optional
      delivery_sessions_missed:
        type: ["integer", "null"]
        description: Count of delivery sessions that are not optional that have been missed so far
      delivery_sessions_remaining:
        type: ["integer", "null"]
        description: Count of remaining delivery sessions that are not optional in the delivery plan
      latest_coaching_session_attended:
        type: ["string", "null"]
        description: The most recent date that a learner attended a coaching session
        format: date
      latest_delivery_session_attended:
        type: ["string", "null"]
        description: The most recent date that a learner attended an optional or mandatory delivery session
        format: date
      latest_progress_review_date:
        type: ["string", "null"]
        description: Date of the latest progress review
        format: date
      total_sessions:
        type: ["integer", "null"]
        description: Total number of delivery sessions the apprentice should attend
  functional_skills:
    type: object
    description: Details about the apprentice's functional skills status
    properties:
      functional_skills_english:
        type: ["string", "null"]
        description: Status of the apprentices English functional skills
        enum:
          - exempt_with_certs
          - achieved_functional_skills
          - pass
          - exam_booked
          - exam_action_required
          - exam_action_required_no_evidence
          - yet_to_provide_extension_granted
          - yet_to_provide_name_change
          - yet_to_provide_certs
          - yet_to_provide_unacceptable_evidence
          - yet_to_provide_degree_uploaded
      functional_skills_maths:
        type: ["string", "null"]
        description: Status of the apprentices Maths functional skills
        enum:
          - exempt_with_certs
          - achieved_functional_skills
          - pass
          - exam_booked
          - exam_action_required
          - exam_action_required_no_evidence
          - yet_to_provide_extension_granted
          - yet_to_provide_name_change
          - yet_to_provide_certs
          - yet_to_provide_unacceptable_evidence
          - yet_to_provide_degree_uploaded
      maths_fs_exam_attempt_number:
        type: ["integer", "null"]
        description: Number of attempts of functional skills maths exams
      maths_fs_exam_date:
        type: ["string", "null"]
        description: Date of last booked functional skills maths exam
        format: date
      maths_fs_exam_status:
        type: ["string", "null"]
        description: Status of the latest maths exam
        enum:
          - pass
          - fail
          - exam_booked
          - no_show_or_cancelled
          - preliminary_pass_action_required
          - preliminary_fail_action_required
      meets_requirements:
        type: ["boolean", "null"]
        description: If the apprentice meets all functional skills requirements
      reading_fs_exam_attempt_number:
        type: ["integer", "null"]
        description: Number of attempts of functional skills reading exams
      reading_fs_exam_date:
        type: ["string", "null"]
        description: Date of last booked functional skills reading exam
        format: date
      reading_fs_exam_status:
        type: ["string", "null"]
        description: Status of the latest reading exam
        enum:
          - pass
          - fail
          - exam_booked
          - no_show_or_cancelled
          - preliminary_pass_action_required
          - preliminary_fail_action_required
      sl_fs_exam_attempt_number:
        type: ["integer", "null"]
        description: Number of attempts of functional skills speaking and listening exams
      sl_fs_exam_date:
        type: ["string", "null"]
        description: Date and time of last booked functional skills speaking and listening exam
        format: date-time
      sl_fs_exam_status:
        type: ["string", "null"]
        description: Status of the latest speaking and listening exam
        enum:
          - pass
          - fail
          - exam_booked
          - no_show_or_cancelled
          - preliminary_pass_action_required
          - preliminary_fail_action_required
      writing_fs_exam_attempt_number:
        type: ["integer", "null"]
        description: Number of attempts of functional skills writing exams
      writing_fs_exam_date:
        type: ["string", "null"]
        description: Date of last booked functional skills writing exam
        format: date
      writing_fs_exam_status:
        type: ["string", "null"]
        description: Status of the latest writing exam
        enum:
          - pass
          - fail
          - exam_booked
          - no_show_or_cancelled
          - preliminary_pass_action_required
          - preliminary_fail_action_required
  otj:
    type: object
    description: Off the job training details
    properties:
      otj_completed_hours:
        type: ["number", "null"]
        description: OTJ hours completed
      otj_percent:
        type: ["number", "null"]
        description: Percentage of target OTJ logged
      otj_required_hours:
        type: ["number", "null"]
        description: Total OTJ hours required to complete the apprenticeship
      otj_target_hours:
        type: ["number", "null"]
        description: OTJ hours required to be on track
  risk:
    type: object
    description: Risk details
    properties:
      support_areas:
        type: ["array", "null"]
        description: The areas where the apprentice needs support
        items:
          type: string
          enum:
            - attendance
            - otj
            - functional_skills
            - gateway_booked
            - none_specific
  satisfaction:
    type: object
    description: Satisfaction and wellbeing metrics
    properties:
      apprentice_latest_nps:
        type: ["integer", "null"]
        description: Latest NPS the apprentice responded with
      apprentice_latest_wellbeing:
        type: ["string", "null"]
        description: Latest wellbeing rating the apprentice responded with
        enum:
          - anxious
          - extremely_happy
          - extremely_unhappy
          - good
          - happy
          - okay
          - slightly_anxious_but_positive
          - somewhat_happy
          - somewhat_unhappy
          - unhappy
      latest_manager_support:
        type: ["string", "null"]
        description: Latest manager support rating the apprentice responded with
        enum:
          - high
          - neutral
          - low
  status:
    type: object
    description: Current status information
    properties:
      latest_bil_end_date:
        type: ["string", "null"]
        description: Latest break in learning end date
        format: date
      latest_bil_start_date:
        type: ["string", "null"]
        description: Latest break in learning start date
        format: date
      latest_bil_status:
        type: ["string", "null"]
        description: Latest break in learning status
        enum:
          - pending
          - approved
          - rejected
          - returned
          - abandoned
      learner_status:
        type: ["string", "null"]
        description: The current status of the learner for this apprenticeship
        enum:
          - on_programme
          - post_gateway
          - completed_on_time
          - no_show
          - completed_late
          - non_starter
          - on_bil
          - withdrawal
  updated_at:
    type: string
    description: The time of the last data ingestion, meaning how updated this record is
    format: date-time
