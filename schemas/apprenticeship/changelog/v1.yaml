$id: https://schema.multiverse.io/apprenticeship/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The apprenticeship changelog message from the Platform monolith.
required:
  - id
  - apprentice_account_id
  - apprentice_status
  - inserted_at
  - updated_at
  - cohort_id
  - company_id
  - apprentice_manager_account_id
  - job_title
  - work_hours_per_week
  - flying_start_attended_date
  - flying_start_attendance
  - candidate_type
  - months_to_expected_gateway
properties:
  id:
    type: string
    description: The apprenticeship's id
    format: uuid
  apprentice_account_id:
    type: string
    description: ID of the apprentice's account
    format: uuid
  apprentice_status:
    type: string
    enum:
      - on_programme
      - break_in_learning
      - withdrawn
      - gateway_complete
      - completed
      - non_starter
      - onboarding_pending
      - assessment_overridden
    description: The current status of the apprentice
  inserted_at:
    type: string
    format: date-time
    description: When the apprenticeship was created
  updated_at:
    type: string
    format: date-time
    description: When the apprenticeship was last modified
  cohort_id:
    type: ["string", "null"]
    description: ID of the cohort the apprenticeship belongs to
    format: uuid
  company_id:
    type: ["string", "null"]
    description: The id of the employer company
    format: uuid
  apprentice_manager_account_id:
    type: ["string", "null"]
    description: The account id of the apprentice's line manager
    format: uuid
  job_title:
    type: ["string", "null"]
    description: Job title of the apprentice
  work_hours_per_week:
    type: ["number", "null"]
    description: Number of working hours per week
  flying_start_attended_date:
    type: ["string", "null"]
    format: date
    description: The date the apprentice attended flying start
  flying_start_attendance:
    type: ["string", "null"]
    enum:
      - present
      - absent
      - non-attendee
      - non_starter
      - null
    description: The flying start attendance status
  candidate_type:
    type: ["string", "null"]
    enum:
      - employer_candidate
      - candidate
      - null
    description: The type of candidate
  months_to_expected_gateway:
    type: ["number", "null"]
    description: >-
      The number of months from the start of the programme to the expected gateway date
      for the apprentice. This is based on the programme/qualification they are doing
