$id: https://schema.multiverse.io/apprenticeship/progress_review_submitted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A progress review has been submitted by a user
required:
  - id
  - progress_review_id
  - submitted_at
  - submitted_by
properties:
  id:
    type: string
    description: The id of the apprenticeship.
    format: uuid
  progress_review_id:
    type: string
    description: The id of the progress review.
    format: uuid
  submitted_at:
    description: The time the review was submitted
    type: string
    format: date-time
  submitted_by_role:
    type: string
    enum: ["apprentice_manager", "coach", "apprentice"]
    description: The role of the person that submitted their portion of the progress review. One of apprentice_manager | coach | apprentice
