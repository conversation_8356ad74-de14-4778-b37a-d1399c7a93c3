$id: https://schema.multiverse.io/apprenticeship/coach_assigned/3.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: An event published whenever a coach is assigned to an apprenticeship
type: object
required:
  - id
  - apprenticeship_id
  - coach_user_id
  - coach_role
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the assignment record
    format: uuid
  apprenticeship_id:
    type: string
    description: The id of the apprenticeship
    format: uuid
  coach_user_id:
    type: string
    description: The id of the coach user
    format: uuid
  coach_role:
    type: string
    description: The role of the coach in this assignment
    enum:
      - one_to_one
      - launch
      - success
  inserted_at:
    type: string
    format: date-time
    description: The date and time the record was inserted
  updated_at:
    type: string
    format: date-time
    description: The date and time the record was last updated
