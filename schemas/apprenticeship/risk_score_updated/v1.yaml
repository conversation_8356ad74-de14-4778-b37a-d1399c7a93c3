$id: https://schema.multiverse.io/apprenticeship/risk_score_updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Risk score updated event for the apprenticeship (Platform).
required:
  - apprenticeship
  - id
properties:
  id:
    type: string
    description: The id of the corresponding apprenticeship record.
    format: uuid
  apprenticeship:
    type: object
    required:
      - id
      - risk_score
    properties:
      id:
        type: string
        description: The id of the corresponding apprenticeship record.
        format: uuid
      risk_score:
        type: object
        required:
          - score_bucket
        properties:
          score_bucket:
            type: integer
            enum: [1, 2, 3, 4]
            description: The risk score bucket of the apprenticeship.


