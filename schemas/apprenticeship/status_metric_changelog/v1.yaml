$id: https://schema.multiverse.io/apprenticeship/status_metric_changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A change in status metric for an apprenticeship.
required:
  - id
  - apprenticeship
  - status_metric
properties:
  id:
    type: string
    description: The id of the corresponding status metric record (a root field named ID is required for partition reasons).
    format: uuid
  apprenticeship:
    type: object
    description: "The apprenticeship where the status metric changed."
    properties:
      id:
        type: string
        format: uuid
    required:
      - id
  status_metric:
    type: object
    description: "The status metric that changed."
    properties:
      learner_status:
        type:
          - string
          - "null"
        enum:
          - on_programme
          - post_gateway
          - completed_on_time
          - no_show
          - completed_late
          - non_starter
          - on_bil
          - withdrawal
      latest_bil_start_date:
        type:
          - string
          - "null"
        format: date
      latest_bil_end_date:
        type:
          - string
          - "null"
        format: date
      latest_bil_status:
        type:
          - string
          - "null"
        enum:
          - pending
          - approved
          - rejected
          - returned
          - abandoned
      support_status:
        type: string
        enum:
          - status_unavailable
          - withdrawn
          - no_show
          - non_starter
          - completed
          - break_in_learning
          - post_gateway
          - support
          - on_track
    required:
      - learner_status
      - latest_bil_start_date
      - latest_bil_end_date
      - latest_bil_status
      - support_status