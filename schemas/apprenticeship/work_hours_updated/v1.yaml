$id: https://schema.multiverse.io/apprenticeship/work_hours_updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Work hours updated event for an apprenticeship.
required:
  - apprenticeship_id
  - work_hours_per_week
properties:
  apprenticeship_id:
    type: string
    description: The id of the corresponding apprenticeship record.
    format: uuid
  work_hours_per_week:
    type: number
    description: The number of work hours per week for the apprenticeship.
