$id: https://schema.multiverse.io/apprenticeship/gateway_booked/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The gateway booked event for the apprenticeship (Platform).
required:
  - apprenticeship
  - id
properties:
  id:
    type: string
    description: The id of the corresponding apprenticeship record.
    format: uuid
  apprenticeship:
    type: object
    properties:
      id:
        type: string
        description: The id of the corresponding apprenticeship record.
        format: uuid
      booked_gateway_date:
        type: string
        description: The date the gateway is booked for. If it's in the past we assume the gateway took place.
        format: date
    required:
      - id
      - booked_gateway_date
