$id: https://schema.multiverse.io/apprenticeship/manager_assigned/0.0.1
$schema: https://json-schema.org/draft/2020-12/schema
description: An event published whenever a manager is assigned to an apprenticeship
type: object
required:
  - id
  - apprenticeship_id
  - manager_account_id
properties:
  id:
    type: string
    description: The id of the assignment record
    format: uuid
  apprenticeship_id:
    type: string
    description: The id of the apprenticeship
    format: uuid
  manager_account_id:
    type: string
    description: The id of the coach user
    format: uuid
  inserted_at:
    type: string
    format: date-time
    description: The date and time the record was inserted
