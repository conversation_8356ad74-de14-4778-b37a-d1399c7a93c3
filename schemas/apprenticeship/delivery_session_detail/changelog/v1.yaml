$id: https://schema.multiverse.io/delivery_session_detail/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The delivery session detail changelog message from the Platform monolith.
required:
  - delivery_session_detail
properties:
  delivery_session_detail:
    type: object
    properties:
      uuid:
        type: string
        description: The delivery session's uuid
        format: uuid
      start_datetime:
        type: string
        description: The delivery session's start datetime
        format: date-time
      duration_in_minutes:
        type: integer
        description: The delivery session's duration in minutes
      session_name:
        type: ["string", "null"]
        description: The delivery session's name when the name differs from the one inhereited from the corresponding group delivery session record
      session_description:
        type: ["string", "null"]
        description: The delivery session's description when the description differs from the one inhereited from the corresponding group delivery session record
      location_type:
        type: string
        enum:
          [
            "teams",
            "zoom",
            "in_person",
            "embedded_learning",
            "custom_delivery_url",
          ]
        description: The delivery session's location type
      address:
        type: ["string", "null"]
        description: The delivery session's address when the location_type is in_person
      delivery_coach_id:
        type: ["string", "null"]
        description: The delivery session's delivery coach account UUID
        format: uuid
      teaching_assistant_id:
        type: ["string", "null"]
        description: The delivery session's teaching assistant account UUID
        format: uuid
      cohort_session_additional_emails:
        type: array
        description: The delivery session's additional emails to be notified
        items:
          type: string
          format: email
      is_flying_start:
        type: boolean
        description: Whether the delivery session is a flying start
      custom_delivery_url:
        type: ["string", "null"]
        description: The delivery session's custom delivery URL when the location_type is custom_delivery_url
      embedded_meeting_id:
        type: ["string", "null"]
        description: The delivery session's embedded meeting ID when the location_type is embedded_learning
      calendar_invites_sent_by_platform:
        type: boolean
        description: Whether the calendar invites were sent by the Platform monolith
      group_delivery_session_id:
        type: string
        description: The delivery session's group delivery session UUID
        format: uuid
      cohort_id:
        type: string
        description: The delivery session's cohort UUID
        format: uuid
      last_edited_by:
        type: ["string", "null"]
        description: The account UUID of the user who last edited the delivery session
        format: uuid
      inserted_at:
        type: string
        format: date-time
        description: The date and time the record was inserted
      updated_at:
        type: string
        format: date-time
        description: The date and time the record was last updated
    required:
      - uuid
      - start_datetime
      - duration_in_minutes
      - location_type
      - cohort_session_additional_emails
      - is_flying_start
      - calendar_invites_sent_by_platform
      - group_delivery_session_id
      - cohort_id
