$id: https://schema.multiverse.io/apprenticeship/latest_break_in_learning_updated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The latest break in learning updated event for the apprenticeship (Platform).
required:
  - apprenticeship
  - id
properties:
  id:
    type: string
    description: The id of the corresponding apprenticeship record.
    format: uuid
  apprenticeship:
    type: object
    required:
      - id
      - latest_break_in_learning
    properties:
      id:
        type: string
        description: The id of the corresponding apprenticeship record.
        format: uuid
      latest_break_in_learning:
        type: object
        required:
          - status
          - start_date
          - end_date
        properties:
          status:
            type: string
            description: The status of the latest break in learning. You can find the list of available statuses here - https://github.com/Multiverse-io/mv_data_pipeline/blob/main/terraform/aws/appsync/schema.graphql#L118-L124
            format: text
          start_date:
            type: string
            description: The start date of the latest break in learning.
            format: date
          end_date:
            type: string
            description: The end date of the latest break in learning.
            format: uuid
