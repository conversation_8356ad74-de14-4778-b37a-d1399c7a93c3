$id: https://schema.multiverse.io/apprenticeship_applications/pla_queried/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A measure of a candidate's prior learning overlap with the course they have applied for. Published when a candidate's application has been queried by HR.
required:
  - id
  - candidate
  - programme
  - company
  - actor_account
  - actor_review
properties:
  id:
    type: string
    description: The id of the corresponding candidate record (a root field named ID is required for partition reasons).
    format: uuid
  candidate:
    type: object
    description: "The candidate who had their prior learning assessed. The id here refers to the user.uuid"
    properties:
      id:
        type: string
        format: uuid
    required:
      - id
  programme:
    type: object
    description: "The programme the candidate is applying for."
    properties:
      id:
        type: string
        format: uuid
      name:
        type: string
      version_id:
        type: string
    required:
      - id
      - name
      - version_id
  actor_account:
    type: object
    description: "The account of the actor who reviewed the candidate's prior learning."
    properties:
      email:
        type: string
    required:
      - email
  company:
    type: object
    description: "The company associated with the candidate when they submitted their application."
    properties:
      id:
        type: string
        format: uuid
    required:
      - id
  actor_review:
    type: object
    description: "The actor's prior learning assessment review."
    properties:
      decision:
        type: string
        const: "queried"
      decided_at:
        description: "The date that the prior learning assessment got queried."
        type: string
        format: date-time
    required:
      - decision
      - decided_at
