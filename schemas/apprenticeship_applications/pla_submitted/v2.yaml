$id: https://schema.multiverse.io/apprenticeship_applications/pla_submitted/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A measure of an employer candidate's prior learning overlap with the course they have applied for. Published when an employer candidate's application has been reviewed by our internal admissions team.
required:
  - id
  - candidate
  - programme
  - company
properties:
  id:
    type: string
    description: The id of the corresponding candidate record (a root field named ID is required for partition reasons).
    format: uuid
  candidate:
    type: object
    description: "The candidate who had their prior learning assessed."
    properties:
      id:
        type: string
        format: uuid
    required:
      - id
  programme:
    type: object
    description: "The programme the candidate is applying for."
    properties:
      id:
        type: string
        format: uuid
      name:
        type: string
      version_id:
        type: string
    required:
      - id
      - name
      - version_id
  company:
    type: object
    description: "The company associated with the candidate when they submitted their application."
    properties:
      id:
        type: string
        format: uuid
    required:
      - id
  prior_learning_assessment:
    type: object
    description: "The prior learning assessment associated with the candidate."
    properties:
      admissions_decision:
        type: string
        enum: ["approved", "approved_with_adjustment", "rejected"]
      admissions_adjustment:
        type: ["number", "null"]
    required:
      - admissions_decision
      - admissions_adjustment
