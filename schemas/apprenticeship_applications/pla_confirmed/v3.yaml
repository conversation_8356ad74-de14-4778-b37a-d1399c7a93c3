$id: https://schema.multiverse.io/apprenticeship_applications/pla_confirmed/3.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A measure of an employer candidate's prior learning overlap with the course they have applied for. Published when an employer candidate's application has been confirmed by an Hr manager or by timeout.
required:
  - id
  - candidate
  - programme
  - company
  - actor_review
  - actor_account
properties:
  id:
    type: string
    description: The id of the corresponding candidate record (a root field named ID is required for partition reasons).
    format: uuid
  candidate:
    type: object
    description: "The candidate who had their prior learning assessed."
    properties:
      id:
        type: string
        format: uuid
    required:
      - id
  programme:
    type: object
    description: "The programme the candidate is applying for."
    properties:
      id:
        type: string
        format: uuid
      name:
        type: string
      version_id:
        type: string
        format: uuid
    required:
      - id
      - name
      - version_id
  company:
    type: object
    description: "The company associated with the candidate when they submitted their application."
    properties:
      id:
        type: string
        format: uuid
    required:
      - id
  actor_review:
    type: object
    description: "The actor's prior learning assessment review."
    properties:
      decision:
        type: string
        enum: ["confirmed", "confirmed_by_timeout"]
      decided_at:
        description: "The date that the prior learning assessment got confirmed."
        type: string
        format: date-time
    required:
      - decision
      - decided_at
  actor_account:
    type: 
      - object
      - "null"
    description: "The account of the actor who reviewed the candidate's prior learning. When this is null this means that the system approved it."
    properties:
      email:
        type: string
    required:
      - email
