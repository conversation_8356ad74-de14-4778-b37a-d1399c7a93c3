$id: https://schema.multiverse.io/note/changelog/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The note changelog message from notes service.
required:
  - note
properties:
  note:
    type: object
    properties:
      id:
        type: string
        description: The note's id
        format: uuid
      author_account_id:
        type: string
        description: ID of the account that authored the note
        format: uuid
      subject_id:
        type: string
        description: ID of the entity this note is about. The id here depends on the entity type of the note. If apprenticeship then subject_id is the apprenticeship_id and if entity type is account then subject_id refers to account_id etc
        format: uuid
      body:
        type: string
        description: The content of the note
      entity_type:
        type: string
        description: The type of entity this note relates to
        enum: ["apprenticeship", "account", "cohort", "delivery_session"]
      note_type:
        type: string
        description: The classification of this note
        enum: ["coach_note"]
      tags:
        type: array
        description: Array of tags associated with the note
        items:
          type: string
      inserted_at:
        type: string
        format: date-time
        description: When the note was created
      updated_at:
        type: string
        format: date-time
        description: When the note was last modified
      deleted_at:
        type: ["string", "null"]
        format: date-time
        description: When the note was soft deleted
    required:
      - id
      - author_account_id
      - subject_id
      - body
      - entity_type
      - note_type
      - inserted_at
      - updated_at
