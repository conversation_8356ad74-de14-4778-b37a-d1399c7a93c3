$id: https://schema.multiverse.io/atlas/learning_unit_channel_changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A learning unit channel changelog event is raised whenever a learning unit channel entity is created or updated in the system.
required:
  - id
  - unit_template_version_id
  - channel_id
  - company_id
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the learning unit channel record
    format: uuid
  unit_template_version_id:
    type: string
    description: The id of the learning unit template version which this record is associated with
    format: uuid
  channel_id:
    type: string
    description: The channel the learning unit is associated with
    format: uuid
  company_id:
    description: The company the learning unit is associated with
    type: ["string", "null"]
    format: uuid
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
