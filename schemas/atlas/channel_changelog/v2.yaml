$id: https://schema.multiverse.io/atlas/channel_changelog/2.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A channel changelog event is raised whenever a new channel is created or updated in the system.
required:
  - id
  - title
  - type
  - inserted_at
  - updated_at
  - created_by_id
  - bot_id
properties:
  id:
    type: string
    description: The id of the channel record
    format: uuid
  title:
    type: string
    description: The title of the channel
  type:
    type: string
    description: The type of the channel
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
  created_by_id:
    description: The id of the user who created the channel, or null if the channel was automatically created
    type: ["string", "null"]
    format: uuid
  bot_id:
    description: The id of the bot associated with the channel, or null if no bot is associated
    type: ["string", "null"]
    format: uuid
