$id: https://schema.multiverse.io/atlas/user_out_of_office_changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An out of office changelog event is raised whenever a user's out of office status is updated in Atlas.
required:
  - id
  - user_id
  - is_out_of_office
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the out of office record
    format: uuid
  user_id:
    type: string
    description: The id of the user that the out of office status belongs to
    format: uuid
  is_out_of_office:
    type: boolean
    description: Whether the user is out of office or not
  out_of_office_message:
    type: ["string", "null"]
    description: The message that the user has set for their out of office status
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time