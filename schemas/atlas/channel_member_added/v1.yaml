$id: https://schema.multiverse.io/atlas/channel_member_added/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: channel member added event is raised whenever a new channel member record is added
required:
  - id
  - channel_id
  - user_id
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the channel member record
    format: uuid
  channel_id:
    type: string
    description: The id of the channel record
    format: uuid
  user_id:
    type: string
    description: The id of the user
    format: uuid
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
