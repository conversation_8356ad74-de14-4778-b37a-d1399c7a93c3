$id: https://schema.multiverse.io/atlas/message_read/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Represents a message being read by a user
required:
  - id
  - message_id
  - user_id
  - read_at
properties:
  id:
    type: string
    description: The id of the read receipt record
    format: uuid
  message_id:
    type: string
    description: The id of the message that was read
    format: uuid
  user_id:
    type: string
    description: The id of the user who read the message
    format: uuid
  read_at:
    type: string
    description: The time that the message was read
    format: date-time
