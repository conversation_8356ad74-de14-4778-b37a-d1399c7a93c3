$id: https://schema.multiverse.io/atlas/message_reaction_deleted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A message reaction removed event is raised whenever a user reacts to the .
required:
  - id
  - deleted_at
properties:
  id:
    type: string
    description: The id of the message reaction record
    format: uuid
  deleted_at:
    type: ["string", "null"]
    description: The time that the message reaction was deleted as a timestamp with timezone information, in ISO8601 format, or null if the message has not been deleted
    format: date-time