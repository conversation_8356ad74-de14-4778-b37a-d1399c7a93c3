$id: https://schema.multiverse.io/atlas/customer_survey_changelog/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A customer survey changelog event is raised whenever a new customer survey is created or updated in the system.
required:
  - id
  - text
  - type
  - score
  - thread_id
  - user_id
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the customer survey record
    format: uuid
  text:
    description: The text of the customer survey
    type: ["string", "null"]
  type:
    type: string
    description: The type of the customer survey
  score:
    description: The score of the customer survey
    type: ["integer", "null"]
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
  user_id:
    type: string
    description: The id of the user who created the customer survey
    format: uuid
  thread_id:
    type: string
    description: The id of the thread associated with the customer survey
    format: uuid
