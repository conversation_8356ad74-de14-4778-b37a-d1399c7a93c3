$id: https://schema.multiverse.io/atlas/spotlight_highlight_created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A spotlight highlight created event is raised whenever a highlight is created.
required:
  - id
  - text
  - thread_id
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the highlight record
    format: uuid
  text:
    type: string
    description: The text of the highlight
  thread_id:
    type: string
    description: The id of the thread that the highlight belongs to
    format: uuid
  inserted_at:
    type: string
    description: The time that the highlight was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the highlight was updated in the database represented as a string in ISO8601 format
    format: date-time 