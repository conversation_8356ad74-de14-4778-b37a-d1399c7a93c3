$id: https://schema.multiverse.io/atlas/project_idea_created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Represents a project idea created
required:
  - id
  - title
  - subtitle
  - deliverable
  - business_problem_addressed
  - impact
  - inserted_at
  - updated_at
  - project_id
  - user_id
properties:
  id:
    type: string
    description: The id of the project idea
    format: uuid
  title:
    type: string
    description: The title of the project idea
  subtitle:
    type: string
    description: The subtitle of the project idea
  deliverable:
    type: string
    description: The deliverable of the project idea
  business_problem_addressed:
    type: string
    description: The business problem that the project idea addresses
  impact:
    type: string
    description: The impact of the project idea
  inserted_at:
    type: string
    description: The date and time the project idea was created
    format: date-time
  updated_at:
    type: string
    description: The date and time the project idea was updated
    format: date-time
  project_id:
    type: string
    description: The id of the project that the project idea is associated with
    format: uuid
  user_id:
    type: string
    description: The id of the user that the project idea is associated with
    format: uuid