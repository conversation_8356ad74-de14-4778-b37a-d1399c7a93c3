$id: https://schema.multiverse.io/atlas/tour_guide_step_completed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A tour guide step completed event is raised whenever a tour guide step is completed by a user in the system.
required:
  - id
  - flow_id
  - step_number
  - user_id
  - completed_at
properties:
  id:
    type: string
    description: The database id of the tour guide step record
    format: uuid
  flow_id:
    type: string
    description: The identifier for the tour guide step that was completed
  title:
    type: string
    description: The title of the tour guide step
  description:
    type: string
    description: The description/content of the tour guide step
  step_number:
    type: integer
    description: The order number of this step within its flow
  user_id:
    type: string
    description: The id of the user who completed this tour guide step
    format: uuid
  completed_at:
    type: string
    description: The time that the tour guide step was completed represented as a string in ISO8601 format
    format: date-time
