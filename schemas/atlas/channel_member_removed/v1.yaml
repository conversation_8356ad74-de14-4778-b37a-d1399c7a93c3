$id: https://schema.multiverse.io/atlas/channel_member_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A channel member removed event is raised whenever a user is removed from a channel.
required:
  - id
  - deleted_at
properties:
  id:
    type: string
    description: The id of the channel member record
    format: uuid
  channel_id:
    type: string
    description: The id of the channel record
    format: uuid
  user_id:
    type: string
    description: The id of the user
    format: uuid
  deleted_at:
    type: "string"
    description: The time that the channel member was removed as a timestamp with timezone information, in ISO8601 format
    format: date-time
