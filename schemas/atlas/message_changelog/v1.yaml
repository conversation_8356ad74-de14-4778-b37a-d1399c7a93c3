$id: https://schema.multiverse.io/atlas/message_changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A message changelog event is raised whenever a new thread is created or updated in the system.
required:
  - id
  - text
  - thread_id
  - user_id
  - bot_id
  - inserted_at
  - updated_at
  - deleted_at
properties:
  id:
    type: string
    description: The id of the message record
    format: uuid
  text:
    type: string
    description: The text of the message
  thread_id:
    type: string
    description: The id of the thread that the message belongs to
    format: uuid
  user_id:
    type: ["string", "null"]
    description: The id of the user who sent the message, or null if a bot sent the message
    format: uuid
  bot_id:
    type: ["string", "null"]
    description: The id of the bot associated with the message, or null if no bot is associated
    format: uuid
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
  deleted_at:
    type: ["string", "null"]
    description: The time that the message was deleted as a timestamp with timezone information, in ISO8601 format, or null if the message has not been deleted
    format: date-time
