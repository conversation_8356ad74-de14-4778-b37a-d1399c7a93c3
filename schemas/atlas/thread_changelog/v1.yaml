$id: https://schema.multiverse.io/atlas/thread_changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A thread changelog event is raised whenever a new thread is created or updated in the system.
required:
  - id
  - channel_id
  - inserted_at
  - updated_at
  - title
  - archived
properties:
  id:
    type: string
    description: The id of the thread record
    format: uuid
  channel_id:
    type: string
    description: The id of the channel that the thread belongs to
    format: uuid
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
  title:
    type: string
    description: The title of the thread
  archived:
    type: boolean
    description: Whether the thread is archived or not
  
