$id: https://schema.multiverse.io/atlas/message_feedback_changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A message feedback changelog event is raised whenever a new thread is created or updated in the system.
required:
  - id
  - content
  - message_id
  - user_id
  - helpful
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the message feedback record
    format: uuid
  content:
    type: ["string", "null"]
    description: The content of the message feedback
  helpful:
    type: boolean
    description: Whether the message feedback was helpful or not
  message_id:
    type: string
    description: The id of the message that the message feedback belongs to
    format: uuid
  user_id:
    type: string
    description: The id of the user who sent the message feedback, or null if a bot sent the message feedback
    format: uuid
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
