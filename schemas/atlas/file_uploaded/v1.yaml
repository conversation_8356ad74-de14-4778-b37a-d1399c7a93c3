$id: https://schema.multiverse.io/atlas/file_uploaded/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: A file upload changelog event is raised whenever a new thread is created or updated in the system.
required:
  - id
  - name
  - size
  - message_id
  - user_id
  - inserted_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the file upload record
    format: uuid
  name:
    type: string
    description: The name of the file upload
  size:
    type: integer
    description: The size of the file upload
  content_type:
    type: string
    description: The content type of the file upload
  message_id:
    type: string
    description: The id of the message that the file upload belongs to
    format: uuid
  user_id:
    type: string
    description: The id of the user who sent the file upload
    format: uuid
  inserted_at:
    type: string
    description: The time that the data was inserted in the database represented as a string in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time that the data was updated in the database represented as a string in ISO8601 format
    format: date-time
