$id: https://schema.multiverse.io/teams_meeting_attendance_report/created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever we store a new meeting attendance report
required:
  - id
  - teams_meeting_id
  - total_participant_count
  - meeting_start_date_time
  - meeting_end_date_time
  - meeting_duration_in_seconds
  - average_attendance_time_in_seconds
  - attendance_records
  - created_at
properties:
  id:
    type: string
    description: The id of the meeting attendance report
  teams_meeting_id:
    type: string
    description: The id of the teams meeting, this id comes from the teams api
  total_participant_count:
    type: integer
    description: The total count of participants in the meeting
  meeting_start_date_time:
    type: string
    description: The start date and time of the meeting
    format: date-time
  meeting_end_date_time:
    type: string
    description: The end date and time of the meeting
    format: date-time
  meeting_duration_in_seconds:
    type: integer
    description: The duration of the meeting in seconds
  average_attendance_time_in_seconds:
    type: integer
    description: The average attendance time of the meeting in seconds
  attendance_records:
    type: array
    description: The attendance records for the meeting
    items:
      type: object
      required:
        - id
        - email_address
        - display_name
        - role
        - total_attendance_time_in_seconds
        - attendance_intervals
      properties:
        id:
          type: string
          description: The id of the meeting attendance record
        email_address:
          type: string
          description: The email address of the participant
        display_name:
          type: string
          description: The display name of the participant
        role:
          type: string
          description: The role of the participant
        total_attendance_time_in_seconds:
          type: integer
          description: The total attendance time of the participant in seconds
        first_join_date_time:
          type: string
          description: The date and time the participant first joined the meeting
          format: date-time
        last_leave_date_time:
          type: string
          description: The date and time the participant last left the meeting
          format: date-time
        attendance_intervals:
          type: array
          description: The attendance intervals for the participant
          items:
            type: object
            required:
              - join_date_time
              - leave_date_time
              - duration_in_seconds
            properties:
              join_date_time:
                type: string
                description: The date and time the participant joined the meeting
                format: date-time
              leave_date_time:
                type: string
                description: The date and time the participant left the meeting
                format: date-time
              duration_in_seconds:
                type: integer
                description: The duration of the attendance interval in seconds
  created_at:
    type: string
    description: The date and time the meeting attendance report was created
    format: date-time
