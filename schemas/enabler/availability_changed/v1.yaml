$id: https://schema.multiverse.io/enabler/availability_changed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
$description: Event is published whenever the availability of an enabler changes.
type: object
required:
  - id
  - available
  - availability_updated_at
properties:
  id:
    type: string
    description: The id of the enabler, which matches their account id
    format: uuid
  available:
    type: boolean
    description: Whether the enabler is available to be allocated to allocations
    format: boolean
  availability_updated_at:
    type: string
    description: The time this enabler availability was updated in ISO8601 format
    format: date-time