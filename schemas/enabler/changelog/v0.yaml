$id: https://schema.multiverse.io/enabler/changelog/0.3.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Represents an enabler known to the enabler registry. Enablers are users of the Multiverse
  platform who have specific roles, such as `staff:coach`. A new enabler will be created when
  a new user account is created with such a role or when such a role is added to an existing
  user account.
type: object
required:
  - id
  - email
  - tags
  - created_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the enabler, which matches their account id
    format: uuid
  email:
    type: string
    description: The email address of the enabler
  first_name:
    type: string
    description: The first name of the enabler
  last_name:
    type: string
    description: The first name of the enabler
  tags:
    type: object
    description: |
      The tags used to list the enabler's abilities. This will be an object where
      each property is a list of tags of a given type. We're still not documenting
      the different keys/tag types as those are still constantly changing.
    properties:
      unit_template_ids:
        type: array
        description: List of unit template ids the enabler is skilled on
        items:
          type: string
          format: uuid
      types:
        type: array
        description: List of types the enabler is able to be allocated to
        items:
          type: string
          enum: ["guide", "instructor"]
      company_ids:
        type: array
        description: List of company ids the enabler is associated with
        items:
          type: string
          format: uuid
  created_at:
    type: string
    description: The time this enabler was created in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time this enabler was updated in ISO8601 format
    format: date-time
