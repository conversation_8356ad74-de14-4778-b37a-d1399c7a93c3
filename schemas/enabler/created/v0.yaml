$id: https://schema.multiverse.io/enabler/created/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Event published whenever a new enabler is added to the enabler registry.
  Enablers are users of the Multiverse platform who have specific roles,
  such as `staff:coach`. A new enabler will be created when a new user account
  is created with such a role or when such a role is added to an existing user account.
type: object
required:
  - id
  - email
  - created_at
properties:
  id:
    type: string
    description: The id of the enabler, which matches their account id
    format: uuid
  email:
    type: string
    description: The email address of the enabler
  first_name:
    type: string
    description: The first name of the enabler
  last_name:
    type: string
    description: The first name of the enabler
  created_at:
    type: string
    description: The time this enabler was created in ISO8601 format
    format: date-time
