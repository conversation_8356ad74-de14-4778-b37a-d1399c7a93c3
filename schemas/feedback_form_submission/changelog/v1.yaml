$id: https://schema.multiverse.io/feedback_form_submission/changelog/1.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The event dispatched whenever a feedback form submission is updated
required:
  - id
  - feedback_form_id
  - account_id
  - user_agent
  - context
  - status
  - answers
  - created_at
  - updated_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the feedback form submission
  feedback_form_id:
    type: string
    format: uuid
    description: The id of the feedback form the submission is associated with
  unique_key:
    type: string
    description: A unique key used to allow multiple submissions per user to one form (e.g., using the same form for many units)
  account_id:
    type: string
    format: uuid
    description: The id of the account that submitted the feedback form
  user_agent:
    type: string
    description: The user agent of the browser used to submit the feedback form
  url:
    type: string
    description: The url on which the feedback form was submitted
  display_type:
    type: string
    description: The display type of the feedback form when it was submitted
    enum:
      - inline
      - link
      - full_screen
      - popover
      - modal
      - docked
  context:
    description: Additional context information about the feedback form submission.
    type: object
    properties:
      meeting_id:
        type: string
        description: The id of the Hello App meeting associated with the feedback form submission
      schema_version:
        type: string
        description: The version of the schema used for this submission
      source:
        type: string
        description: The source system that created this submission
      unit_id:
        type: string
        description: The id of the unit this submission is associated with
      unit_template_version_id:
        type: string
        description: The id of the unit template version this submission is associated with
      activity_id:
        type: string
        description: The id of the activity this submission is associated with
  status:
    type: string
    description: The status of the feedback form submission.
    enum:
      - presented
      - partially_submitted
      - submitted
  submitted_at:
    type: string
    format: date-time
    description: The time the feedback form was submitted
  answers:
    type: array
    description: The answers to the feedback form questions
    items:
      type: object
      required:
        - id
        - question_id
        - answer
        - created_at
        - updated_at
      properties:
        id:
          type: string
          format: uuid
          description: The id of the answer
        question_id:
          type: string
          format: uuid
          description: The id of the question the answer is associated with
        answer:
          type: string
          description: The answer to the question
        created_at:
          type: string
          format: date-time
          description: The time the answer was created
        updated_at:
          type: string
          format: date-time
          description: The time the answer was last updated
  created_at:
    type: string
    format: date-time
    description: The time the feedback form submission was created
  updated_at:
    type: string
    format: date-time
    description: The time the feedback form submission was last updated
