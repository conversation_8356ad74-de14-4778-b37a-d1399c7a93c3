$id: https://schema.multiverse.io/feedback_form_submission/changelog/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The event dispatched whenever a feedback form submission is updated
required:
  - id
  - feedback_form_id
  - account_id
  - user_agent
  - context
  - status
  - answers
  - created_at
  - updated_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the feedback form submission
  feedback_form_id:
    type: string
    format: uuid
    description: The id of the feedback form the submission is associated with
  account_id:
    type: string
    format: uuid
    description: The id of the account that submitted the feedback form
  user_agent:
    type: string
    description: The user agent of the browser used to submit the feedback form
  context:
    type: object
    description: Additional context information about the feedback form submission
    properties:
      meeting_id:
        type: string
        description: The id of the Hello App meeting associated with the feedback form submission
  status:
    type: string
    enum:
      - presented
      - pending
      - submitted
  submitted_at:
    type: string
    format: date-time
    description: The time the feedback form was submitted
  answers:
    type: array
    description: The answers to the feedback form questions
    items:
      type: object
      required:
        - id
        - question_id
        - answer
        - created_at
        - updated_at
      properties:
        id:
          type: string
          format: uuid
          description: The id of the answer
        question_id:
          type: string
          format: uuid
          description: The id of the question the answer is associated with
        answer:
          type: string
          description: The answer to the question
        created_at:
          type: string
          format: date-time
          description: The time the answer was created
        updated_at:
          type: string
          format: date-time
          description: The time the answer was last updated
  created_at:
    type: string
    format: date-time
    description: The time the feedback form submission was created
  updated_at:
    type: string
    format: date-time
    description: The time the feedback form submission was last updated
