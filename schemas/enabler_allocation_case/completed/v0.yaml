$id: https://schema.multiverse.io/enabler_allocation_case/completed/0.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
  - enabler_allocation_type
  - enabler_allocation_need_id
  - allocated_enablers
  - completed_at
properties:
  id:
    type: string
    description: The id of the enabler allocation case
    format: uuid
  enabler_allocation_need_id:
    type: string
    description: The id of the enabler allocation need that triggered the creation of this allocation case
    format: uuid
  enabler_allocation_type:
    type: string
    description: The type of enabler allocation, which is defined by the type of the allocation need that triggered the creation of the allocation case
    enum: ["unit", "guidance"]
  learner_account_id:
    type: string
    description: The account id of the learner that this allocation case is for
    format: uuid
  allocated_enablers:
    type: array
    description: A list with the enablers allocated to this case
    items:
      type: object
      required:
        - enabler_type
        - enabler_account_id
      properties:
        enabler_type:
          type: string
          description: The type of enablement this enabler will provide in this allocation case
          enum: ["instructor", "guide"]
        enabler_account_id:
          type: string
          description: The account id of this enabler
          format: uuid
  completed_at:
    type: string
    description: The time this allocation case was completed in ISO8601 format
    format: date-time
