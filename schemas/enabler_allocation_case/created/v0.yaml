$id: https://schema.multiverse.io/enabler_allocation_case/created/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
  - enabler_allocation_type
  - enabler_allocation_need_id
  - created_at
properties:
  id:
    type: string
    description: The id of the enabler allocation case
    format: uuid
  enabler_allocation_need_id:
    type: string
    description: The id of the enabler allocation need that triggered the creation of this allocation case
    format: uuid
  enabler_allocation_type:
    type: string
    description: The type of enabler allocation, which is defined by the type of the allocation need that triggered the creation of the allocation case
    enum: ["unit", "guidance"]
  created_at:
    type: string
    description: The time this allocation case was created in ISO8601 format
    format: date-time
