$id: https://schema.multiverse.io/user_goal/progressed/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever progress towards a goal changes
required:
  - id
  - progress
  - competencies
properties:
  id:
    description: The id of the user goal
    type: string
    format: uuid
  progress:
    description: Overall progress towards this user goal, based on the current progress of associated competencies
    type: number
    minimum: 0
    maximum: 100
  competencies:
    description: The list of competencies associated to this goal
    type: array
    items:
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
        attained:
          description: Whether the competency has been attained. If not present it's safe to assume that the competency hasn't been attained
          type: boolean
          default: false
