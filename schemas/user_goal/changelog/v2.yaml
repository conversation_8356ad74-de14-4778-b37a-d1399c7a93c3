$id: https://schema.multiverse.io/user_goal/changelog/2.3.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event representing the current state of a user goal
required:
  - id
  - user_account_id
  - customer_goal_id
  - title
  - description
  - status
  - learning_enabled
  - progress
  - competencies
  - created_at
  - updated_at
properties:
  id:
    description: The id of the user goal
    type: string
    format: uuid
  user_account_id:
    description: The account id of the user this goal belongs to
    type: string
    format: uuid
  customer_goal_id:
    description: The id of the customer goal that the user has been assigned to
    type: string
    format: uuid
  title:
    description: The title of the user goal
    type: string
  description:
    description: The description of the user goal
    type: string
  status:
    description: The status of the user goal
    type: string
    enum: ["pending_invite_acceptance", "in_progress", "completed", "cancelled"]
  learning_enabled:
    description: Indicates whether learning has been enabled for this user goal
    type: boolean
  progress:
    description: Overall progress towards this user goal, based on the current progress of associated competencies
    type: number
    minimum: 0
    maximum: 100
  competencies:
    description: The list of competencies associated to this goal
    type: array
    items:
      minItems: 1
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
  created_at:
    description: The time the user goal was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the user goal was last updated in ISO8601 format
    type: string
    format: date-time
  skills_scan_enabled:
    description: Whether this user goal requires a skills scan to be completed
    type: boolean
    default: true
  learner_description:
    description: The learner-facing, layman-worded description of the user goal
    type: string
  is_levy:
    description: Whether the goal is a Levy goal or not. Can be assumed as false if not present.
    type: boolean
