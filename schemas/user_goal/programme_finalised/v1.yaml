$id: https://schema.multiverse.io/user_goal/programme_finalised/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event indicating that a levy user goal has been finalised with a programme
required:
  - id
  - account_id
  - programme_id
properties:
  id:
    description: The id of the user goal
    type: string
    format: uuid
  account_id:
    description: The id of the account that the user goal belongs to
    type: string
    format: uuid
  programme_id:
    description: The id of the programme / apprenticeship qualification that the user goal has been finalised with
    type: string
    format: uuid
