$id: https://schema.multiverse.io/feedback_form/changelog/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The event dispatched whenever a feedback form is updated
required:
  - id
  - name
  - description
  - display_type
  - url
  - status
  - event_trigger
  - feedback_groups
  - question_groups
  - created_at
  - updated_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the feedback form
  name:
    type: string
    description: The name of the feedback form
  description:
    type: string
    description: The description of the feedback form
  display_type:
    type: string
    description: The display type of the feedback form
    enum:
      - inline
      - link
      - full_screen
      - popover
      - modal
      - docked
  url:
    type: string
    description: The unique url of the feedback form
  status:
    type: string
    description: The status of the feedback form
    enum:
      - draft
      - published
      - archived
  event_trigger:
    type: string
    description: The event that triggered the feedback form
  feedback_groups:
    type: array
    description: The feedback groups associated with the feedback form
    items:
      type: string
  question_groups:
    type: array
    description: The question groups associated with the feedback form
    items:
      type: object
      required:
        - id
        - name
        - description
        - questions
      properties:
        id:
          type: string
          format: uuid
          description: The id of the question group
        name:
          type: string
          description: The name of the question group
        description:
          type: string
          description: The description of the question group
        questions:
          type: array
          description: The questions associated with the question group
          items:
            type: object
            description: A question associated with the question group
            required:
              - id
              - text
              - type
              - respondent
              - created_at
              - updated_at
            properties:
              id:
                type: string
                format: uuid
                description: The id of the question
              text:
                type: string
                description: The text of the question
              type:
                type: string
                description: The type of the question
                enum:
                  - reaction
                  - long-text
                  - short-text
                  - email
                  - rating-1-5
                  - rating-1-7
                  - nps
                  - statement
                  - s3uri
                  - thank-you
                  - single-option
                  - multiple-option
              options:
                type: array
                description: The options of the question when question type is single-option or multiple-option
                items:
                  type: string
              respondent:
                type: string
                description: The respondent type of the question
                enum:
                  - learner
                  - guide
                  - customer
              created_at:
                type: string
                format: date-time
                description: The time the question was created
              updated_at:
                type: string
                format: date-time
                description: The time the question was last updated
  created_at:
    type: string
    format: date-time
    description: The time the feedback form was created
  updated_at:
    type: string
    format: date-time
    description: The time the feedback form was last updated
