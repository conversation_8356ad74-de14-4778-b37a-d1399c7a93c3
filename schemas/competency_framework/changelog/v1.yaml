$id: https://schema.multiverse.io/competency_framework/changelog/1.0.1
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a competency framework is updated
required:
  - id
  - name
  - state
  - competencies
  - created_at
  - updated_at
properties:
  id:
    description: The id of the competency framework
    type: string
    format: uuid
  name:
    description: The name of the competency framework
    type: string
  description:
    description: The description of the competency framework
    type: string
  state:
    description: The state of the competency framework
    type: string
    enum: ["draft", "live"]
  competencies:
    description: The list of competencies associated with the competency framework
    type: array
    items:
      minItems: 0
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
  created_at:
    description: The time the competency framework was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the competency framework was updated in ISO8601 format
    type: string
    format: date-time
