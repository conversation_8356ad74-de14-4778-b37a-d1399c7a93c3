$id: https://schema.multiverse.io/uk_apprenticeship_standard/changelog/0.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: > 
  An event published whenever an apprenticeship standard is updated.  The
  uk_apprenticeship_standard entity contains data about an IfATE apprenticeship
  standard and its associated occupations. All the data in this entity should
  originate from the IfATE standard but not all the data in the standard will be
  present in this entity, only what is relevant to KSBs.
required:
  - id
  - reference_number
  - version
  - title
  - status
  - updated_at
  - created_at
  - occupations
properties:
  id:
    type: string
    description: The id of the apprenticeship standard (specific to Multiverse)
    format: uuid
  reference_number:
    description: The IfATE reference number of the apprenticeship standard
    type: string
  version:
    type: string
    description: >
      The version of the apprenticeship standard (as provided by IfATE)
  title:
    type: string
    description: The title of the apprenticeship standard
  status:
    type: string
    description: The status of the occupational standard at Multiverse
    enum:
      - draft
      - published
  created_at:
    type: string
    format: date-time
    description: > 
      The date and time the occupational standard was created at Multiverse in
      ISO8601 format
  updated_at:
    type: string
    format: date-time
    description: >
      The date and time the occupational standard was updated at Multiverse in
      ISO8601 format
  occupations:
    type: array
    description: >
      The occupations that are part of the apprenticeship standard
    items:
      type: object
      required:
        - standard_code
        - title
      properties:
        standard_code:
          type: string
          description: >
            The IfATE standard code of the occupation
        title:
          type: string
      uniqueItems: true
