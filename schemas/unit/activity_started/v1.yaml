$id: https://schema.multiverse.io/unit/activity_started/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever an activity in a unit is started
required:
  - id
  - account_id
  - activity_id
  - activity_template_version_id
  - title
  - started_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the unit
  account_id:
    type: string
    format: uuid
    description: The id of the user who started this activity
  activity_id:
    type: string
    format: uuid
    description: The globally unique id of the activity instance
  activity_template_version_id:
    type: string
    format: uuid
    description: The id of the activity template version from which this activity was created
  title:
    description: The title of the activity
    type: string
  started_at:
    description: The time at which the activity was started in ISO8601 format
    type: string
    format: date-time
