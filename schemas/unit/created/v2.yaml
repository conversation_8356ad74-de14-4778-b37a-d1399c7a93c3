$id: https://schema.multiverse.io/unit/created/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a unit is created
required:
  - id
  - unit_template_id
  - unit_template_version_id
  - title
  - thumbnail
  - competencies
  - created_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the unit
  unit_template_id:
    type: string
    format: uuid
    description: The id of the unit template
  unit_template_version_id:
    type: string
    format: uuid
    description: The id of the unit template version
  title:
    description: The title of the unit
    type: string
  thumbnail:
    description: The link to the thumbnail image of the unit
    type: string
    maxLength: 512
  competencies:
    description: The list of competencies associated with the unit
    type: array
    items:
      minItems: 1
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
  start_date:
    description: The time the unit starts in ISO8601 format
    type: string
    format: date-time
  end_date:
    description: The time the unit ends in ISO8601 format
    type: string
    format: date-time
  created_at:
    description: The time the unit was created in ISO8601 format
    type: string
    format: date-time
