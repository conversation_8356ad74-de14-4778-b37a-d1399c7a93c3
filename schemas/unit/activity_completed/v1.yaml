$id: https://schema.multiverse.io/unit/activity_completed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever an activity in a unit is completed
required:
  - id
  - account_id
  - activity_id
  - completed_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the unit
  account_id:
    type: string
    format: uuid
    description: The globally unique id of the activity instance
  activity_id:
    type: string
    format: uuid
    description: The id of the activity
  completed_at:
    description: The time at which the activity was completed in ISO8601 format
    type: string
    format: date-time
