$id: https://schema.multiverse.io/pathway/created/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a pathway is created for a learner
required:
  - id
  - name
  - units
  - created_at
properties:
  id:
    description: The id of the pathway
    type: string
    format: uuid
  name:
    description: The name of the pathway
    type: string
  account_id:
    description: The account id of the learner
    type: string
    format: uuid
  thumbnail:
    description: The link to the thumbnail image for the pathway
    type: string
    maxLength: 512
  units:
    description: The list of units contained in the pathway
    type: array
    items:
      minItems: 1
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the unit
          type: string
          format: uuid
  created_at:
    description: The time the pathway was created in ISO8601 format
    type: string
    format: date-time
