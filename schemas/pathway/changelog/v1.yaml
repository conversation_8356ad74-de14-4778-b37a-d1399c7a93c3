$id: https://schema.multiverse.io/pathway/changelog/1.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a pathway is modified
required:
  - id
  - name
  - learner_account_id
  - elements
  - created_at
properties:
  id:
    description: The id of the pathway
    type: string
    format: uuid
  name:
    description: The name of the pathway
    type: string
  learner_account_id:
    description: The account id of the learner
    type: string
    format: uuid
  type:
    description: The type of the pathway
    type: string
    enum: ["LEVY", "SAAS"]
    default: "SAAS"
  apprenticeship:
    description: The apprenticeship details if the pathway type is LEVY
    type: object
    required:
      - id
    properties:
      id:
        description: The id of the apprenticeship
        type: string
        format: uuid
  user_goal_id:
    description: The user goal id associated to the pathway
    type: string
    format: uuid
  thumbnail:
    description: The link to the thumbnail image for the pathway
    type: string
    maxLength: 512
  elements:
    description: The ordered list of pathway elements
    type: array
    items:
      type: object
      required:
        - id
        - type
        - object_id
      properties:
        id:
          description: A unique identifier for this element in the pathway
          type: string
          format: uuid
        type:
          description: The type of the element
          type: string
          enum: ["unit", "project"]
        object_id:
          description: The ID of the unit or project that the element references
          type: string
          format: uuid
        module_id:
          description: The id of the module this element belongs to (if any)
          type: string
          format: uuid
        deadline:
          description: The deadline for completing this element
          type: string
          format: date-time
  modules:
    description: Metadata for modules in the pathway (unordered)
    type: array
    items:
      type: object
      required:
        - id
        - title
      properties:
        id:
          description: The unique identifier for the module
          type: string
          format: uuid
        title:
          description: The name of the module
          type: string
        deadline:
          description: The deadline for completing this module
          type: string
          format: date-time
  created_at:
    description: The time the pathway was created in ISO8601 format
    type: string
    format: date-time
if:
  properties:
    type:
      const: "LEVY"
then:
  required:
    - apprenticeship
