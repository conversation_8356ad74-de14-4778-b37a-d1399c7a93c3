$id: https://schema.multiverse.io/notification/send/0.6.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a notification is triggered for a user.
required:
  - id
  - knockWorkflowKey
  - recipientAccountId
  - workflowEnabled
properties:
  id:
    type: string
    description: The id of the notification.
    format: uuid
  knockWorkflowKey:
    type: string
    description: The human-readable key of the Knock workflow to trigger upon receipt of the event.
  recipientAccountId:
    type: string
    description: The account id of the recipient of the notification.
    format: uuid
  workflowEnabled:
    type: boolean
    description: Whether the related Knock workflow should be triggered when this event is consumed.
  optionalFields:
    type: object
    properties:
      actionUrl:
        type: string
        description: The fully qualified URL of any actions to be taken as part of the notification, e.g. for links to be included in an email.
