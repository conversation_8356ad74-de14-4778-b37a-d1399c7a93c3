$id: https://schema.multiverse.io/notification/send_calendar_invite/0.2.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a calendar invite notification is triggered for a user (invite or cancel).
required:
  - body
  - eventId
  - eventType
  - guestEmail
  - ics
  - id
  - inviteType
  - knockWorkflowKey
  - subject
  - workflowEnabled
properties:
  body:
    type: string
    description: The body content for the calendar invite email.
  eventId:
    type: string
    description: The ID of the event this invite is for (for tracking/logging).
  eventType:
    type: string
    description: The type of event this invite is for (e.g., 'delivery_session', 'meeting').
  guestEmail:
    type: string
    description: The email address of the guest who is receiving the invite.
  ics:
    type: string
    description: The base64 encoded ICS content.
  id:
    type: string
    description: The id of the notification.
    format: uuid
  inviteType:
    type: string
    enum: ["create", "update", "cancel"]
    description: The type of invite (e.g., 'create', 'update', 'cancel').
  subject:
    type: string
    description: The subject line for the calendar invite email.
  workflowEnabled:
    type: boolean
    description: Whether the related Knock workflow should be triggered when this event is consumed.
  knockWorkflowKey:
    type: string
    description: The key of the Knock workflow to trigger.
