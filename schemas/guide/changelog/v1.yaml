$id: https://schema.multiverse.io/guide/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Represents a guide known to the guide registry. guides are users of the Multiverse
  platform who have specific roles, such as `staff:coach`. A new guide will be created when
  a new user account is created with such a role or when such a role is added to an existing
  user account.
type: object
required:
  - id
  - email
  - tags
  - available
  - created_at
  - updated_at
properties:
  id:
    type: string
    description: The id of the guide, which matches their account id
    format: uuid
  email:
    type: string
    description: The email address of the guide
  first_name:
    type: string
    description: The first name of the guide
  last_name:
    type: string
    description: The first name of the guide
  tags:
    type: object
    description: |
      The tags used to list the guide's abilities. This will be an object where
      each property is a list of tags of a given type. We're still not documenting
      the different keys/tag types as those are still constantly changing.
    properties:
      competency_ids:
        type: array
        description: List of competency ids the guide is skilled on
        items:
          type: string
          format: uuid
      types:
        type: array
        description: List of types the guide is able to be allocated to
        items:
          type: string
          enum: ["guide", "instructor", "cohort_coach", "marking_coach", "launch_coach", "success_coach", "coach"]
      company_ids:
        type: array
        description: List of company ids the guide is associated with
        items:
          type: string
          format: uuid
  available:
    type: boolean
    description: Whether the guide is available to be allocated to allocations
    format: boolean
  created_at:
    type: string
    description: The time this guide was created in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The time this guide was updated in ISO8601 format
    format: date-time
