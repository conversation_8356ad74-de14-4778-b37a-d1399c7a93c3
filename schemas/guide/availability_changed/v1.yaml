$id: https://schema.multiverse.io/guide/availability_changed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
$description: Event is published whenever the availability of a guide changes.
type: object
required:
  - id
  - available
  - availability_updated_at
properties:
  id:
    type: string
    description: The id of the guide, which matches their account id
    format: uuid
  available:
    type: boolean
    description: Whether the guide is available to be allocated to allocations
    format: boolean
  availability_updated_at:
    type: string
    description: The time this guide availability was updated in ISO8601 format
    format: date-time
