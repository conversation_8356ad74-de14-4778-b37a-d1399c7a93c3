$id: https://schema.multiverse.io/guide/created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: |
  Event published whenever a new guide is added to the guide registry.
  Guides are users of the Multiverse platform who have specific roles,
  such as `staff:coach`. A new guide will be created when a new user account
  is created with such a role or when such a role is added to an existing user account.
type: object
required:
  - id
  - email
  - created_at
properties:
  id:
    type: string
    description: The id of the guide, which matches their account id
    format: uuid
  email:
    type: string
    description: The email address of the guide
  first_name:
    type: string
    description: The first name of the guide
  last_name:
    type: string
    description: The first name of the guide
  created_at:
    type: string
    description: The time this guide was created in ISO8601 format
    format: date-time
