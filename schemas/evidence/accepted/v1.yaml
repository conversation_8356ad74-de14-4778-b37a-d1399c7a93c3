$id: https://schema.multiverse.io/evidence/accepted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The event dispatched on the when a submitted evidence is accepted.
required:
  - id
  - account_id
  - evidence_type
  - updated_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the evidence.
  account_id:
    type: string
    format: uuid
    description: The uuid of the account.
  evidence_type:
    type: string
    description: The type of evidence.
    enum:
      - english_exam_certificate
      - maths_exam_certificate
      - proof_of_name_change
      - confirm_no_remark
      - other
  updated_at:
    type: string
    format: date-time
    description: The date and time the evidence status was updated to accepted.
