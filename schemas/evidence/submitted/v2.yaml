$id: https://schema.multiverse.io/evidence/submitted/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The event dispatched on the submission of evidence.
required:
  - id
  - account_id
  - evidence_type
  - qualification_type
  - qualification_grade
  - updated_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the evidence.
  account_id:
    type: string
    format: uuid
    description: The uuid of the account.
  evidence_type:
    type: string
    description: The type of evidence.
    enum:
      - english_exam_certificate
      - maths_exam_certificate
      - proof_of_name_change
      - confirm_no_remark
      - other
  qualification_type:
    type: string
    description: The name and level of a known qualification.
  qualification_grade:
    type: string
    description: The qualification grade.
  overseas_qualification_title:
    type: string
    description: The qualification title, if it was obtained overseas.
  overseas_qualification_country:
    type: string
    description: The country the qualification was obtained in, if it was obtained overseas.
  overseas_qualification_level:
    type: string
    description: The level of qualification, if it was obtained overseas.
    enum:
      - school
      - degree
  updated_at:
    type: string
    format: date-time
    description: The date and time the evidence was submitted.
