$id: https://schema.multiverse.io/evidence/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The changelog event published when evidence is modified.
required:
  - id
  - account_id
  - name
  - status
  - evidence_type
  - apprenticeship_id
  - created_at
  - updated_at
properties:
  id:
    type: string
    format: uuid
    description: The id of the evidence.
  account_id:
    type: string
    format: uuid
    description: The uuid of the account.
  name:
    type: string
    description: The name of the evidence.
  status:
    type: string
    description: The status of the evidence.
    enum:
      - submitted
      - accepted
      - rejected
      - requested
      - inReview
  evidence_type:
    type: string
    description: The type of evidence.
    enum:
      - english_exam_certificate
      - maths_exam_certificate
      - proof_of_name_change
      - confirm_no_remark
      - other
  rejection_code:
    type: string
    description: The reason the evidence was rejected, if any.
  qualification_type:
    type: string
    description: The name and level of a known qualification.
  qualification_grade:
    type: string
    description: The qualification grade.
  apprenticeship_id:
    type: string
    format: uuid
    description: The id of the apprenticeship if the evidence is related to an apprenticeship.
  qualification_country:
    type: string
    description: The country the qualification was obtained in.
  overseas_qualification_title:
    type: string
    description: The qualification title, if it was obtained overseas.
  overseas_qualification_level:
    type: string
    description: The level of qualification, if it was obtained overseas.
    enum:
      - school
      - degree
  created_at:
    type: string
    format: date-time
    description: The date and time the evidence was created.
  updated_at:
    type: string
    format: date-time
    description: The date and time the evidence was updated.
