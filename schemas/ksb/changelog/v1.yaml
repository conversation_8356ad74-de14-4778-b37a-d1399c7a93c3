$id: https://schema.multiverse.io/ksb/changelog/1.5.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a KSB is updated
required:
  - id
  - title
  - type
  - met_threshold
  - exceeded_threshold
  - version
  - previous_versions
  - created_at
  - updated_at
properties:
  id:
    description: The id of the KSB
    type: string
    format: uuid
  title:
    description: The title of the KSB
    type: string
  description:
    description: The description of the KSB
    type: string
  type:
    description: The KSB type
    type: string
    enum: ["knowledge", "skill", "behaviour"]
  tag:
    description: Optional tag for the KSB e.g. "K1", "S1", "B1"
    type: string
  met_threshold:
    description: The score needed to meet the KSB's expectations
    type: number
    minimum: 0
    maximum: 100
  met_explanation:
    description: Optional explanation of what is required to meet the KSB's expectations
    type: string
  met_feedback:
    description: Optional feedback given when the KSB's expectations are met
    type: string
  exceeded_threshold:
    description: The score needed to exceed the KSB's expectations
    type: number
    minimum: 0
    maximum: 100
  exceeded_explanation:
    description: Optional explanation of what is required to exceed the KSB's expectations
    type: string
  exceeded_feedback:
    description: Optional feedback given when the KSB's expectations are exceeded
    type: string
  more_work_required_feedback:
    description: Optional feedback given when the KSB's expectations are not met
    type: string
  version:
    description: The version number of the KSB
    type: number
  previous_versions:
    description: The list of previous versions of the KSB
    type: array
    items:
      minItems: 0
      uniqueItems: true
      type: object
      required:
        - id
        - version
        - created_at
      properties:
        id:
          description: The id of the KSB
          type: string
          format: uuid
        created_at:
          description: The time the KSB was created in ISO8601 format
          type: string
          format: date-time
        version:
          description: The version number of the KSB
          type: number
  created_at:
    description: The time the KSB was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the KSB was updated in ISO8601 format
    type: string
    format: date-time
  grading_primer:
    description: The grading primer for the KSB
    type: string
  curriculum_id:
    description: The id of the curriculum the KSB belongs to
    type: string
    format: uuid
  assessment_method:
    description: NOTE this field is deprecated. Please use standard_assessment_method instead. (am1 -> work_based and am2 -> portfolio)
    type: string
    enum: ['am1', 'am2']
  standard_assessment_method:
    description: The assessment method for the KSB defined by the standard
    type: string
    enum: ['work_based', 'portfolio']
  grading_criteria:
    description: The list of grading criteria for the KSB
    type: array
    items:
      type: object
      required:
        - id
        - order
        - criterion_text
        - target_grade
      properties:
        id:
          description: The id of the grading criterion
          type: string
          format: uuid
        order:
          description: The order of the grading criterion
          type: number
        criterion_text:
          description: The text of the grading criterion
          type: string
        target_grade:
          description: The target grade for the grading criterion
          type: string
          enum: ["MEETS_EXPECTATIONS", "EXCEEDS_EXPECTATIONS"]
        created_at:
          description: The time the grading criterion was created in ISO8601 format
          type: string
          format: date-time
        modified_at:
          description: The time the grading criterion was last modified in ISO8601 format
          type: string
          format: date-time
