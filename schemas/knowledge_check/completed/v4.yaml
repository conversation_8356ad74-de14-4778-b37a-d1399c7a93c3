$id: https://schema.multiverse.io/knowledge_check/completed/4.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a quiz with knowledge_check enabled has been completed
required:
  - id
  - title
  - learner_account_id
  - response_id
  - started_at
  - completed_at
  - questions
properties:
  id:
    description: The ID of the knowledge check quiz.
    type: string
    format: uuid
  title:
    description: The title of the knowledge check quiz.
    type: string
  learner_account_id:
    description: The ID of the learner undertaking the knowledge check quiz.
    type: string
    format: uuid
  response_id:
    description: The ID of the response to the knowledge check quiz.
    type: string
    format: uuid
  unit_id:
    description: The unit ID linked to the quiz, only present for SaaS knowledge checks.
    type: string
    format: uuid
  pathway_id:
    description: The pathway ID linked to the quiz, only present for SaaS knowledge checks.
    type: string
    format: uuid
  competency_id:
    description: The competency ID linked to the quiz, only present for SaaS knowledge checks.
    type: string
    format: uuid
  score:
    description: The score the learner has obtained after finishing the quiz. This is an optional field with a percentage value that is calculated for the learner. This can be null if a quiz is completed without a quiz answer.
    type: number
  started_at:
    description: The time the learner started the quiz in ISO8601 format
    type: string
    format: date-time
  completed_at:
    description: The time the learner completed the quiz in ISO8601 format
    type: string
    format: date-time
  questions:
    type: array
    description: The questions that form the quiz with the answers submitted by the learner.
    items:
      type: object
      required:
        - id
        - content
        - type
      properties:
        id:
          description: The ID of the question.
          type: string
          format: uuid
        content:
          description: The HTML content of the quiz question.
          type: string
          contentMediaType: text/html
        type:
          type: string
          description: The type of quiz question. The types primarily vary between choice based questions and text answer questions. The scores are calculated differently for different question types.
          enum:
            - MULTIPLE_CHOICE
            - OPEN_ENDED
            - NUMBER_MATCH
            - TEXT_MATCH
        options:
          type: array
          description: The options that are associated with a question. This will be an empty array for questions that are not option based.
          items:
            type: object
            required:
              - id
              - content
              - is_correct
            properties:
              id:
                description: The ID of the option.
                type: string
                format: uuid
              content:
                description: The text content that comprises each option. This contains HTML text.
                type: string
                contentMediaType: text/html
              is_correct:
                type: boolean
                description: A flag that indicates if this is (one of) the correct option(s).
        answer:
          type: object
          description: The answer submitted by the learner to the question.
          required:
            - id
            - content
          properties:
            id:
              description: The ID of the Answer.
              type: string
              format: uuid
            content:
              description: The text content of each answer. This field is left blank for questions that have options and contains a HTML string for text match, number match, or open ended questions.
              type: string
              contentMediaType: text/html
            is_correct:
              type: boolean
              description: A flag that indicates if the answer is correct.
            selected_options:
              type: array
              description: An array that contains all the selected options for a question. This is only applicable if the question is an option based question.
              items:
                type: string
                format: uuid
                uniqueItems: true
