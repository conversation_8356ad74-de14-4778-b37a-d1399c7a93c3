$id: https://schema.multiverse.io/competency_module/changelog/1.4.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a competency module is updated
required:
  - id
  - name
  - order
  - competencies
  - created_at
  - updated_at
properties:
  id:
    description: The id of the competency module
    type: string
    format: uuid
  learning_uid:
    description: |
      A manually created internal facing unique identifier for the competency module.
      Learning uses these to cross-reference and identify competency modules.
      Example values would be "AI-1" or "SWE-42".
    type: string
  name:
    description: The name of the competency module
    type: string
  description:
    description: The description of the competency module
    type: string
  proficiency_level:
    description: The proficiency level required to undertake this module
    type: string
    enum: ["beginner", "intermediate", "advanced"]
  order:
    description: The order of the competency module
    type: number
  competencies:
    description: The list of competencies in the competency module
    type: array
    items:
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
  prerequisites:
    description: |
      The competency modules which are prerequisites of this competency module.
      Only direct prerequisites are listed (i.e. not prerequisites of
      prerequisites). Order is not significant.
    type: array
    items:
      uniqueItems: true
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the prerequisite competency module
          type: string
          format: uuid      
  created_at:
    description: The time the competency module was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the competency module was updated in ISO8601 format
    type: string
    format: date-time
  deleted_at:
    description: The time the competency module was soft deleted in ISO8601 format
    type: string
    format: date-time
