$id: https://schema.multiverse.io/programme/changelog/3.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The programme changelog message.
required:
  - id
  - name
  - email_address
  - created_at
  - updated_at
properties:
  id:
    type: string
    description: The programme's id
    format: uuid
  name:
    type: string
    description: The programme's name
  email_address:
    type: ["string", "null"]
    description: The programme's email address
  created_at:
    type: string
    description: The programme's creation date in ISO8601 format
    format: date-time
  updated_at:
    type: string
    description: The programme's last update date in ISO8601 format
    format: date-time
  marketing_url:
    type: ["string", "null"]
    description: The programme's marketing URL
