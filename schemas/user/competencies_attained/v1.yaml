$id: https://schema.multiverse.io/user/competencies_attained/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
  - user
properties:
  id:
    type: string
    description: The id of the user
    format: uuid
  user:
    type: object
    required:
      - id
      - competencies
    properties:
      id:
        type: string
        description: The id of the user
        format: uuid
      competencies:
        type: array
        description: List of competencies attained by the user
        items:
          type: object
          required:
            - id
            - score
          properties:
            id:
              type: string
              description: The id of the competency
              format: uuid
            score:
              type:
                - number
                - 'null'
              description: The score the user has attained for the competency - null if the competency was attained via inheritance
