$id: https://schema.multiverse.io/user/project_progressed/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
description: Event representing a user's progress on a project, including submissions and reviews

# Define project submission states
x-project-submission-states: &project_submission_states
  - in_progress
  - awaiting_review
  - needs_more_work
  - reflect_on_feedback
  - completed

type: object
required:
  - learner_account_id
  - project_id
  - project_state
  - submissions
properties:
  learner_account_id:
    type: string
    description: The ID of the learner
    format: uuid
  project_id:
    type: string
    description: The ID of the project
    format: uuid
  project_state:
    type: string
    description: The global state of the project computed based on submission states
    enum: *project_submission_states
  submissions:
    type: array
    description: List of all submissions made by the learner for this project
    items:
      type: object
      required:
        - submission_id
        - submission_state
        - submission_started_at
      properties:
        submission_id:
          type: string
          description: The ID of the project submission
          format: uuid
        submission_state:
          type: string
          description: The state of this individual submission
          enum: *project_submission_states
        submission_started_at:
          type: string
          description: Timestamp when the submission was created
          format: date-time
        submission_submitted_at:
          type: string
          description: Timestamp when the submission was submitted for review. Not present for draft submissions.
          format: date-time
        submission_completed_at:
          type: string
          description: Timestamp when the submission was marked as complete
          format: date-time
        reviews:
          type: array
          description: List of reviews made on this submission
          items:
            type: object
            required:
              - review_id
              - review_state
              - reviewer_account_id
              - review_started_at
            properties:
              review_id:
                type: string
                description: The ID of the project review
                format: uuid
              review_state:
                type: string
                description: The state of this review
                enum:
                  - draft
                  - done
              reviewer_account_id:
                type: string
                description: The account ID of the reviewer
                format: uuid
              review_started_at:
                type: string
                description: Timestamp when the review was created
                format: date-time
  apprenticeship_id:
    description: The ID of the apprenticeship associated with the project submission. Only included for Levy projects.
    type: string
    format: uuid
