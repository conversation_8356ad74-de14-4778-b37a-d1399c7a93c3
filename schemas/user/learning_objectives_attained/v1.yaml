$id: https://schema.multiverse.io/user/learning_objectives_attained/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published when a user attains a learning objective
required:
  - user_id
  - learning_objectives
properties:
  user_id:
    type: string
    description: The id of the user
    format: uuid
  learning_objectives:
    type: array
    description: List of learning objectives attained by the user
    items:
      type: object
      required:
        - id
        - assessment_summary
        - attained_at
      properties:
        id:
          type: string
          description: The id of the learning objective
          format: uuid
        assessment_summary:
          type: string
          description: The assessment of the learning objective
          enum: ["met", "exceeded"]
        attained_at:
          description: The time that the learning objective was attained at in ISO8601 format - null if the learning objective was attained via inheritance
          type:
            - string
            - "null"
          format: date-time
