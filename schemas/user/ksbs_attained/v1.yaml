$id: https://schema.multiverse.io/user/ksbs_attained/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
  - user
properties:
  id:
    type: string
    description: The id of the user
    format: uuid
  user:
    type: object
    required:
      - id
      - ksbs
    properties:
      id:
        type: string
        description: The id of the user
        format: uuid
      ksbs:
        type: array
        description: List of KSBs attained by the user
        items:
          type: object
          required:
            - id
            - score
          properties:
            id:
              type: string
              description: The id of the KSB
              format: uuid
            score:
              type: 
                - number
                - 'null'
              description: The score the user has attained for the KSB - null if the KSB was attained via inheritance
