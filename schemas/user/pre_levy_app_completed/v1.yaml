$id: https://schema.multiverse.io/user/pre_levy_app_completed/1.0.1
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a levy learner has completed the pre-application steps (i.e. completed a skills scan). This can be consumed by the Platform to generate a user member, so the learner can later apply for an apprenticeship.
required:
  - account_id
  - company_id
properties:
  account_id:
    description: The account id of the user who completed the pre-application steps
    type: string
    format: uuid
  company_id:
    description: The id of the customer/company the user is associated with
    type: string
    format: uuid
