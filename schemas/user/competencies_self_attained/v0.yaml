$id: https://schema.multiverse.io/user/competencies_self_attained/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
  - user
properties:
  id:
    description: The id of the user account
    type: string
    format: uuid
  user:
    type: object
    required:
      - id
      - competencies
    properties:
      id:
        description: The id of the user account
        type: string
        format: uuid
      competencies:
        type: array
        description: List of competencies attained by the user via self evidence
        items:
          minItems: 1
          type: object
          required:
            - id
            - score
          properties:
            id:
              description: The id of the competency
              type: string
              format: uuid
            score:
              description: The score the user has attained for the competency via self evidence - null if the competency was attained via inheritance
              type:
                - number
                - 'null'
              minimum: 0
              maximum: 100
