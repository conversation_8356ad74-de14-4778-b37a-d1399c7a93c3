$id: https://schema.multiverse.io/curriculum/changelog/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  An event published whenever a curriculum is updated. A curriculum is a
  multiverse resource that specifies the learning content that should be
  included to teach a certain qualification, for example an apprenticeship
  standard.
required:
  - id
  - learning_uid
  - version_number
  - title
  - status
  - updated_at
  - created_at
  - competency_modules
properties:
  id:
    type: string
    description: The id of the curriculum
    format: uuid
  learning_uid:
    type: string
    description: >
      A manually created internal facing unique identifier for the curriculum.
      The learning_uid and version_number fields together form a unique
      identifier for the curriculum.
  version_number:
    type: integer
    description: The version number of the curriculum
    minimum: 1
  title:
    type: string
    description: The title of the curriculum
  status:
    type: string
    description: >
      The status of the curriculum.  A curriculum starts as a draft, once it is
      ready to be used it is published.  If it should no longer be used for
      creating new content, then it can be deprecated.
    enum:
      - draft
      - published
      - deprecated
  created_at:
    type: string
    format: date-time
    description: > 
      The date and time the curriculum was created at Multiverse in ISO8601
      format
  updated_at:
    type: string
    format: date-time
    description: >
      The date and time the curriculum was updated at Multiverse in ISO8601
      format
  uk_apprenticeship_standard:
    type: object
    required:
      - reference_number
      - version
    description: >
      The IfATE apprenticeship standard that the curriculum implements, if
      applicable.
    properties:
      reference_number:
        type: string
        description: >
          The IfATE reference number of the apprenticeship standard
      version:
        type: string
        description: >
          The IfATEversion number of the apprenticeship standard
      occupational_standard_codes:
        type: array
        description: >
          The IfATE standard codes of the occupations this curriculum
          implements, if applicable.  If omitted, the curriculum is relevant to
          all occupations linked to the apprenticeship standard.
        items:
          type: string
        uniqueItems: true
        minItems: 1
  competency_modules:
    type: array
    description: >
      The competency modules that are part of the curriculum
    items:
      type: object
      required:
        - id
        - learning_uid
      properties:
        id:
          type: string
          format: uuid
          description: The unique id of the competency module
        learning_uid:
          type: string
          description: >
            The learning uid of the competency module - several competency
            modules may share the same learning_uid
    uniqueItems: true
