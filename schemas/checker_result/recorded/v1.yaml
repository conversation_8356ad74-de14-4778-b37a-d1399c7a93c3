$id: https://schema.multiverse.io/checker_result/recorded/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The checker_result recorded event.
required:
  - id
  - published_at
  - account_id
  - diagnostic_succeeded
properties:
  id:
    description: The id of the checker_result.recorded event
    type: string
    format: uuid
  account_id:
    type: string
    description: The accounts service id of the user to whom is this checker_result associated.
    format: uuid
  diagnostic_succeeded:
    type: boolean
    description: The overall checker_result status. If any of Checker's checks fails, 'diagnosticSucceeded' will be set to 'false', if all checks pass, 'diagnosticSucceeded' will be set to 'true'
  published_at:
    description: The time the checker_result event was created in ISO8601 format
    type: string
    format: date-time
  checks:
    type: array
    description: The list of all executed checks.
    items:
      uniqueItems: true
      type: object
      required:
        - metric
        - succeeded
      properties:
        metric:
          type: string
          description: The name/identifier of a specific check. # Deliberately not an enum, so that the event schema does not have to be updated with every new check
        succeeded:
          type: boolean
          description: The result of this specific metric
