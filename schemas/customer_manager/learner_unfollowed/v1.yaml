$id: https://schema.multiverse.io/customer_manager/learner_unfollowed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a manager is un-following a learner (Saas).
required:
  - id
  - learner_account_id
  - followed_at
properties:
  id:
    description: The account id of the manager that has un-followed the learner.
    type: string
    format: uuid
  learner_account_id:
    description: The account id of the learner that has been un-followed.
    type: string
    format: uuid
  unfollowed_at:
    type: string
    description: Timestamp of the un-follow.
    format: date-time
