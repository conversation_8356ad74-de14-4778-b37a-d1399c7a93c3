$id: https://schema.multiverse.io/learning_session/created/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: Event published when a learning session is created
required:
  - id
  - account_id
  - start_date
  - day_of_week
  - duration_in_minutes
  - calendar_invite_sent
  - cancelled_at
properties:
  id:
    description: The id of the learning session
    type: string
    format: uuid
  account_id:
    type: string
    description: The account id of the user to whom is this event associated
    format: uuid
  start_date:
    description: Datetime for the first learning session in a user's schedule
    type: string
    format: date-time
  day_of_week:
    type: string
    description: The day of the week for when the learning session is scheduled
    enum:
      [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
      ]
  duration_in_minutes:
    description: The length of each learning session in minutes
    type: integer
  calendar_invite_sent:
    description: Whether a calendar invite has been sent for this learning session
    type: boolean
  cancelled_at:
    description: DateTime when a session was cancelled. If null, session was not cancelled.
    type: ["string", "null"]
    format: date-time
