$id: https://schema.multiverse.io/levy_programme_preferences/submitted/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a levy learner's programme preferences have been submitted.
required:
  - account_id
  - user_skills_scan_id
  - user_skills_scan_result_id
  - programmes
  - submitted_at
properties:
  account_id:
    description: The account id of the user.
    type: string
    format: uuid
  user_skills_scan_id:
    description: The corresponding skills scan survey instance id.
    type: string
    format: uuid
  user_skills_scan_result_id:
    description: The corresponding skills scan survey result id.
    type: string
    format: uuid
  programmes:
    type: array
    description: The list of programmes ranked by preference
    items:
      type: object
      required:
        - apprenticeship_qualification_id
        - rank
      properties:
        apprenticeship_qualification_id:
          type: string
          format: uuid
          description: The id of the programme
        rank:
          type: number
          description: The rank of the programme
  submitted_at:
    description: The time when the programme preferences were submitted in ISO8601 format
    type: string
    format: date-time
  level_of_interest:
    description: Response to the question "How interested are you in joining one of these Multiverse programmes?"  0 - Not at all, 4 - Extremely interested
    type: number
    minimum: 0
    maximum: 4
