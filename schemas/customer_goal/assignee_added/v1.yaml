$id: https://schema.multiverse.io/customer_goal/assignee_added/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: |
  An event published whenever a new assignee is added to a customer goal.
  
  This event is only published for learners who join a goal through broadcast links.
  Not published for learners invited individually via client-xp (their user goals are created immediately, before finalising signup).
  Note: The individual invitation flow may be deprecated in the future.
required:
  - id
  - assignee_account_id
  - added_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  assignee_account_id:
    description: Account id of the user assigned to the customer goal
    type: string
    format: uuid
  added_at:
    description: The time when the user was assigned to the customer goal in ISO8601 format
    type: string
    format: date-time
