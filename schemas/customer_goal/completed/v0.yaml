$id: https://schema.multiverse.io/customer_goal/completed/0.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a customer goal is completed
required:
  - id
  - status
  - progress
  - completed_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  status:
    description: The status of the customer goal, always 'completed' for this event
    type: string
    const: "completed"
  progress:
    description: Overall progress towards this goal, always 100 when the customer goal is completed
    type: number
    const: 100
  completed_at:
    description: The time when the customer goal was completed
    type: string
    format: date-time
