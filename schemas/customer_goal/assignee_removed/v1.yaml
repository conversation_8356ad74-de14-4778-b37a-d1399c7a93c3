$id: https://schema.multiverse.io/customer_goal/assignee_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever an assignee is removed from a customer goal
required:
  - id
  - assignee_account_id
  - removed_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  assignee_account_id:
    description: Account id of the user no longer assigned to the customer goal
    type: string
    format: uuid
  removed_at:
    description: The time when the user was removed from the customer goal in ISO8601 format
    type: string
    format: date-time
