$id: https://schema.multiverse.io/customer_goal/participant_role_added/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a participant role is added to a customer goal for a given user
required:
  - id
  - account_id
  - role
  - added_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  account_id:
    description: Account id of the user
    type: string
    format: uuid
  added_at:
    description: The time when the user's participant role was added to the customer goal in ISO8601 format
    type: string
    format: date-time
  role:
    type: string
    enum: ["owner"]
    description: The role added
