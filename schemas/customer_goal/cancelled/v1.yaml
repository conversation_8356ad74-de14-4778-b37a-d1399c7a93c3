$id: https://schema.multiverse.io/customer_goal/cancelled/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a customer goal is cancelled
required:
  - id
  - status
  - cancelled_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  status:
    description: The status of the customer goal, always 'cancelled' for this event
    type: string
    const: "cancelled"
  cancelled_at:
    description: The time when the customer goal was cancelled
    type: string
    format: date-time
