$id: https://schema.multiverse.io/customer_goal/invite_sent/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a learner is invited to join a customer goal
required:
  - id
  - account_id
  - invitation_status
  - invite_sent_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  account_id:
    description: The account id of the learner being invited to the customer goal
    type: string
    format: uuid
  invitation_status:
    description: The current status of the account invitation
    type: string
    enum: ["Not Applicable", "Not Sent", "Pending Response", "Accepted"]
  invite_sent_at:
    description: The time when the invite was sent in ISO8601 format
    type: string
    format: date-time

