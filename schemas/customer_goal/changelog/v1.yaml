$id: https://schema.multiverse.io/customer_goal/changelog/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event representing the current state of a customer goal
required:
  - id
  - title
  - description
  - status
  - type
  - company_id
  - account_id
  - diagnosis_id
  - assignee_count
  - progress
  - assignee_completion_count
  - competencies
  - created_at
  - updated_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  title:
    description: The title of the customer goal
    type: string
  description:
    description: The description of the customer goal
    type: string
  status:
    description: The status of the customer goal
    type: string
    enum: ["in_progress", "completed", "cancelled"]
  type:
    description: The type of customer goal, can be either 'company' or 'department'
    type: string
    enum: ["company", "department"]
  company_id:
    description: The id of the customer/company which owns the customer goal
    type: string
    format: uuid
  account_id:
    description: The account id of the user who submitted the needs used for the generation of this customer goal
    type: string
    format: uuid
  diagnosis_id:
    description: The id of the diagnosis used as input for the customer goal.
    type: string
    format: uuid
  assignee_count:
    description: Number of users assigned to this customer goal
    type: integer
    minimum: 0
  progress:
    description: Overall progress towards this customer goal, based on the current progress on the associated competencies
    type: number
    minimum: 0
    maximum: 100
  assignee_completion_count:
    description: Number of assignees who have completed this customer goal
    type: integer
    minimum: 0
  competencies:
    description: The list of competencies associated to this customer goal, including their progress information
    type: array
    items:
      type: object
      required:
        - id
        - progress
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
        progress:
          description: The current progress towards this goal as a number from 0 to 100
          type: number
          minimum: 0
          maximum: 100
  projects:
    description: The list of projects associated to this customer goal
    type: array
    items:
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the project
          type: string
          format: uuid
  created_at:
    description: The time the customer goal was created in ISO8601 format
    type: string
    format: date-time
  updated_at:
    description: The time the customer goal was updated in ISO8601 format
    type: string
    format: date-time
