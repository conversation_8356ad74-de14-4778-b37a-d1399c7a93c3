$id: https://schema.multiverse.io/customer_goal/participant_role_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a participant role is removed from a customer goal for a given user
required:
  - id
  - account_id
  - role
  - removed_at
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  account_id:
    description: Account id of the user
    type: string
    format: uuid
  removed_at:
    description: The time when the user's participant role was removed from the customer goal in ISO8601 format
    type: string
    format: date-time
  role:
    type: string
    enum: ["owner"]
    description: The role removed
