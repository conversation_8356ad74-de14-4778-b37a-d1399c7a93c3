$id: https://schema.multiverse.io/customer_goal/created/2.3.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever a new customer goal is created
required:
  - id
  - title
  - description
  - status
  - company_id
  - account_id
  - competencies
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  title:
    description: The title of the customer goal
    type: string
  description:
    description: The description of the customer goal
    type: string
  status:
    description: The status of the customer goal, 'in_progress' when the customer goal has just been created
    type: string
    const: "in_progress"
  company_id:
    description: The id of the customer/company which owns the diagnosis
    type: string
    format: uuid
  account_id:
    description: The account id of the user who submitted the needs used as input for the diagnosis
    type: string
    format: uuid
  diagnosis_id:
    description: The id of the diagnosis used as input for the customer goal.
    type: string
    format: uuid
  competencies:
    description: The list of competencies associated to this goal, including their progress information
    type: array
    items:
      minItems: 1
      uniqueItems: true
      type: object
      required:
        - id
        - progress
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
        progress:
          description: The current progress towards this goal, 0 when the customer goal has just been created
          type: number
          const: 0
  projects:
    description: The list of projects associated to this customer goal
    type: array
    items:
      type: object
      required:
        - id
      properties:
        id:
          description: The id of the project
          type: string
          format: uuid
  created_at:
    description: The time the customer goal was created in ISO8601 format
    type: string
    format: date-time
  is_levy:
    description: Whether the customer_goal is a levy funded or not. Can be assumed as false if not present.
    type: boolean
  skills_scans_enabled:
    description: Whether this customer goal expects skills scans to be sent when assignees are added
    type: boolean
    default: true
  learner_description:
    description: The learner-facing, layman-worded description of the customer goal
    type: string
