$id: https://schema.multiverse.io/customer_goal/progressed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: An event published whenever progress towards a goal changes
required:
  - id
  - progress
  - assignee_completion_count
  - competencies
properties:
  id:
    description: The id of the customer goal
    type: string
    format: uuid
  progress:
    description: Overall progress towards this customer goal, based on the current progress on the associated competencies
    type: number
    minimum: 0
    maximum: 100
  assignee_completion_count:
    description: Number of assignees who have completed this customer goal
    type: integer
    minimum: 0
  competencies:
    description: The list of competencies associated to this customer goal, including their progress information
    type: array
    items:
      type: object
      required:
        - id
        - progress
      properties:
        id:
          description: The id of the competency
          type: string
          format: uuid
        progress:
          description: The current progress towards this goal as a number from 0 to 100
          type: number
          minimum: 0
          maximum: 100
