$id: https://schema.multiverse.io/account/deactivated/2.0.1
$schema: https://json-schema.org/draft/2020-12/schema
description: "The account has been deactivated, anonymising user data and preventing the user from logging in and accessing any products. Unlike disabling (see: `account/disabled`) which retains all data. A deactivated account cannot be re-enabled later as all data is anonymised."
type: object
required:
  - id
  - deactivated_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  deactivated_at:
    type: string
    description: When the account was deactivated as a timestamp with timezone information, in ISO8601 format
    format: date-time
