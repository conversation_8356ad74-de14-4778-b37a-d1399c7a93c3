$id: https://schema.multiverse.io/account/changelog/3.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The account changelog message from account service.
required:
  - account
properties:
  account:
    type: object
    properties:
      id:
        type: string
        description: The account's id
        format: uuid
      first_name:
        type: ["string", "null"]
        description: The account's first name
      preferred_first_name:
        type: ["string", "null"]
        description: The account's preferred first name
      last_name:
        type: ["string", "null"]
        description: The account's last name
      email:
        type: string
        description: The account's email address
      roles:
        type: array
        description: The account's roles
        items:
          type: string
      mobile_number:
        type: ["string", "null"]
        description: The account's mobile number
      pronouns:
        type: ["string", "null"]
        description: The account's pronouns
      timezone:
        type: ["string", "null"]
        description: The account's timezone
      region:
        type: string
        description: The account's region
      image:
        type: ["string", "null"]
        description: The account's image URL
      active:
        type: boolean
        description: The account's active status. DEPRECATED. Use `status` instead.
      status:
        type: string
        enum:
          [
            "registered",
            "invited",
            "unverified",
            "enabled",
            "deactivated",
            "disabled",
            "gated",
          ]
        description: The account's status. Enabled is the state for an account that has been verified and is currently active.
      previous_status:
        type: ["string", "null"]
        enum:
          [
            "registered",
            "invited",
            "unverified",
            "enabled",
            "deactivated",
            "disabled",
            "gated",
          ]
        description: The account's previous status.
      token_expiry:
        type: ["string", "null"]
        format: "date-time"
        description: The account's most recent token expiry datetime. Can be in the past if the token has expired. Null if no token has been generated. Tokens are used to move from `unverified`, `invited` or `registered` to `enabled` status.
      invitation_status:
        type: string
        enum: ["Not Applicable", "Not Sent", "Pending Response", "Accepted"]
        description: The account's invitation status. DEPRECATED. Use `status` & `token_expiry` instead.
      policy_acceptances:
        description: >
          Lists different policy types that account holders have to accept and whether they've been accepted.
          A value of `true` indicates that the policy in question has been accepted and is current.
          A value of `false` indicates that either the policy was never accepted or that it needs to be re-accepted
          due to a newer version being released.
        type: object
        required:
          - terms_and_conditions
          - privacy_policy
        properties:
          terms_and_conditions:
            type: boolean
          privacy_policy:
            type: boolean
      inserted_at:
        type: "string"
        format: "date-time"
      updated_at:
        type: "string"
        format: "date-time"
        description: The last time the account was updated
      source:
        type: string
        description: The source of the account creation e.g. "platform", "client_xp", "auth0-scim" etc.
      company_id:
        type: ["string", "null"]
        description: The account's company id
        format: uuid
    required:
      - id
      - email
      - roles
      - region
      - active
      - invitation_status
      - status
      - token_expiry
