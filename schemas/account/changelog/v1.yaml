$id: https://schema.multiverse.io/account/changelog/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: The account changelog message from account service.
required:
  - account
properties:
  account:
    type: object
    properties:
      id:
        type: string
        description: The account's id
        format: uuid
      first_name:
        type: string
        description: The account's first name
      preferred_first_name:
        type: string
        description: The account's preferred first name
      last_name:
        type: string
        description: The account's last name
      email:
        type: string
        description: The account's email address
        format: email
      roles:
        type: array
        description: The account's roles
        items:
          type: string
      mobile_number:
        type: string
        description: The account's mobile number
      pronouns:
        type: string
        description: The account's pronouns
      timezone:
        type: string
        description: The account's timezone
      region:
        type: string
        description: The account's region
      image:
        type: string
        description: The account's image URL
      active:
        type: boolean
        description: The account's active status
      invitation_status:
        type: string
        enum: ["Not Applicable", "Not Sent", "Pending Response", "Accepted"]
        description: The account's invitation status
    required:
      - id
      - email
      - roles
      - region
      - active
      - invitation_status
