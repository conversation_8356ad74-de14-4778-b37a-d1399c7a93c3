$id: https://schema.multiverse.io/account/signed_up/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - account
  - id
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  account:
    type: object
    properties:
      id:
        type: string
        description: The account's id
        format: uuid
      first_name:
        type: string
        description: The account's first name
      last_name:
        type: string
        description: The account's last name
      email:
        type: string
        description: The account's email address
        format: email
      roles:
        type: array
        description: The account's roles
        items:
          type: string
    required:
      - id
      - first_name
      - last_name
      - email
      - roles
