$id: https://schema.multiverse.io/account/signed_up/2.1.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: "DEPRECATED. Use `account/registered` and `account/invited` instead."
required:
  - id
  - signed_up_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  signed_up_at:
    type: string
    description: When the account signed up as a timestamp with timezone information, in ISO8601 format
    format: date-time
