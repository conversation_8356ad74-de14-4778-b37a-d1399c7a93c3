$id: https://schema.multiverse.io/account/re_enabled/1.1.0
$schema: https://json-schema.org/draft/2020-12/schema
description: "DEPRECATED. Use `account/enabled` instead."
type: object
required:
  - id
  - re_enabled_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  re_enabled_at:
    type: string
    description: When the account was re-enabled as a timestamp with timezone information, in ISO8601 format
    format: date-time
