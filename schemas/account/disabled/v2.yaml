$id: https://schema.multiverse.io/account/disabled/2.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: "The account has been disabled, preventing the user from logging in and accessing any products. Unlike deactivation (see: `account/deactivated`) which anonymises user data. A disabled account retains all data and can be re-enabled later. Not to be confused with `account/gated`."
type: object
required:
  - id
  - reason
  - disabled_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  reason:
    type: string
    description: The reason the account was disabled
  disabled_at:
    type: string
    description: When the account was disabled as a timestamp with timezone information, in ISO8601 format
    format: date-time
