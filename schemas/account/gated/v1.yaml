$id: https://schema.multiverse.io/account/gated/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: "The account has been gated, preventing the user from accessing any of our products. The user can login and perform minimal set of actions, such as requesting their account to be deleted. Not to be confused with `account/disabled` which prevents the user from logging in and accessing any products."
type: object
required:
  - id
  - reason
  - gated_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  reason:
    type: string
    description: The reason the account was gated
  gated_at:
    type: string
    description: When the account was gated as a timestamp with timezone information, in ISO8601 format
    format: date-time
