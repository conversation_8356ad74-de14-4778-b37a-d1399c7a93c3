$id: https://schema.multiverse.io/account/registered/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
  - registered_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  registered_at:
    type: string
    description: When the user self-registered for an account as a timestamp with timezone information, in ISO8601 format. Does not include accounts created via invitation - see `account/invited` for those events.
    format: date-time
