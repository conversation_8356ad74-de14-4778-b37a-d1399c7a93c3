$id: https://schema.multiverse.io/account/enabled/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
description: "The account has been enabled, allowing the user to log in and access any products."
type: object
required:
  - id
  - enabled_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  enabled_at:
    type: string
    description: When the account was enabled as a timestamp with timezone information, in ISO8601 format
    format: date-time
