$id: https://schema.multiverse.io/account/policies_accepted/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
description: >
  Published whenever the account holder accepts one or more policies.
required:
  - account_id
  - accepted_policies
  - accepted_at
properties:
  account_id:
    type: string
    description: The account's id
    format: uuid
  accepted_policies:
    description: A list of policies that have been accepted by the account holder
    type: array
    items:
      type: string
      enum: ["privacy_policy", "terms_and_conditions"]
  accepted_at:
    description: The time the policies were accepted in ISO8601 format
    type: string
    format: date-time
