$id: https://schema.multiverse.io/account/role_removed/1.0.0
$schema: https://json-schema.org/draft/2020-12/schema
type: object
required:
  - id
  - roles
  - updated_at
properties:
  id:
    type: string
    description: The account's id
    format: uuid
  roles:
    type: array
    description: The account's updated roles
    items:
      type: string
  updated_at:
    type: string
    description: When the role was removed as a timestamp with timezone information, in ISO8601 format
    format: date-time
