#!/bin/bash

# Check if an argument was provided
if [ $# -ne 1 ]; then
    echo "Usage: $0 <number_of_duplicates>"
    exit 1
fi

# Store the number of duplicates
NUM_DUPLICATES=$1

# Check if the input is a positive integer
if ! [[ "$NUM_DUPLICATES" =~ ^[0-9]+$ ]]; then
    echo "Error: Please provide a positive integer as the number of duplicates."
    exit 1
fi

# Source directory
SOURCE_DIR="schemas/apprenticeship/summary_updated"

# Check if source directory exists
if [ ! -d "$SOURCE_DIR" ]; then
    echo "Error: Source directory '$SOURCE_DIR' does not exist."
    exit 1
fi

# Array of letters to use (lowercase a-z)
LETTERS=(a b c d e f g h i j k l m n o p q r s t u v w x y z)

# Function to generate a letter code for a number
# For 1-26, returns a single letter (a-z)
# For >26, returns combinations (aa, ab, ac, etc.)
generate_letter_code() {
    local num=$1
    local letter_code=""
    
    if [ $num -le 26 ]; then
        # For 1-26, just use the corresponding letter
        letter_code=${LETTERS[$((num-1))]}
    else
        # For >26, calculate the combination
        local temp=$((num-1))
        
        while [ $temp -ge 0 ]; do
            letter_code="${LETTERS[$((temp % 26))]}${letter_code}"
            temp=$((temp / 26 - 1))
        done
    fi
    
    echo "$letter_code"
}

# Loop to create the duplicates
for i in $(seq 1 $NUM_DUPLICATES); do
    # Get letter code for this index
    letter=$(generate_letter_code $i)

    # Target directory name
    TARGET_DIR="schemas/apprenticeship/summary_updated_$letter"

    # Check if target directory already exists
    if [ -d "$TARGET_DIR" ]; then
        echo "Target directory '$TARGET_DIR' already exists, removing it first"
        rm -rf "$TARGET_DIR"
    fi

    # Create the target directory by copying the source
    echo "Creating duplicate $i with letter code '$letter' at $TARGET_DIR"
    mkdir -p "$(dirname "$TARGET_DIR")"
    cp -r "$SOURCE_DIR" "$TARGET_DIR"

    # Update the $id in the v0.yaml file
    if [ -f "$TARGET_DIR/v0.yaml" ]; then
        echo "Updating $id in $TARGET_DIR/v0.yaml"
        # Use platform-compatible sed command
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS version (BSD sed)
            sed -i '' "s|\\$id: https://schema.multiverse.io/apprenticeship/summary_updated/|\\$id: https://schema.multiverse.io/apprenticeship/summary_updated_$letter/|" "$TARGET_DIR/v0.yaml"
        else
            # Linux version (GNU sed)
            sed -i "s|\\$id: https://schema.multiverse.io/apprenticeship/summary_updated/|\\$id: https://schema.multiverse.io/apprenticeship/summary_updated_$letter/|" "$TARGET_DIR/v0.yaml"
        fi
    else
        echo "Warning: v0.yaml not found in $TARGET_DIR"
    fi
done

echo "Successfully created $NUM_DUPLICATES schema duplicates with letter codes." 
