#!/bin/bash

set -e

# Default accuracy
ACCURACY=10

# Handle command line argument for accuracy
if [ $# -eq 1 ]; then
  ACCURACY=$1
fi

# Initialize search range
MIN=1
MAX=1000
CURRENT=10 # Start with 100 as requested
HIGHEST_SUCCESS=-1
LOWEST_FAILURE=-1

# Store the original directory
ORIGINAL_DIR=$(pwd)

# Function to run code generation
run_codegen() {
  # Set the maximum memory allocation to 8GB to avoid out of memory errors
  export NODE_OPTIONS="--max_old_space_size=8192"

  local n=$1
  echo "Running with n=$n schemas..."

  # Step 1: Generate schema duplicates
  ./scripts/create_schema_duplicates.sh $n

  # Step 2: Run codegen and build
  if ./scripts/codegen.sh typescript && cd support/output/typescript && touch yarn.lock && yarn install && yarn build; then
    echo "SUCCESS: Code generation succeeded with n=$n"
    return 0
  else
    echo "FAILURE: Code generation failed with n=$n"
    return 1
  fi
}

# Function to clean up
cleanup() {
  # Return to original directory
  cd "$ORIGINAL_DIR"

  # Remove all generated schemas
  echo "Cleaning up generated schemas..."
  rm -rf schemas/apprenticeship/summary_updated_*
}

# Binary search loop
while true; do
  # Clean up from previous run
  cleanup

  # Run with current number of schemas
  if run_codegen $CURRENT; then
    # Success case
    HIGHEST_SUCCESS=$CURRENT

    # If we've previously found a failure, narrow the search
    if [ $LOWEST_FAILURE -ne -1 ]; then
      # Check if we're within the required accuracy
      if [ $((LOWEST_FAILURE - HIGHEST_SUCCESS)) -le $ACCURACY ]; then
        break
      fi

      # Calculate next value to try
      NEXT=$(((LOWEST_FAILURE + HIGHEST_SUCCESS) / 2))

      # If we can't narrow down further, we're done
      if [ $NEXT -eq $HIGHEST_SUCCESS ]; then
        break
      fi

      CURRENT=$NEXT
    else
      # No failure yet, double the value
      CURRENT=$((CURRENT * 2))
    fi
  else
    # Failure case
    LOWEST_FAILURE=$CURRENT

    # If we've previously found a success, narrow the search
    if [ $HIGHEST_SUCCESS -ne -1 ]; then
      # Check if we're within the required accuracy
      if [ $((LOWEST_FAILURE - HIGHEST_SUCCESS)) -le $ACCURACY ]; then
        break
      fi

      # Calculate next value to try
      NEXT=$(((HIGHEST_SUCCESS + LOWEST_FAILURE) / 2))

      # If we can't narrow down further, we're done
      if [ $NEXT -eq $HIGHEST_SUCCESS ]; then
        break
      fi

      CURRENT=$NEXT
    else
      # No success yet, halve the value
      CURRENT=$((CURRENT / 2))
    fi
  fi

  # Return to original directory before next iteration
  cd "$ORIGINAL_DIR"

  echo "Current search range: [$HIGHEST_SUCCESS, $LOWEST_FAILURE]"
  echo "Next try: $CURRENT"
  echo "-----------------------------------"
done

# Clean up final run
cleanup

echo "====================================="
echo "BINARY SEARCH RESULT SUMMARY"
echo "====================================="
echo "The script has determined the capacity limits for TypeScript code generation:"
echo ""
echo "✅ SUCCESSFUL: Up to $HIGHEST_SUCCESS additional new schemas can be added successfully"
echo "❌ FAILURE: Adding $LOWEST_FAILURE or more schemas causes the system to fail"
echo ""
echo "The search was conducted with an accuracy of $ACCURACY schemas."
echo "This means the exact failure threshold is somewhere between $HIGHEST_SUCCESS and $LOWEST_FAILURE additional schemas."
echo "====================================="
