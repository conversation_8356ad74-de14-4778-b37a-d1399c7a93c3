#!/bin/bash

set -e

base=${BASE_REF:-main}

changed_schemas=$(git diff --name-only "$base" --diff-filter=M | grep -E "schemas/.*.yaml" | xargs -r -n1 readlink -f)
all_schemas=$(find ~+/schemas ~+/test-schemas -type f -name "*.yaml")
yml_files=$(find ~+/schemas ~+/test-schemas -type f -name "*.yml")

pushd support > /dev/null

poetry install

source $(poetry env info --path)/bin/activate

echo "=== validating all schemas ==="

echo "$all_schemas" | xargs python event_support/main.py validate

echo "=== validating changed schemas ==="

if [ ! -z "$changed_schemas" ]; then
    echo "$changed_schemas" | xargs python event_support/main.py validate --diff "$base"
else
    echo "No changed schemas to validate"
fi

# warning for .yml files
if [ ! -z "$yml_files" ]; then
    echo ""
    echo "########## WARNING ##########"
    echo "The following .yml files need to renamed to .yaml:"
    for file in $yml_files
    do
        echo "$file"
    done

    exit 1
fi
