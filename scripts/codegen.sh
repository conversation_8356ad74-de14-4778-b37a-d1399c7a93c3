#!/bin/bash
#
# Event Schema Code Generator
#
# Usage:
#   ./codegen.sh                     - Run all generators
#   ./codegen.sh typescript python   - Run only specified generators
#
# Available generators:
#   - typescript
#   - python
#   - elixir
#   - backstage
#

set -e

# Initialize dependencies
pushd support >/dev/null
poetry install
npm install

# Get list of generators to run
GENERATORS=("$@")

# If no generators specified, run all
if [ ${#GENERATORS[@]} -eq 0 ]; then
    GENERATORS=("elixir" "typescript" "python" "backstage")
fi

# Run each requested generator
for generator in "${GENERATORS[@]}"; do
    case "$generator" in
    "elixir")
        echo "Generating Elixir code..."
        ./run generate elixir "../schemas"
        ;;
    "typescript")
        echo "Generating Typescript code..."
        ./run generate typescript "../schemas"
        ;;
    "python")
        echo "Generating Python code..."
        ./run generate python "../schemas"
        ./run generate python-test "../test-schemas"
        ;;
    "backstage")
        echo "Generating Backstage code..."
        ./run generate backstage "../schemas"
        ;;
    *)
        echo "Unknown generator: $generator"
        echo "Available generators: elixir, typescript, python, backstage"
        exit 1
        ;;
    esac
done

popd >/dev/null
