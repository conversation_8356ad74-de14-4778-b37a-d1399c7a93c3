#!/bin/bash

# =============================================================================
# ts_lib_to_local_project.sh
# =============================================================================
# 
# Description:
#   This script generates the TypeScript library from event schemas and copies
#   it to a specified local project's node_modules directory. It's useful for
#   testing schema changes in a local project without publishing a new version.
#
# Usage:
#   ./scripts/ts_lib_to_local_project.sh <target_directory>
#
# Arguments:
#   target_directory - Path to the target project where the library will be copied
#
# Example:
#   ./scripts/ts_lib_to_local_project.sh ../guidance_hub
#
# =============================================================================

# Check if help was requested or no arguments were provided
if [ $# -ne 1 ] || [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
  echo "Usage: $0 <target_directory>"
  echo "Example: $0 ../guidance_hub"
  echo ""
  echo "This script generates the TypeScript library from event schemas and copies"
  echo "it to the specified local project's node_modules directory."
  echo "It's useful for testing schema changes without publishing a new version."
  exit 1
fi

TARGET_DIR=$1

# Define the destination directory
DEST_DIR="$TARGET_DIR/node_modules/@multiverse-io/event-schemas-ts/dist/"

# Validate that the destination directory exists
if [ ! -d "$DEST_DIR" ]; then
  echo "Error: Destination directory '$DEST_DIR' does not exist"
  exit 1
fi

# Store the original directory
ORIGINAL_DIR=$(pwd)

# Ensure we return to the original directory on exit or error
cleanup() {
  echo "Returning to original directory..."
  cd "$ORIGINAL_DIR"
}
trap cleanup EXIT

# Exit on error
set -e

# Generate TypeScript library
echo "Generating TypeScript library..."
./scripts/codegen.sh typescript

# Navigate to output directory
echo "Building TypeScript package..."
cd support/output/typescript/

# Setup and build the package
touch yarn.lock
yarn install
yarn build

echo "TypeScript library successfully generated and built!"

echo "Copying TypeScript library to local project at $TARGET_DIR..."
cd $ORIGINAL_DIR
cp -r support/output/typescript/dist/* "$DEST_DIR"

echo "TypeScript library successfully copied to local project!"