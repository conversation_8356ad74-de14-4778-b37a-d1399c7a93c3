name: CI
on:
  push:
    branches:
      - "main"
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  ci:
    runs-on: ubuntu-latest
    outputs:
      typescript_verification_success: ${{ steps.verify_typescript_codegen.outcome == 'success' }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.12
      - uses: abatilo/actions-poetry@v2
        with:
          poetry-version: 1.8.3
      - uses: erlef/setup-beam@v1
        with:
          otp-version: 26.2
          elixir-version: 1.16.2

      - name: Run tests
        run: |
          cd support
          poetry install
          poetry run pytest
          poetry run make format-check

      - name: Validate schemas (PR)
        if: github.event_name == 'pull_request'
        run: |
          git fetch origin ${{ github.event.pull_request.base.ref }} --depth 1
          ./scripts/validate_schemas.sh
        env:
          BASE_REF: origin/${{ github.event.pull_request.base.ref }}
          FORCE_COLOR: true

      - name: Validate schemas (main)
        if: github.event_name != 'pull_request'
        run: |
          git fetch origin main --depth 1
          ./scripts/validate_schemas.sh
        env:
          BASE_REF: origin/main
          FORCE_COLOR: true

      - name: Run codegen
        run: |
          ./scripts/codegen.sh

          shopt -s dotglob
          mv support/output /tmp/output

      - name: Setup git config
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "event schema automation"
          git config --global credential.helper store
          echo "https://${GH_TOKEN}:<EMAIL>" >> ~/.git-credentials
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: Verify generated Elixir code
        id: verify_elixir_codegen
        continue-on-error: true
        run: |
          mkdir ~/.ssh
          echo "${MULTIVERSE_DEPLOYER_SSH_KEY_ENCODED}" > ~/.ssh/encoded_id_key
          base64 -d -i ~/.ssh/encoded_id_key > ~/.ssh/id_rsa
          chmod 0400 ~/.ssh/id_rsa
          eval $(ssh-agent)
          ssh-add ~/.ssh/id_rsa
          cd /tmp/output/elixir
          mix deps.get
          mix compile
        env:
          MULTIVERSE_DEPLOYER_SSH_KEY_ENCODED: ${{secrets.MULTIVERSE_DEPLOYER_SSH_KEY_ENCODED}}

      - name: Push Elixir codegen
        if: github.event_name != 'pull_request' && steps.verify_elixir_codegen.outcome == 'success'
        run: |
          git fetch origin elixir --depth 1
          git reset --hard origin/elixir
          git switch -C elixir

          rm -rf *
          cp -r /tmp/output/elixir/* .

          git_hash=$(git rev-parse --short "$GITHUB_SHA")

          git add -A

          if git diff --cached --quiet; then
            echo "no changes to commit"
            exit 0
          fi

          git commit -m "Updated schema from Multiverse-io/event-schemas@$git_hash"
          git push origin elixir

      - name: Verify generated Typescript code
        id: verify_typescript_codegen
        continue-on-error: true
        run: |
          corepack enable
          corepack prepare yarn@4.1.1 --activate
          cd /tmp/output/typescript
          # Required to generate the lockfile on CI
          yarn install --mode update-lockfile
          yarn install --immutable
          # Increase Node.js heap memory to avoid out-of-memory errors
          export NODE_OPTIONS="--max-old-space-size=8192"
          yarn build

      - name: Push Typescript codegen
        if: github.event_name != 'pull_request' && steps.verify_typescript_codegen.outcome == 'success'
        run: |
          git fetch origin typescript --depth 1
          git reset --hard origin/typescript
          git switch -C typescript

          rsync -a --delete --exclude='.github/' --exclude='.git/' --exclude='.yarnrc.yml' /tmp/output/typescript/ ./
          git_hash=$(git rev-parse --short "$GITHUB_SHA")

          git add -A

          if git diff --cached --quiet; then
            echo "no changes to commit"
            exit 0
          fi

          git commit -m "Updated schema from Multiverse-io/event-schemas@$git_hash"
          git push origin typescript

      - name: Push Python codegen
        if: github.event_name != 'pull_request'
        run: |
          git fetch origin python --depth 1
          git reset --hard origin/python
          git switch -C python

          rm -rf *
          cp -r /tmp/output/python/* .

          git_hash=$(git rev-parse --short "$GITHUB_SHA")

          git add -A

          if git diff --cached --quiet; then
            echo "no changes to commit"
            exit 0
          fi

          git commit -m "Updated schema from Multiverse-io/event-schemas@$git_hash"
          git push origin python

      - name: Push Python codegen (test schemas)
        if: github.event_name != 'pull_request'
        run: |
          git fetch origin python-test --depth 1
          git reset --hard origin/python-test
          git switch -C python-test

          rm -rf *
          cp -r /tmp/output/python-test/* .

          git_hash=$(git rev-parse --short "$GITHUB_SHA")

          git add -A

          if git diff --cached --quiet; then
            echo "no changes to commit"
            exit 0
          fi

          git commit -m "Updated schema from Multiverse-io/event-schemas@$git_hash"
          git push origin python-test

      - name: Push Backstage codegen
        if: github.event_name != 'pull_request'
        run: |
          git fetch origin backstage --depth 1
          git reset --hard origin/backstage
          git switch -C backstage

          rm -rf *
          cp -r /tmp/output/backstage/* .

          git_hash=$(git rev-parse --short "$GITHUB_SHA")

          git add -A

          if git diff --cached --quiet; then
            echo "no changes to commit"
            exit 0
          fi

          git commit -m "Updated schema from Multiverse-io/event-schemas@$git_hash"
          git push origin backstage

      - name: Check verification status
        if: steps.verify_elixir_codegen.outcome != 'success' || steps.verify_typescript_codegen.outcome != 'success'
        run: |
          echo "Generated code failed verification!"
          exit 1

  publish-typescript-lib:
    needs: ci
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && needs.ci.outputs.typescript_verification_success
    permissions:
      packages: write
      contents: read
    env:
      # Note: This is technically used to write in this case
      GITHUB_READ_PACKAGES_TOKEN: ${{secrets.GITHUB_TOKEN}}
    steps:
      - uses: actions/checkout@v4
        with:
          ref: typescript
      - uses: actions/setup-node@v4
        with:
          node-version: 20.10
          registry-url: https://npm.pkg.github.com/

      - name: Enable Corepack and Prepare Yarn
        run: |
          corepack enable
          corepack prepare yarn@4.1.1 --activate

      - name: Create yarn lockfile
        run: yarn install --mode update-lockfile

      - run: yarn install --immutable
      - name: Build typescript package
        run: |
          # Increase Node.js heap memory to avoid out-of-memory errors
          export NODE_OPTIONS="--max-old-space-size=8192"
          yarn build
      - run: yarn npm publish
        env:
          NODE_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}
