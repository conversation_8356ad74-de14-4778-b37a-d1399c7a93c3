# Events tooling Python [![CircleCI](https://dl.circleci.com/status-badge/img/gh/Multiverse-io/events_tooling_py/tree/main.svg?style=svg)](https://dl.circleci.com/status-badge/redirect/gh/Multiverse-io/events_tooling_py/tree/main)

For documentation on setting up, deploying, running the Events tooling Python locally and more, see the [Events tooling Python Tech Docs](https://reef.tech-tools.multiverse.io/docs/default/component/events_tooling_py) hosted in Reef. 

To edit the Events tooling Python docs, see our [Backstage documentation](https://reef.tech-tools.multiverse.io/docs/default/component/backstage/techdocs/) for guidance. The techdocs are in the `docs` folder in this repo.