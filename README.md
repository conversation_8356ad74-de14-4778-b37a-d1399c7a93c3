## Overview

This repository contains a test framework for event tooling. It aims to provide

- tools to write tests for the event tooling requirements
- a test for each event tooling requirement

## Development environment

### Setting up

```sh
asdf plugin-add python
asdf plugin-add poetry
asdf install

poetry install
```

### Formatting / linting / type-checking

```sh
poetry run ruff format
```

```sh
poetry run ruff check [--fix]
```

```sh
poetry run mypy
```

## Tests

### Running tests

The tests are slow but can safely be run concurrently (adjust the value of `n`
if you need to).

```sh
poetry run pytest -n 20
```

### How to write a requirement test

E.g. if you want to write the requirement test for P-03, create a file at
[src/event_tooling_tests/p03.py](./src/event_tooling_tests/p03.py). That on is
already implemented so can be used as a template

Then you should write one or more test objects for the requirement test. That
test object should connect to rabbitmq but "hard-code" the expected behaviour.

See for example

- [src/event_tooling_tests/tests/requirement_test_objects/req_p03_fail.py](./src/event_tooling_tests/tests/requirement_test_objects/req_p03_fail.py)
  - this test object is supposed to fail the requirement test
- [src/event_tooling_tests/tests/requirement_test_objects/req_p03_ok.py](./src/event_tooling_tests/tests/requirement_test_objects/req_p03_ok.py)
  - this test is supposed to pass the requirement test

### Testing report on requirement tests

```sh
python -m event_tooling_tests.tests
```

Note: it is possible to get a report for a single requirement test, e.g.

```sh
python -m event_tooling_tests.tests --req C-06
```
