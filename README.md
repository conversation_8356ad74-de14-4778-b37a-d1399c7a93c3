# Event schema registry

This is the Multiverse Event Schema Registry. It is the source of truth for event schemas
and contains both their definitions and code to support their usage.

For more in depth docs see the markdown files in the `docs` directory.

## Backwards Compatibility

By default, all schemas must maintain backwards compatibility when updated. However, there are two exceptions:

1. **V0 Schemas**: These are considered experimental/unstable. Breaking changes are allowed when incrementing the minor version (e.g., 0.1.0 -> 0.2.0).

2. **Explicitly Allowed Schemas**: Some schemas are explicitly allowed to have breaking changes. These are listed in `support/event_support/main.py` under `ALLOW_BREAKING_CHANGES_TO_SCHEMAS`. Only add schemas here in rare circumstances where:
   - No events have been (or will be) published under that schema version
   - Breaking changes are needed for a recently added schema
   - No consumers have been created using the old schema version

## Typescript package updates

To learn about the reasons for 'major' and 'minor' package updates, view git-history of [THIS](https://github.com/Multiverse-io/event-schemas/blob/a6a655a1c278500fccb0627c97afe6bb5cb5614e/support/event_support/generator/typescript.py#L26) line.
