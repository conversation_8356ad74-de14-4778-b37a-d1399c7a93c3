#!/usr/bin/env python3
"""
Script to check if generated models have the raw_json_schema attribute in their Meta class.
"""

import sys
import json
import importlib
from pathlib import Path

def check_schema(package_dir):
    """
    Check if the generated models have the raw_json_schema attribute in their Meta class.
    
    Args:
        package_dir: Path to the directory containing the generated package
    """
    # Add the package directory to the Python path
    sys.path.insert(0, str(package_dir))
    
    # Try to import a generated model
    try:
        # Find all Python files in the package directory
        python_files = list(Path(package_dir).glob("**/[!_]*.py"))
        
        if not python_files:
            print(f"No Python files found in {package_dir}")
            return
        
        # Print the number of Python files found
        print(f"Found {len(python_files)} Python files")
        
        # Try to import each Python file
        for python_file in python_files:
            # Get the module path
            rel_path = python_file.relative_to(package_dir)
            module_path = str(rel_path.with_suffix("")).replace("/", ".")
            
            try:
                # Import the module
                module = importlib.import_module(module_path)
                
                # Find all classes in the module
                for name in dir(module):
                    obj = getattr(module, name)
                    
                    # Check if the object is a class and has a Meta class
                    if isinstance(obj, type) and hasattr(obj, "Meta"):
                        print(f"\nFound class with Meta: {module_path}.{name}")
                        
                        # Check if the Meta class has the raw_json_schema attribute
                        if hasattr(obj.Meta, "raw_json_schema"):
                            print(f"  ✅ Has raw_json_schema attribute")
                            
                            # Try to parse the raw_json_schema as JSON
                            try:
                                schema = json.loads(obj.Meta.raw_json_schema)
                                print(f"  ✅ raw_json_schema is valid JSON")
                                print(f"  Schema ID: {schema.get('$id', 'N/A')}")
                                print(f"  Schema type: {schema.get('type', 'N/A')}")
                                print(f"  Properties: {list(schema.get('properties', {}).keys())}")
                            except json.JSONDecodeError:
                                print(f"  ❌ raw_json_schema is not valid JSON")
                        else:
                            print(f"  ❌ Does NOT have raw_json_schema attribute")
            except ImportError as e:
                print(f"Could not import {module_path}: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <package_dir>")
        sys.exit(1)
    
    package_dir = sys.argv[1]
    check_schema(package_dir)
